class Error < ApplicationRecord
  belongs_to :jersey, optional: true

  validates :type, presence: true
  validates :message, presence: true

  # Error type constants matching PHP application
  INVALID_STRING_START = "invalid_string_start"
  WORD_UNKNOWN = "word_unknown"
  NO_SEMANTICAL_MEANING = "no_semantical_meaning"
  PATTERN_NOT_RELATED_TO_ELEMENT = "pattern_not_related_to_element"
  GENERATE_SVG = "generate_svg"

  scope :by_type, ->(type) { where(type: type) }
  scope :for_jersey, ->(jersey) { where(jersey: jersey) }

  def to_s
    "#{type}: #{message}"
  end
end
