<?php

namespace DBLS\Bundle\JerseyColoursBundle\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Template;
use DBLS\Bundle\JerseyColoursBundle\Adapters\FileAdapter;

class DefaultController extends Controller {

    /**
     * @Route("/", name="homepage")
     * @Template("JerseyColoursBundle:Default:index.html.twig")
     */
    public function indexAction() {

        $em = $this->getDoctrine()->getManager();

        //======== Get Jersey
        $entitiesJersey = $em->getRepository('JerseyColoursBundle:Jersey')->findBy(
                array('parsed' => 1),                 // $where 
                array('id' => 'DESC'),  // $orderBy
                5,                    // $limit
                0                     // $offset
        );

        // Find All Count
        $countJersey = $em->createQueryBuilder()
                        ->select('COUNT(silk.id)')
                        ->from('JerseyColoursBundle:Jersey', 'silk')
                        ->where('silk.parsed = :parsed')
                        ->setParameter('parsed', 1)
                        ->getQuery()
                        ->getSingleScalarResult();

        //$countJersey = $em->getRepository('JerseyColoursBundle:Jersey')->findBy(array('parsed' => 1));

        //======== Get Pattern
        $entitiesPattern = $em->getRepository('JerseyColoursBundle:Pattern')->findBy(
                array(),                 // $where 
                array('id' => 'DESC'),  // $orderBy
                5,                    // $limit
                0                     // $offset
        );

        // Find All Count
        $countPattern = $em->getRepository('JerseyColoursBundle:Pattern')->findAll();
        
        return array(
                'entities_jersey' => $entitiesJersey,
                'count_jersey'    => $countJersey,
                'entities_pattern' => $entitiesPattern,
                'count_pattern'    => count($countPattern),
            );
    }
    
    

}
