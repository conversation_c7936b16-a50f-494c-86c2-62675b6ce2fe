<?php

namespace DBLS\Bundle\JerseyColoursBundle\StringParser;

/**
 * Description of Tokens
 *
 * <AUTHOR>
 */
class Tokens {
    const T_ELEMENT  = 'T_ELEMENT';
    const T_COLOUR   = 'T_COLOUR';
    const T_PATTERN  = 'T_PATTERN';
    const T_UNKOWN   = 'T_UNKOWN';
    const T_AND      = 'T_AND';
    const T_WITH     = 'T_WITH';
    const T_COLON    = 'T_COLON';
    const T_COMMA    = 'T_COMMA';
    
    const id_and     = 'and';
    const id_ampersand = '&';
    const id_with    = 'with';
    const id_colon   = ':';
    const id_comma   = ',';
    
    //-------------------------------------------------
    //
    //  Public statics
    //  
    //-------------------------------------------------
    
    /**
     * Split tokens.
     * 
     * @param array $tokens
     * @param string $type
     * @return array
     */
    public static function splitBy($tokens, $type) {
        $splitted = array();
        
        $currIdx = 0;
        foreach ($tokens as $token) {
            if ( $token['token'] == $type)
            {
                if ( count($splitted[$currIdx]) > 0 )
                {
                    $currIdx++;
                }
            }
            else
            {
                $splitted[$currIdx][] = $token;
            }
        }
        
        return $splitted;
    }
    
    /**
     * Chunk tokens up to $stopTokenType
     * 
     * @param type $tokens
     * @param type $stopTokenType
     * @return type
     */
    public static function getChunk(&$tokens, $stopTokenType) {
        $chunk = array();
        
        if ( count($tokens) <= 0 ) {
            return $chunk;
        }
        
        $tokenFound = false;
        while (count($tokens) > 0 && !$tokenFound) {
            $item = array_shift($tokens);
            if ( $item['token'] != $stopTokenType) {
                $chunk[] = $item;
            }
            else {
                $tokenFound = true;
            }
        }
        
        return $chunk;
    }
    
    
    public static function backToString($tokens) {
        $items = array();
        foreach ($tokens as $token) {
            if (!empty($token['original_term'])) {
                $items[] = trim($token['original_term']);
            } else {
                $items[] = trim($token['match']);
            }
        }
        return implode(' ', $items);
    }
    
    public static function getCommaToken() {
        return array(
            'token' => self::T_COMMA,
            'match' => ', '
        );
    }
    
    public static function getArrayOf($tokens, $field = 'match') {
        $items = array();
        foreach ($tokens as $token) {
            if (!empty($token[$field])) {
                $items[] = $token[$field];
            }
        }
        return $items;
    }
    
}
