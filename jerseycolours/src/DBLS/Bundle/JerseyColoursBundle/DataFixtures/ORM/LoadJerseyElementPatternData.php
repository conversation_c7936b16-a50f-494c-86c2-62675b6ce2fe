<?php

namespace DBLS\Bundle\JerseyColoursBundle\DataFixtures\ORM;

use Doctrine\Common\DataFixtures\AbstractFixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Common\Persistence\ObjectManager;
use DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElementPattern;

class LoadJerseyElementPatternData extends AbstractFixture implements OrderedFixtureInterface
{

    /**
     * {@inheritDoc}
     */
    public function load(ObjectManager $manager)
    {       
        
        $jerseyPatternThubiaan = new JerseyElementPattern();
        $jerseyPatternThubiaan->setPattern($this->getReference('pattern-core-star'));
        $jerseyPatternThubiaan->setJerseyElement($this->getReference('jersey-element-thubiaan-shirt'));
                
        $jerseyPatternWaltzing1 = new JerseyElementPattern();
        $jerseyPatternWaltzing1->setPattern($this->getReference('pattern-sleeve-checked'));
        $jerseyPatternWaltzing1->setJerseyElement($this->getReference('jersey-element-waltzing-sleeves'));
        
        $jerseyPatternWaltzing2 = new JerseyElementPattern();
        $jerseyPatternWaltzing2->setPattern($this->getReference('pattern-cap-quartered'));
        $jerseyPatternWaltzing2->setJerseyElement($this->getReference('jersey-element-waltzing-cap'));
        
        $jerseyPatternToo1 = new JerseyElementPattern();
        $jerseyPatternToo1->setPattern($this->getReference('pattern-core-circle'));
        $jerseyPatternToo1->setJerseyElement($this->getReference('jersey-element-toocool-shirt'));
        
        $jerseyPatternToo2 = new JerseyElementPattern();
        $jerseyPatternToo2->setPattern($this->getReference('pattern-sleeve-quartered'));
        $jerseyPatternToo2->setJerseyElement($this->getReference('jersey-element-toocool-sleeves'));
        
        $jerseyPatternVatican = new JerseyElementPattern();
        $jerseyPatternVatican->setPattern($this->getReference('pattern-core-star'));
        $jerseyPatternVatican->setJerseyElement($this->getReference('jersey-element-vatican-shirt'));

        $manager->persist($jerseyPatternThubiaan);
        $manager->persist($jerseyPatternWaltzing1);
        $manager->persist($jerseyPatternWaltzing2);
        $manager->persist($jerseyPatternToo1);
        $manager->persist($jerseyPatternToo2);
        $manager->persist($jerseyPatternVatican);
        $manager->flush();
        
        $this->addReference('jersey-element-pattern-thubiaan-shirt', $jerseyPatternThubiaan);
        $this->addReference('jersey-element-pattern-waltzing-sleeves', $jerseyPatternWaltzing1);
        $this->addReference('jersey-element-pattern-waltzing-cap', $jerseyPatternWaltzing2);
        $this->addReference('jersey-element-pattern-toocool-shirt', $jerseyPatternToo1);
        $this->addReference('jersey-element-pattern-toocool-sleeves', $jerseyPatternToo2);
        $this->addReference('jersey-element-pattern-vatican-shirt', $jerseyPatternVatican);
        
    }

    /**
     * {@inheritDoc}
     */
    public function getOrder()
    {
        return 9;
    }
    
}
