<?php

namespace DBLS\Bundle\JerseyColoursBundle\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Error
 *
 * @ORM\Table()
 * @ORM\Entity(repositoryClass="DBLS\Bundle\JerseyColoursBundle\Entity\ErrorRepository")
 */
class Error
{
    const WORD_UNKNOWN = 1;
    const NO_SEMANTICAL_MEANING = 2;
    const INVALID_STRING_START = 3;
    const PATTERN_NOT_RELATED_TO_ELEMENT = 4;
    const GENERATE_SVG = 5;
    
    /**
     * @var integer
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="type", type="integer", length=255)
     */
    private $type;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="date", type="datetime")
     */
    private $date;

    /**
     * @var string
     *
     * @ORM\Column(name="message", type="text")
     */
    private $message;

    /**
     * @var string
     *
     * @ORM\Column(name="details", type="text", nullable=true)
     */
    private $details;

    /**
     * @var string
     *
     * @ORM\Column(name="idAPI", type="string", length=255, nullable=true)
     */
    private $idAPI;

    /**
     * @ORM\ManyToOne(targetEntity="Jersey", inversedBy="errors")
     * @ORM\JoinColumn(name="jersey_id", referencedColumnName="id", onDelete="CASCADE")
     **/
    private $jersey;

    /**
     * @var boolean
     *
     * @ORM\Column(name="solved", type="boolean")
     */
    private $solved;
    
    /**
     * @var string
     *
     * @ORM\Column(name="expression", type="string", length=255, nullable=true)
     */
    private $expression;

    public function __construct()
    {
        $this->date = new \DateTime("now");
        $this->solved = false;
    }
    
    /**
     * Get id
     *
     * @return integer 
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set type
     *
     * @param string $type
     * @return Error
     */
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    /**
     * Get type
     *
     * @return string 
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Set date
     *
     * @param \DateTime $date
     * @return Error
     */
    public function setDate($date)
    {
        $this->date = $date;

        return $this;
    }

    /**
     * Get date
     *
     * @return \DateTime 
     */
    public function getDate()
    {
        return $this->date;
    }

    /**
     * Set message
     *
     * @param string $message
     * @return Error
     */
    public function setMessage($message)
    {
        $this->message = $message;

        return $this;
    }

    /**
     * Get message
     *
     * @return string 
     */
    public function getMessage()
    {
        return $this->message;
    }

    /**
     * Set expression
     *
     * @param string $expression
     * @return Error
     */
    public function setExpression($expression)
    {
        $this->expression = $expression;

        return $this;
    }

    /**
     * Get expression
     *
     * @return string 
     */
    public function getExpression()
    {
        return $this->expression;
    }

    /**
     * Set idAPI
     *
     * @param string $idAPI
     * @return Error
     */
    public function setIdAPI($idAPI)
    {
        $this->idAPI = $idAPI;

        return $this;
    }

    /**
     * Get idAPI
     *
     * @return string 
     */
    public function getIdAPI()
    {
        return $this->idAPI;
    }

    /**
     * Set jersey
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $jersey
     * @return Error
     */
    public function setJersey(\DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $jersey = null)
    {
        $this->jersey = $jersey;

        return $this;
    }

    /**
     * Get jersey
     *
     * @return \DBLS\Bundle\JerseyColoursBundle\Entity\Jersey 
     */
    public function getJersey()
    {
        return $this->jersey;
    }

    /**
     * Set solved
     *
     * @param boolean $solved
     * @return Jersey
     */
    public function setSolved($solved)
    {
        $this->solved = $solved;

        return $this;
    }

    /**
     * Get status
     *
     * @return boolean 
     */
    public function getSolved()
    {
        return $this->solved;
    }

    /**
     * Set details
     *
     * @param string $details
     * @return Error
     */
    public function setDetails($details)
    {
        $this->details = $details;

        return $this;
    }

    /**
     * Get details
     *
     * @return string 
     */
    public function getDetails()
    {
        return $this->details;
    }
}
