{% extends 'site.html.twig' %}

{% block page_header %}
    <div id="content-header">
        <h1>Pattern list</h1>
        <div class="btn-group">
            <a class="btn btn-large" href="{{ path('pattern_new') }}" title="New Pattern"><i class="fa fa-plus-circle"></i> New Pattern</a>
        </div>
    </div>
{% endblock page_header %}

{% block breadcrumb %}
    <div id="breadcrumb">
        <a href="{{ path('homepage') }}" class="tip-bottom" data-original-title="Go to Home"><i class="fa fa-home"></i> Home</a>
        <a href="{{ path('pattern') }}" class="current">Pattern list</a>
    </div>
{% endblock breadcrumb %}


{% block content %}
    <div class="row">
        <div class="col-xs-12">
            <div class="widget-box">
                <div class="widget-title">
                    <span class="icon">
                        <i class="fa fa-th"></i>
                    </span>
                    <h5>Pattern list</h5>
                </div>
                <div class="widget-content nopadding">
                    <table class="table table-bordered table-striped table-hover data-table auto-init">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th style="width: 35%">Description</th>
                                <th>Element</th>
                                <th>Preview</th>
                                <th>Status</th>
                                <th style="width: 20%" class="no-sort">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for entity in entities %}
                                <tr>
                                    <td>{{ entity.id }}</td>
                                    <td>{{ entity|title }}</td>
                                    <td>{{ entity.elementType.name|title }}</td>
                                    <td>
                                        <a class="btn btn-default btn-block" href="javascript:void(0);" onclick="JockerColour.Pattern.showPatternPreview('{{ asset(entity.getFrontViewPath|escape('js')) }}','{{ asset(entity.getSideViewPath|escape('js')) }}', '{{ entity.description|capitalize }}')">
                                            <img style="display:block; margin: 0 auto; max-height: 90px; max-width: 170px;" src="{{ asset(entity.getFrontViewPath) }}"/>
                                        </a>
                                    </td>
                                    <td><label class="label label{% if entity.status %}-success">Active{% else %}-danger">Inactive{% endif %}</label></td>
                                    <td>
                                        <a href="{{ path('pattern_edit', {'id': entity.id}) }}" class="btn btn-primary btn-xs"><i class="fa fa-pencil"></i> Edit</a>
                                        <a href="javascript:;" data-id="{{ entity.id }}" class="btn btn-danger btn-xs delete-item"><i class="fa fa-trash-o"></i> Delete</a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% endblock %}


{% block extrascripts %}
    {% javascripts 
	'@JerseyColoursBundle/Resources/public/js/bundle/pattern.js' %}
    <script type="text/javascript" src="{{ asset_url }}"></script>
    {% endjavascripts %}
{% endblock extrascripts %}