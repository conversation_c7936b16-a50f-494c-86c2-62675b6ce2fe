<?php

namespace DBLS\Bundle\JerseyColoursBundle\DataFixtures\ORM;

use Doctrine\Common\DataFixtures\AbstractFixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Common\Persistence\ObjectManager;
use DBLS\Bundle\JerseyColoursBundle\Entity\Race;

class LoadRaceData extends AbstractFixture implements OrderedFixtureInterface
{

    /**
     * {@inheritDoc}
     */
    public function load(ObjectManager $manager)
    {

        $raceOne = new Race();
        $raceOne->setMeeting($this->getReference('meeting-one'));
        $raceOne->setExternalRaceId("85f46a2f-ba83-4fdc-b42d-9b6bbcd7d2ed");
        $raceOne->setExternalMeetId("6e33a793-d3d6-45e8-a523-2997c86ba860");
        $raceOne->setName("Mill Park Stud");
        $raceOne->setNumber(1);
        $raceOne->setRacetime("2015-01-01 00:00:00");
        $raceOne->setApproved(0);

        $manager->persist($raceOne);
        $manager->flush();
        $this->addReference('race-one', $raceOne);
        
        

        $raceTwo = new Race();
        $raceTwo->setMeeting($this->getReference('meeting-one'));
        $raceTwo->setExternalRaceId("85f46a2f-ba83-4fdc-b42d-9b6bbcd7drsw");
        $raceTwo->setExternalMeetId("6e33a793-d3d6-45e8-a523-2997c86ba860");
        $raceTwo->setName("Teste Race Two");
        $raceTwo->setNumber(2);
        $raceTwo->setRacetime("2015-01-01 00:00:00");
        $raceTwo->setApproved(0);

        $manager->persist($raceTwo);
        $manager->flush();
        $this->addReference('race-two', $raceTwo);
    }

    /**
     * {@inheritDoc}
     */
    public function getOrder()
    {
        return 3;
    }
    
}
