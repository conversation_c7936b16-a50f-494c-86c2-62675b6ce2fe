<?php

namespace DBLS\Bundle\JerseyColoursBundle\Tests\Services;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use DBLS\Bundle\JerseyColoursBundle\Entity\Jersey;
use DBLS\Bundle\JerseyColoursBundle\Entity\Error;
use DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElement;
use DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElementPattern;
use \DBLS\Bundle\JerseyColoursBundle\Entity\JerseyPatternColour;

/**
 * The purpose of this calss it to test the JerseyGeneratorService
 * 
 * <AUTHOR>
 */
class JerseyGeneratorServiceTest extends KernelTestCase
{
    static $container;
    static $service;
    
    /**
     * @var \Doctrine\ORM\EntityManager
     */
    private $em;
    
    /**
     * {@inheritDoc}
     */
    public function setUp()
    {
        self::bootKernel();
        $this->em = static::$kernel->getContainer()
            ->get('doctrine')
            ->getManager()
        ;
    }    
    
    /**
     * {@inheritDoc}
     */
    protected function tearDown()
    {
        parent::tearDown();
        $this->em->close();
    }    
    
    public static function setUpBeforeClass()
    {
         //start the symfony kernel
         $kernel = static::createKernel();
         $kernel->boot();

         //get the DI container
         self::$container = $kernel->getContainer();

         self::$service = self::$container->get('services.jersey_generator');
    }
    
    public function testDummy() {
        $this->assertEquals(1,1);
    }
    
    public function xtestCreateJersey()
    {
        $em = $this->em;
        $meeting = $em->getRepository('JerseyColoursBundle:Meeting')->findOneBy(array('id' => 1));
        
        $colourWhite = $em->getRepository('JerseyColoursBundle:Colour')->findOneBy(array('name' => 'white'));
        $colourYellow = $em->getRepository('JerseyColoursBundle:Colour')->findOneBy(array('name' => 'yellow'));
        
        $patternStar = $em->getRepository('JerseyColoursBundle:Pattern')->findOneBy(array('description' => 'Star'));
        $patternSpots = $em->getRepository('JerseyColoursBundle:Pattern')->findOneBy(array('description' => 'Spots'));
        
        // creating the jersey
        $JerseyThubiaan = new Jersey();
        $JerseyThubiaan->setName("AdrTest_".date('H:i:s'));
        $JerseyThubiaan->setDescription("Description");
        $JerseyThubiaan->setStringValue("Red coolar");
        $JerseyThubiaan->setStatus(true);
        $JerseyThubiaan->setMeeting($meeting);
        
        // an error for the jersey
        $error = new Error();
        $error->setType("type");
        $error->setMessage("message");
        $error->setIdAPI("apiid");
        $error->setDate(new \DateTime());
        $JerseyThubiaan->getErrors()->add($error);
        
        $patternColour1 = new JerseyPatternColour();
        $patternColour1->setOrder(1);
        $patternColour1->setColour($colourWhite);
        
        $patternColour2 = new JerseyPatternColour();
        $patternColour2->setOrder(2);
        $patternColour2->setColour($colourWhite);

        $jerseyElementPattern = new JerseyElementPattern();
        $jerseyElementPattern->setPattern($patternStar);
        $jerseyElementPattern->getPatternColours()->add($patternColour1);
        $jerseyElementPattern->getPatternColours()->add($patternColour2);

        // create an element type
        $jerseyElement = new JerseyElement();
        $jerseyElement->setOrder(1);
        $jerseyElement->getJerseyPatterns()->add($jerseyElementPattern);
        
        // add element to the jersey
        $JerseyThubiaan->getElements()->add($jerseyElement);
		
        // have to pass in entity manager in order to test
        self::$service->createJersey($JerseyThubiaan);
		
        //-- asserts
        $entity = $em->getRepository('JerseyColoursBundle:Jersey')->findOneBy(array('id' => $JerseyThubiaan->getId()));
        $this->assertTrue($entity != null);
        $this->assertCount(1, $entity->getErrors());
        $this->assertCount(1, $entity->getElements());
        
        $elements = $entity->getElements();
        $element = $elements[0];
        
        $this->assertCount(1, $element->getJerseyPatterns());
        
        $jerseyPatterns = $element->getJerseyPatterns();
        $jerseyPattern = $jerseyPatterns[0];
        $this->assertCount(2, $jerseyPattern->getPatternColours());
    }    
    
}