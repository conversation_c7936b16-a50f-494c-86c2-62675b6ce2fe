<?php

namespace DBLS\Bundle\JerseyColoursBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Doctrine\Common\Collections\ArrayCollection;
use SamJ\DoctrineSluggableBundle\SluggableInterface;

/**
 * Jersey
 *
 * @ORM\Table()
 * @ORM\Entity(repositoryClass="DBLS\Bundle\JerseyColoursBundle\Entity\JerseyRepository")
 */
class Jersey implements SluggableInterface
{
    /**
     * @var integer
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=255)
     */
    private $name;

    /**
     * @var string
     *
     * @ORM\Column(name="description", type="string", length=255, nullable=true)
     */
    private $description;

    /**
     * @var string
     *
     * @ORM\Column(name="stringValue", type="text")
     */
    private $stringValue;

    /**
     * @var boolean
     *
     * @ORM\Column(name="status", type="boolean", options={"default":0})
     */
    private $status = false;

    /**
     * @var boolean
     *
     * @ORM\Column(name="parsed", type="boolean", options={"default":0})
     */
    private $parsed = false;

    /**
     * @var boolean
     *
     * @ORM\Column(name="parsing", type="boolean", options={"default":0})
     */
    private $parsing = false;

    /**
     * @ORM\ManyToOne(targetEntity="Race", inversedBy="jerseys")
     * @ORM\JoinColumn(name="race_id", referencedColumnName="id")
    */
    protected $race;

    /**
     * @ORM\ManyToOne(targetEntity="Trainer", inversedBy="jerseys")
     * @ORM\JoinColumn(name="trainer_id", referencedColumnName="id")
    */
    protected $trainer;

    /**
     * @ORM\OneToMany(targetEntity="JerseyElement", mappedBy="jersey")
    */
    protected $elements;

    /**
     * @ORM\OneToMany(targetEntity="Error", mappedBy="jersey")
     **/
   protected $errors;

   /**
     * @ORM\Column(type="string")
     */
    protected $slug;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="parsing_time", type="datetime", nullable=true)
     */
    private $parsingTime;

    /**
     * @var string
     *
     * @ORM\Column(name="externalJerseyId", type="string")
     */
    private $externalJerseyId;

    /**
     * @ORM\OneToMany(targetEntity="Jersey", mappedBy="copyFrom")
     **/
    private $copies;

    /**
     * @ORM\ManyToOne(targetEntity="Jersey", inversedBy="copies")
     * @ORM\JoinColumn(name="copyFrom_id", referencedColumnName="id")
     **/
    private $copyFrom;
    
    /**
     * @ORM\ManyToMany(targetEntity="Synonym", inversedBy="jerseys")
     * @ORM\JoinTable(name="jersey_synonym")
     **/
    private $synonyms;

    /**
     * @var integer
     *
     * @ORM\Column(name="number", type="integer", nullable=true)
     */
    private $number;

   public function __construct()
    {
        $this->elements = new ArrayCollection();
        $this->errors = new ArrayCollection();
        $this->synonyms = new ArrayCollection();
    }

    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set name
     *
     * @param string $name
     * @return Jersey
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set description
     *
     * @param string $description
     * @return Jersey
     */
    public function setDescription($description)
    {
        $this->description = $description;

        return $this;
    }

    /**
     * Get description
     *
     * @return string
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * Set stringValue
     *
     * @param string $stringValue
     * @return Jersey
     */
    public function setStringValue($stringValue)
    {
        $this->stringValue = $stringValue;

        return $this;
    }

    /**
     * Get stringValue
     *
     * @return string
     */
    public function getStringValue()
    {
        return $this->stringValue;
    }

    /**
     * Set status
     *
     * @param boolean $status
     * @return Jersey
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status
     *
     * @return boolean
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * Set status
     *
     * @param boolean $parsed
     * @return Jersey
     */
    public function setParsed($parsed)
    {
        $this->parsed = $parsed;

        return $this;
    }

    /**
     * Get parsed
     *
     * @return boolean
     */
    public function getParsed()
    {
        return $this->parsed;
    }

    /**
     * Get parsing
     *
     * @return boolean
     */
    public function getParsing()
    {
        return $this->parsing;
    }

    /**
     * Set status
     *
     * @param boolean $parsing
     * @return Jersey
     */
    public function setParsing($parsing)
    {
        $this->parsing = $parsing;

        return $this;
    }

    /**
     * Set parsingTime
     *
     * @param \DateTime $parsingTime
     * @return Meeting
     */
    public function setParsingTime($parsingTime)
    {
        $this->parsingTime = $parsingTime;

        return $this;
    }

    /**
     * Get parsingTime
     *
     * @return \DateTime
     */
    public function getParsingTime()
    {
        return $this->parsingTime;
    }

    /**
     * Add errors
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\error $errors
     * @return Jersey
     */
    public function addError(\DBLS\Bundle\JerseyColoursBundle\Entity\error $errors)
    {
        $this->errors[] = $errors;

        return $this;
    }

    /**
     * Remove errors
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\error $errors
     */
    public function removeError(\DBLS\Bundle\JerseyColoursBundle\Entity\error $errors)
    {
        $this->errors->removeElement($errors);
    }

    /**
     * Get errors
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getErrors()
    {
        return $this->errors;
    }

    /**
     *
     * @return boolean
     */
    public function hasError()
    {
        if( count($this->getErrors()) > 0 ){

            foreach( $this->getErrors() as $error ){
                if( !$error->getSolved() ){
                    return true;
                }
            }
            return false;
        }
        else{
            return false;
        }
    }

    /**
     * @return string
     */
    public function getFolderPath()
    {
        $racePath = $this->getRace()->getFolderPath();

        $jerseyFolder = $this->getSlug();
        //if ( !empty( $this->number ) )
        //    $jerseyFolder = implode('_', array($this->number, $jerseyFolder));

        $path = array($racePath, $jerseyFolder);

        if ( !$this->status )
            array_unshift($path, '../draft');

        return implode('/', $path);
    }

    /**
     *
     * @return string
     */
    public function getFrontViewImage($fallback = true)
    {
        $file = 'images/silks/' . $this->getFolderPath() . '/front.svg';

        if ($this->getStatus() == 1) {
            $file = $this->getApprovedPath('page-masters');
            $file .= $this->getSlug() . '_front.svg';
        }
        
        if ( !$fallback || ( file_exists( $file ) && filesize( $file ) > 0 ) )
            return $file;
        else
            return 'images/no-image.svg';
    }

    /**
     * 
     * @return string
     */
    public function getApprovedSvg()
    {
        $file = $this->getApprovedPath('page-masters');
        $file .= $this->getSlug() . '_side.svg';

        return realpath($file);   
    }

    /**
     * 
     * @return string
     */
    public function getApprovedJpeg()
    {
        $file = $this->getApprovedPath('aap');
        $file .= $this->getSlug() . '_side.jpg';

        return realpath($file);
    }

    /**
     *
     * @return string
     */
    public function getSideViewImage($fallback = true)
    {
        $file = 'images/silks/' . $this->getFolderPath() . '/side.svg';
        
        if ($this->getStatus() == 1) {
            $file = $this->getApprovedPath('page-masters');
            $file .= $this->getSlug() . '_side.svg';
        }

        if( !$fallback || ( file_exists( $file ) && filesize( $file ) > 0 ) )
            return $file;
        else
            return 'images/no-image.svg';
    }

    /**
     * 
     * @param  string $folder
     * @return string
     */
    public function getApprovedPath($folder = "page-masters")
    {
        $infos = array(
            'race_date'     => $this->getRace()->getMeeting()->getDate()->format('Y_m_d'),
            'meeting_name'  => $this->getRace()->getMeeting()->getCleanFolder(),
            'race_id'       => $this->getRace()->getNumber(),
            'race_name'     => $this->getRace()->getCleanFolder()
        );

        $path = sprintf("images/silks/%s/%s/%s/%s/",
                    $folder,
                    $infos['race_date'],
                    $infos['meeting_name'],
                    $infos['race_id'] . '_' . $infos['race_name']
                );

        return $path;
    }
    
    /**
     * Add elements
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElement $elements
     * @return Jersey
     */
    public function addElement(\DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElement $elements)
    {
        $this->elements[] = $elements;

        return $this;
    }

    /**
     * Remove elements
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElement $elements
     */
    public function removeElement(\DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElement $elements)
    {
        $this->elements->removeElement($elements);
    }

    /**
     * Get elements
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getElements()
    {
        return $this->elements;
    }

    /**
     *
     * @return string
     */
    public function getSlug()
    {
        return $this->slug;
    }

    /**
     * Set slug
     *
     * @param string $slug
     * @return Jersey
     */
    public function setSlug($slug)
    {
        //if (!empty($this->slug))
        //    return false;

        $this->slug = $slug;
        return $this;
    }

    /**
     *
     * @return string
     */
    public function getSlugFields()
    {
        return array($this->getNumber(), $this->getName());
    }

    /**
     * Set race
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Race $race
     * @return Jersey
     */
    public function setRace(\DBLS\Bundle\JerseyColoursBundle\Entity\Race $race = null)
    {
        $this->race = $race;

        return $this;
    }

    /**
     * Get race
     *
     * @return \DBLS\Bundle\JerseyColoursBundle\Entity\Race
     */
    public function getRace()
    {
        return $this->race;
    }

    /**
     * Set trainer
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Trainer $trainer
     * @return Jersey
     */
    public function setTrainer(\DBLS\Bundle\JerseyColoursBundle\Entity\Trainer $trainer = null)
    {
        $this->trainer = $trainer;

        return $this;
    }

    /**
     * Get trainer
     *
     * @return \DBLS\Bundle\JerseyColoursBundle\Entity\Trainer
     */
    public function getTrainer()
    {
        return $this->trainer;
    }    

    /**
     * Set externalJerseyId
     *
     * @param integer $externalJerseyId
     * @return Jersey
     */
    public function setExternalJerseyId($externalJerseyId)
    {
        $this->externalJerseyId = $externalJerseyId;

        return $this;
    }

    /**
     * Get externalJerseyId
     *
     * @return integer
     */
    public function getExternalJerseyId()
    {
        return $this->externalJerseyId;
    }

    /**
    * This method returns true if $jersey
    * has the same elements and colours of this current jersey
    *
    * @param Jersey $jersey
    * @return bool
    */
    public function isSameAs($jersey) {
        $isSame = strtolower(trim($this->getName())) == strtolower(trim($jersey->getName()));
        $isSame = $isSame && ($this->getElements()->count() == $jersey->getElements()->count());

        if ( $isSame && $jersey->getElements()->count() > 0 ) {
            foreach ($jersey->getElements() as $element) {
                $isSame = $isSame && $this->hasSameElement($element);
            }
        }

        return $isSame;
    }

    /**
    * Returns true if current jersey has the same element as $jerseyElement
    *
    * @param JerseyElement
    * @return bool
    */
    private function hasSameElement($jerseyElement) {
        $hasSame = true;

        $idElementType = $jerseyElement->getElementType()->getId();

        $mineElement = $this->getElements()->filter(
            function($entity) use ($idElementType) {
                return $entity->getElementType()->getId() == $idElementType;
            }
        )->first();

        $hasSame = $hasSame &&  ($mineElement !== false);

        // test if element colours are equal
        if ( $hasSame ) {
            // test if element colours are the same
            if ( ($jerseyElement->getColour() != null) && ($mineElement->getColour() != null) ) {
                $hasSame = ($jerseyElement->getColour()->getId()) == ($mineElement->getColour()->getId());
            }
            else {
                // if only one element has colour, we've got a divergence
                if ( ($jerseyElement->getColour() == null) xor ($mineElement->getColour() == null) ) {
                    $hasSame = false;
                }
            }
        }

        // test if element patterns are equal
        if ( $hasSame ) {
            // test if both elements have the same patterns and colours
            if ( (!$jerseyElement->getJerseyPatterns()->isEmpty()) && (!$mineElement->getJerseyPatterns()->isEmpty()) ) {
                $hasSame = $jerseyElement->getJerseyPatterns()->count() == $mineElement->getJerseyPatterns()->count();

                if ( $hasSame ) {
                    foreach($jerseyElement->getJerseyPatterns() as $jerseyPattern) {
                        $patternId = $jerseyPattern->getPattern()->getId();

                        $arrMinePattern = $mineElement->getJerseyPatterns()->filter(
                            function($entity) use ($patternId) {
                                return $entity->getPattern()->getId() == $patternId;
                            }
                        );

                        $hasSame = $hasSame && !$arrMinePattern->isEmpty();

                        // test pattern colours
                        if ( $hasSame ) {
                            $minePattern = $arrMinePattern->first();
                            if ( !($jerseyPattern->getPatternColours()->isEmpty()) && !($minePattern->getPatternColours()->isEmpty()) ) {
                                foreach($jerseyPattern->getPatternColours() as $jerseyPatternColour) {
                                    $colourId =  $jerseyPatternColour->getColour()->getId();

                                    $arrMinePatternColour = $minePattern->getPatternColours()->filter(
                                        function($entity) use($colourId) {
                                            return $entity->getColour()->getId() == $colourId;
                                        }
                                    );

                                    $hasSame = $hasSame && !$arrMinePatternColour->isEmpty();
                                }
                            }
                            else {
                                if ( $jerseyPattern->getPatternColours()->isEmpty() xor $minePattern->getPatternColours()->isEmpty() ) {
                                    $hasSame = false;
                                }
                            }
                        }
                    }
                }
            }
            else {
                // if only one element has patterns, we've got a divergence
                if ( ($jerseyElement->getJerseyPatterns()->isEmpty()) xor ($mineElement->getJerseyPatterns()->isEmpty()) ) {
                    $hasSame = false;
                }
            }
        }

        return $hasSame;
    }

    /**
    * This function generates a string representation of this jersey
    */
    public function generateString() {
        $elementBase = $this->getElements()->filter(
            function ($entity) {
               return $entity->getElementType()->getId() == ElementType::BASE;
            }
        )->first();

        // generate string for all other elements, excerpt for base
        $otherElementsStr = $this->generateStringForElements();

        $output = '';

        // TODO: Check if base elements can have patterns and colours
        if ( !empty($elementBase) && null !== $elementBase->getColour() )
        {
            $output = $elementBase->getColour()->getName();
            if ( $otherElementsStr ) {
                $output = sprintf("%s, %s", $output, $otherElementsStr);
            }
        }
        else
        {
            if ( $otherElementsStr ) {
                $output = $otherElementsStr;
            }
        }

        return strtolower($output);
    }

    /**
    * Returns string representation for all jersey's elements
    *
    * @return string
    */
    private function generateStringForElements() 
    {
        $elegibleElements = $this->getElements()->filter(
            function($entity) {
                return
                    $entity->getElementType()->getId() != ElementType::BASE &&
                    (
                        $entity->getColour() != null ||  !$entity->getJerseyPatterns()->isEmpty()
                    )
                ;
            }
        );

        if ( !$elegibleElements->isEmpty() ) {
            $arr = array();
            foreach ($elegibleElements as $element) {
                $arr[] = $this->generateStringForElement($element);
            }
            return implode(', ', $arr);
        }

        return '';
    }

    /**
    * Returns string representation for $element
    *
    * @return string
    */
    private function generateStringForElement($element) 
    {
        $str = $element->getElementType()->getName();

        if ( $element->getColour() ) {
            $elementColour = $element->getColour()->getName();
            $str = sprintf("%s %s", $elementColour, $str);
        }

        if ( !$element->getJerseyPatterns()->isEmpty() ) {
            $elementPatterns = $this->generateStringForPatterns($element->getJerseyPatterns());
            if ( $element->getColour() ) {
                $str = sprintf("%s with %s", $str, $elementPatterns);
            }
            else {
                $str = sprintf("%s %s", $elementPatterns, $str);
            }
        }

        return $str;
    }

    /**
    * Returns string representation for $patterns
    *
    * @return string
    */
    private function generateStringForPatterns($patterns) 
    {
        $arr = array();
        foreach($patterns as $pattern) {
            $arr[] = $this->generateStringForPattern($pattern);
        }
        return implode(", ", $arr);
    }

    /**
    * Returns string representation for $jerseyPattern
    *
    * @return string
    */
    private function generateStringForPattern($jerseyElementPattern) 
    {
        $output = $jerseyElementPattern->getPattern()->getDescription();

        if ( !$jerseyElementPattern->getPatternColours()->isEmpty() ) {
            $colours = '';
            $arrColours = array();
            foreach($jerseyElementPattern->getPatternColours() as $jerseyPatternColour) {
                $arrColours[] = $jerseyPatternColour->getColour()->getName();
            }
            $lastColour = array_pop($arrColours);
            if ( count($arrColours)  == 0 ) {
                $colours = $lastColour;
            } elseif ( count($arrColours)  == 1 ) {
                $colours = array_pop($arrColours) . ' and ' . $lastColour;
            }
            else {
                $colours = implode(", ", $arrColours) . ' and ' . $lastColour;
            }

            $output = sprintf("%s %s", $colours, $output);
        }

       return $output;
    }


    /**
     * Add copies
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $copies
     * @return Jersey
     */
    public function addCopy(\DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $copies)
    {
        $this->copies[] = $copies;

        return $this;
    }

    /**
     * Remove copies
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $copies
     */
    public function removeCopy(\DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $copies)
    {
        $this->copies->removeElement($copies);
    }

    /**
     * Get copies
     *
     * @return \Doctrine\Common\Collections\Collection 
     */
    public function getCopies()
    {
        return $this->copies;
    }

    /**
     * Set copyFrom
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $copyFrom
     * @return Jersey
     */
    public function setCopyFrom(\DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $copyFrom = null)
    {
        $this->copyFrom = $copyFrom;

        return $this;
    }

    /**
     * Get copyFrom
     *
     * @return \DBLS\Bundle\JerseyColoursBundle\Entity\Jersey 
     */
    public function getCopyFrom()
    {
        return $this->copyFrom;
    }

    /**
     * Add synonyms
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Synonym $synonyms
     * @return Jersey
     */
    public function addSynonym(\DBLS\Bundle\JerseyColoursBundle\Entity\Synonym $synonyms)
    {
        $this->synonyms[] = $synonyms;

        return $this;
    }

    /**
     * Remove synonyms
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Synonym $synonyms
     */
    public function removeSynonym(\DBLS\Bundle\JerseyColoursBundle\Entity\Synonym $synonyms)
    {
        $this->synonyms->removeElement($synonyms);
    }

    /**
     * Get synonyms
     *
     * @return \Doctrine\Common\Collections\Collection 
     */
    public function getSynonyms()
    {
        return $this->synonyms;
    }

    /**
     * Set number
     *
     * @param integer $number
     * @return Race
     */
    public function setNumber($number)
    {
        $this->number = $number;

        return $this;
    }

    /**
     * Get number
     *
     * @return integer 
     */
    public function getNumber()
    {
        return $this->number;
    }
}
