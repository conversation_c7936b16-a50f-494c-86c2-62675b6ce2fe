<?php

namespace DBLS\Bundle\JerseyColoursBundle\StringParser;

use DB<PERSON>\Bundle\JerseyColoursBundle\Entity\JerseyPatternColour;
use DB<PERSON>\Bundle\JerseyColoursBundle\Entity\Jersey;
use DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElement;
use DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElementPattern;
use DBLS\Bundle\JerseyColoursBundle\Entity\Error;

/**
 * The purpose of this class to break a string into pieces of information
 * so that information can be used to create a Jersey Entity
 *
 * <AUTHOR>
 */
class StringParser {

    /**
     *
     * @var \Doctrine\ORM\EntityManager
     */
    private $em;

    /**
     *
     * @var string
     */
    private $string;

    /**
     *
     * @var array
     */
    private $allElements;

    /**
     *
     * @var Lexer
     */
    private $lexer;

    /**
     *
     * @var Parser
     */
    private $parser;

    /**
     * @var Jersey
     */
    private $jersey = null;

    /**
     *
     * @var IStringParserHelper
     */
    private $helper = null;
    
    //-------------------------------------------------
    //
    //  Public methods
    //
    //-------------------------------------------------


    /**
     * Constructor
     */
    public function __construct(\Doctrine\ORM\EntityManager $em, IPspellQueryable $dictionary) {
        $this->em = $em;
        $this->helper = new StringParserHelper($this->em);
        $this->lexer = new Lexer($dictionary);
        $this->parser = new GrammarParser($this->lexer, $this->helper);
        $this->allElements = array();
    }

    /**
     * @param Jersey $jersey
     */
    public function setJersey( Jersey $jersey )
    {
        $this->jersey = $jersey;
    }

    /**
     * @return Jersey
     */
    public function getJersey()
    {
        if ( null === $this->jersey )
            $this->jersey = new Jersey();

        return $this->jersey;
    }

    /**
     *
     * @param array $string
     */
    public function createJerseyFromString($string) {
        $this->string = $string;

        $this->lexer->tokenize($this->_cleanUp($string));
        $tokens = $this->lexer->getTokens();

        // sequence up to the first comma is the base color
        $firstChunck = Tokens::getChunk($tokens, Tokens::T_COMMA);
        $isColor = $this->parser->parseBase($firstChunck);

        if ( !$isColor ) {
            $count = count($firstChunck);
            if ($count > 0) {
                array_unshift($tokens, Tokens::getCommaToken());
            }
            for( $i=$count-1 ; $i>=0 ; $i-- ) {
                array_unshift($tokens, $firstChunck[$i]);
            }
        }
        
        // parse remaining tokens
        $this->parser->parse($tokens);

        // BEGIN -- debug
//        echo "<pre>";
//        echo sprintf("String: %s\n", $string);
//        echo "-----------------------------------\n";
//        print_r($this->parser);
//        exit();
        // END -- debug


        return $this->_createJerseyEntity();
    }

    /*
    * Defines the Levenshtein ratio
    */
    public function setLevenshteinRatio($value) {
        $this->lexer->setLevenshteinRatio($value);
    }

    /**
     *
     * @param array $array
     */
    public function setKnownColours($array) {
        $this->lexer->setKnownColours($array);
    }

    /**
     *
     * @param array $array
     */
    public function setKnownPatterns($array, $patternsPerElement) {
        $this->lexer->setKnownPatterns($array, $patternsPerElement);
    }

    /**
     *
     * @param array $array
     */
    public function setKnownElements($array) {
        $this->lexer->setKnownElements($array);
        $this->allElements = $array;
    }
    
    /**
     * Set synonyms
     * 
     * @param type $array
     */
    public function setSynonyms($array) {
        $this->lexer->setSynonyms($array);
    }


    //-------------------------------------------------
    //
    //  Private methods
    //
    //-------------------------------------------------

    /**
     * Makes a clean up in the string to be parsed
     *
     * @param string $string
     * @return string
     */
    private function _cleanUp($string) {
        $string = trim($string);

        $colonPos = strpos($string, ':');
        if ( false !==  $colonPos ) {
            $string = substr_replace($string, ' ', $colonPos, 0);
        }
        
        return preg_replace('/\s{2,}/', ' ', $string);
    }

    /**
    * This method removes all elements from $jersey'
    */
    public function _cleanEntity($jersey) {

        if ( !$jersey->getElements()->isEmpty() ) {
            //-- remove associated elements
            foreach($jersey->getElements() as $jerseyElement) {

                //-- remove associated patterns
                if ( !$jerseyElement->getJerseyPatterns()->isEmpty() ) {
                    foreach($jerseyElement->getJerseyPatterns() as $jerseyPattern) {

                        //-- remove associated pattern colours
                        if ( !$jerseyPattern->getPatternColours()->isEmpty() ) {
                            foreach($jerseyPattern->getPatternColours() as $patternColour) {
                                $jerseyPattern->getPatternColours()->removeElement($patternColour);
                                $jerseyPattern->getPatternColours()->clear();
                                $this->em->remove($patternColour);
                            }
                        }

                        $jerseyElement->getJerseyPatterns()->removeElement($jerseyPattern);
                        $jerseyElement->getJerseyPatterns()->clear();
                        $this->em->remove($jerseyPattern);
                    }
                }

                $jersey->getElements()->removeElement($jerseyElement);
                $jersey->getElements()->clear();
                $this->em->remove($jerseyElement);
            }
        }

        return $jersey;
    }

    /**
     * Returns a JerseyEntity based on parser results
     *
     * @return DBLS\Bundle\JerseyColoursBundle\Entity\Jersey
     */
    private function _createJerseyEntity()
    {
        $jersey = $this->_cleanEntity($this->getJersey());
        //$jersey->setStringValue($this->string);
        //$jersey->setName($this->parser->getJerseyName());

        $idx_element = 1;
        $elementsFound = array();
        if ( $this->parser->getElements() ) {
            foreach ($this->parser->getElements() as $element) {
                $newElement = new JerseyElement();
                $newElement->setOrder($idx_element++);

                // find the element type
                $elementType = $this->em->getRepository('JerseyColoursBundle:ElementType')->findByName($element[Parser::S_ELEMENT_NAME]);
                $newElement->setElementType($elementType);
                $elementsFound[] = trim(strtolower($elementType->getName()));

                // if there's related colour
                if ( array_key_exists(Parser::S_COLOUR, $element) && $element[Parser::S_COLOUR] != null) {
                    $colour = $this->em->getRepository('JerseyColoursBundle:Colour')->findByName($element[Parser::S_COLOUR]);
                    $newElement->setColour($colour);
                }

                // if there's related patterns
                if ( array_key_exists(Parser::S_PATTERNS, $element) && $element[Parser::S_PATTERNS] != null) {
                    foreach ($element[Parser::S_PATTERNS] as $patternArr) {
                        $pattern = $this->em->getRepository('JerseyColoursBundle:Pattern')->findByDescriptionAndElement($patternArr[Parser::S_DESCRIPTION], $elementType->getName());

                        $jerseyElementPattern = new JerseyElementPattern();
                        $jerseyElementPattern->setJerseyElement($newElement);
                        $jerseyElementPattern->setPattern($pattern);

                        if ( array_key_exists(Parser::S_COLOURS, $patternArr) && $patternArr[Parser::S_COLOURS] != null ) {
                            $idx = 1;
                            foreach ($patternArr[Parser::S_COLOURS] as $colourName) {
                                $colour = $this->em->getRepository('JerseyColoursBundle:Colour')->findByName($colourName);
                                $patternColour = new JerseyPatternColour();
                                $patternColour->setColour($colour);
                                $patternColour->setOrder($idx);
                                $patternColour->setJerseyElementPattern($jerseyElementPattern);
                                $jerseyElementPattern->getPatternColours()->add($patternColour);
                                $idx++;
                            }
                        }

                        $newElement->getJerseyPatterns()->add($jerseyElementPattern);
                    }
                }

                $jersey->getElements()->add($newElement);
            }
        }

        // add the other elements not found in the string
        foreach($this->allElements as $elementName) {
            if ( !in_array($elementName, $elementsFound) ) {
                $elementType = $this->em->getRepository('JerseyColoursBundle:ElementType')->findByName($elementName);

                $newElement = new JerseyElement();
                $newElement->setOrder($idx_element++);
                $newElement->setElementType($elementType);
                $jersey->getElements()->add($newElement);
            }
        }

        // add to the jersey the errors found. Important. It has to be the last procdure.
        if ( $this->parser->getErrors() ) {
            foreach($this->parser->getErrors() as $error) {
                $objError = new Error();
                $objError->setIdAPI('--');
                $objError->setType($this->_parserErrorToJerseyError($error['type']));
                $objError->setMessage($error['message']);
                $objError->setExpression($error['expression']);
                $objError->setDetails($this->getErrorDetails());
                $jersey->getErrors()->add($objError);
            }
        }

        return $jersey;
    }

    /**
     * Map parser errors to jersey errors type
     *
     * @param string $parseError
     * @return int
     */
    public function _parserErrorToJerseyError($parseError) {
        $map = array(
            Parser::E_INVALID_STRING_START => Error::INVALID_STRING_START,
            Parser::E_TOKEN_UNKNOWN => Error::WORD_UNKNOWN,
            Parser::E_NO_SEMANTICAL_MEANING => Error::NO_SEMANTICAL_MEANING,
            Parser::E_PATTERN_NOT_RELATED_TO_ELEMENT => Error::PATTERN_NOT_RELATED_TO_ELEMENT
        );

        return $map[$parseError];
    }
    
    /**
     * This method returns output from lexer and parser.
     */
    public function getErrorDetails() {
        $lexerOutput = print_r($this->lexer->getTokens(), true);
        $parserOutput = print_r($this->parser->getElements(), true);
        
        return sprintf(
                "Original String: %s\n\n" .
                "Lexer Output:\n" .
                "-----------------------\n" .
                "%s\n" .
                "-----------------------\n\n" .
                "Parser Output:\n" . 
                "-----------------------\n" .
                "%s\n" .
                "-----------------------\n",
                
                $this->string,
                $lexerOutput,
                $parserOutput
        );
    }
}
