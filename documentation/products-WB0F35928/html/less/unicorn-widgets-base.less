/* General widget styles */
// General widget style
.widget-box {
    background: none repeat scroll 0 0 #fafafa;
    border: 1px solid #CDCDCD;
    clear: both;
    margin-top: 16px;
    margin-bottom: 16px;
    position: relative;
    border-radius: 6px;
    .box-shadow(0 1px 3px rgba(0,0,0,0.1));

	&.widget-calendar, &.widget-chat {
    	overflow:hidden !important;
	}

	&.widget-plain {
		background: transparent;
		border: 0;
		margin-top: 0;
		margin-bottom: 0;
		.box-shadow(none);

		.widget-content {
			padding: 12px 0 0 !important;
		}
	}
	// Box title
	.widget-title {
		a {
			color: @gray;
		}

		span.icon {
			border-right: 1px solid #cdcdcd;
			padding: 8px 10px 8px 11px;
			float: left;
			.opacity(0.7);
			.box-shadow(1px 0 0 0 #ffffff);

			&.pull-right {
				border-right: 0;
				border-left: 1px solid #ffffff;
				.box-shadow(-1px 0 0 #cdcdcd);
			}
			&.with-checkbox {
				padding-bottom: 2px;
			}
		}

		h5 {
			color: #666666;
			text-shadow: 0 1px 0 #ffffff;
		    float: left;
		    font-size: 12px;
		    font-weight: bold;
		    padding: 12px;
		    line-height: 12px;
		    margin: 0;
		}
		// Tabs in box titlebar
		.nav-tabs {
    		border-bottom: 0 none;

			li {
				margin-bottom: 0;

				a {
				    border-bottom: medium none !important;
				    border-left: 1px solid #DDDDDD;
				    border-radius: 0;
				    border-right: 1px solid #DDDDDD;
				    border-top: medium none;
				    color: #999999;
				    margin: 0;
				    outline: medium none;
				    padding: 9px 10px 8px;
				    font-weight: bold;
				    text-shadow: 0 1px 0 #FFFFFF;

				    &:hover {
					    background-color: transparent;
					    border-color: #D6D6D6;
					    border-width: 0 1px;
					    color: #666666;
					}

					[class*="icon-"] {
						margin-right: 3px;
					}
				}
				&.active {
					a, a:hover,	a:focus {
						background-color: #F9F9F9 !important;
					    color: #444444;
					    border-top: 0;
					}
				}

				&:first-child a {
				    border-left: medium none !important;
				    border-top-left-radius: 5px;
				}
			}

			&.pull-right li {
				&:first-child a {
					border-top-left-radius: 0;
					border-left: 1px solid #dddddd !important;
				}

				&:last-child a {
					border-right: 0 !important;
					border-top-right-radius: 5px;
				}
			}
		}
		// Buttons in box titlebar
		.buttons {
			float: right;
			margin: 0 0 0 0;

			.btn {
				padding: 9px 10px 6px;
				color: #555555;
				border-radius: 0;
				border-left: 1px solid #dddddd;
				text-shadow: 0 1px 0 #ffffff;
				font-size: 12px;
				line-height: 17px;
				margin: 0;
				.box-shadow(1px 0 0 0 rgba(255,255,255,0.8) inset);

				[class*="icon-"] {
					vertical-align: text-top;
					margin-right: 3px;
				}

				&:last-child {
					border-radius: 0 6px 0 0;
				}

				&:hover, &:active {
					background-color: #fdfdfd;
					border-width: 0 1px;
					border-color: transparent transparent transparent #dddddd; 
					padding: 9px 10px 6px;
					line-height: 19px;
					.box-shadow(1px 1px 0 0 #ffffff inset);
					#gradient > .vertical(#f8f4fc, #f4f1f7, 0%, 100%);
				}

				&:active {
					.box-shadow(0 1px 3px rgba(0,0,0,0.1) inset);
				}
			}
		}
		// Labels and badges in box titlebar
		.label, .badge {
			padding: 3px 5px 2px;
			float: right;
			margin: 9px 15px 0 0;
			.box-shadow(~"0 1px 2px rgba(0,0,0,0.3) inset, 0 1px 0 #ffffff");
			.widget-calendar & {
				margin-right: 190px;
			}
		}
	}
	// Widget content area
	.widget-content {
		padding: 12px 15px;

		> hr {
			margin: 20px -15px;
			height: 0px;
			border-width: 1px 0;
			border-style: solid none;
			border-color: #eeeeee transparent #fff;
		}
	}

	&.collapsible .collapse.in .widget-content {
	    border-bottom: 1px solid #CDCDCD;
	}

	.flat & {
		.box-shadow(none);
		border-radius: 0;
	}
	@media (max-width: @screen-xs-max) {
		.widget-title .buttons {
	    	float: left;
	    	border-right: 1px solid rgba(255,255,255,0.7);
	    	> .btn {
				white-space: nowrap;
				overflow: hidden;
				padding: 10px 3px 6px 10px !important;
				&:last-child  {
					padding: 9px 6px 6px 10px !important;
					border-right: 1px solid #dddddd;
				}
			}
			
	 		.text, .go-full-screen {
				display: none;
			}
		}
	}
	&.widget-full-screen {
		position: fixed;
		left: 0;
		top: -16px;
		z-index: 1020;
		.square(100%);
		
		&, .widget-title {
			border-radius: 0;
		}
	}
}
.panel-left {
	margin-right: 250px;
	@media (max-width: 700px) {
		margin-right: 0;
	}
}
.panel-right {
	width: 249px;
	background-color: #f2f2f2;
	border-left: 1px solid #dddddd;
	position: absolute;
	right: 0;
	top: 36px;
	height: 2500px;

	.panel-title {
		width: 100%;
		background-color: #ececec;
		border-bottom: 1px solid #dddddd;

		h5 {
			font-size: 12px;
			color: #777777;
			text-shadow: 0 1px 0 #ffffff;
			padding: 6px 10px 5px;
		    margin: 0;

			.panel-content {
				padding: 10px;
			}
		}
	}
	@media (max-width: 700px) {
		border-top: 1px solid #DDDDDD;
		border-left: none;
		position: relative;
		top: auto;
		right: auto;
		height: auto;
		width: auto;
	}
}


.widget-title, .modal-header, .table th, div.dataTables_wrapper .ui-widget-header, .ui-dialog .ui-dialog-titlebar {
	border-bottom: 1px solid #CDCDCD;
    height: 36px;
    .box-shadow(0 1px 0 #ffffff inset);
    .border-top-radius(6px);
    .border-bottom-radius(0);
}
div.dataTables_wrapper .ui-widget-header {
	background-color: #f2f2f2;
}
.ui-widget-overlay {
	position: fixed;
}