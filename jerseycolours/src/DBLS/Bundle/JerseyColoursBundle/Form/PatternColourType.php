<?php

namespace DBLS\Bundle\JerseyColoursBundle\Form;

use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolverInterface;

use Symfony\Component\Form\Form;
use DBLS\Bundle\JerseyColoursBundle\Entity\ColourRepository;

class PatternColourType extends AbstractType
{
    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder->add('order', 'hidden');

        $builder->addEventListener(FormEvents::PRE_SET_DATA, 
            function(FormEvent $event) use ($builder) 
            {
                $jerseyPatternColour = $event->getData();

                $form = $event->getForm();
                
                if ( $jerseyPatternColour && $jerseyPatternColour->getColour() )
                    $id = $jerseyPatternColour->getColour()->getId();
                else
                    $id = 0;

                $this->_addColourField($form, $id);
            }
        );

        $builder->addEventListener(
            FormEvents::PRE_SUBMIT,
            function (FormEvent $event) 
            {
                $data = $event->getData();
                $form = $event->getForm();

                $id = empty($data['colour']) ? 0 : $data['colour'];
                $this->_addColourField($form, $id );
            }
        );
    }

    /**
     * 
     * @param Form   $form
     * @param int $colour_id
     */
    protected function _addColourField(Form $form, $colour_id)
    {
        // add colour
        $form->add(
                'colour',
                'entity',
                array(
                    'class'       => 'JerseyColoursBundle:Colour',
                    'attr'        => array('class' => 'color-select'),
                    'required'    => false,
                    'empty_data'  => null,
                    'query_builder' => function(ColourRepository $colourRepository) use ($colour_id) {
                        $qb = $colourRepository->createQueryBuilder('c');
                        $qb->where('c.id = :id')
                            ->setParameter('id', $colour_id);
                        return $qb;
                    }
                )
        );
    }

    /**
     * @param OptionsResolverInterface $resolver
     */
    public function setDefaultOptions(OptionsResolverInterface $resolver)
    {
        $resolver->setDefaults(array(
            'data_class' => 'DBLS\Bundle\JerseyColoursBundle\Entity\JerseyPatternColour'
        ));
    }

    /**
     * @return string
     */
    public function getName()
    {
        return 'dbls_bundle_jerseycoloursbundle_element_type';
    }
}
