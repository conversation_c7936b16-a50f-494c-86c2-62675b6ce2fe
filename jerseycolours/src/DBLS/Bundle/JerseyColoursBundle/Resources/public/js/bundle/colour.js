JockerColour = window.JockerColour || {};

JockerColour.Colour = {
    
    init: function()
    {
    	var form  = $( 'form' );
    	submit = function()
    	{
    	    var obj = {
    		callback: function( response )
    		{
    		    if ( response.status ) {
    			General.waitTo( 
    			    function()
    			    {
    				General.go( Routing.generate('colour') );
    			    },
    			    3
    			);
                        } else {
                            // form has errors.. So, display them
                            Form.addErrorsMessages( $( 'form' ), response.description.children );
                        }
                        
    		}
    	    };

    	    Form.submitAjax( form, obj );
    	    return false;
    	};
        
    	//Form.addValidate( form, submit );
    	this.configDelete();
            
        // Set input masks
        $("input[data-inputmask-regex]").inputmask('Regex');
        $('.colorpicker').colorpicker();
    },
    
    configDelete: function()
    {
	$(document).on('click', 'a.delete-item',
	    function()
	    {
		var id = $(this).data('id');
		
		bootbox.confirm("Are you sure you want to delete it?", 
		    function(result) 
		    {
                        
			if ( !result ) return true;
			
                        Form.showLoading();
                        
			$.post( Routing.generate('colour_delete', {id: id}))
				.done(
				    function() 
				    {
					Message.msgSuccess('Item was deleted.');
					location.reload(true);
				    })
				.fail(
				    function() 
				    {
					Message.msgError('Item could not be deleted.');
				    }
				)
                                .always(
                                    function()
                                    {
                                        Form.hideLoading();
                                    }                                    
                                );
		    }
		); 
	    }
	);
    }
};

$( document ).ready(
    function()
    {
	JockerColour.Colour.init();
    }
);