<?php

namespace DBLS\Bundle\JerseyColoursBundle\StringParser;

/**
 *
 * <AUTHOR>
 */
abstract class Parser {

    // error constants
    const E_TOKEN_UNKNOWN                  = 'E_TOKEN_UNKNOWN';
    const E_NO_SEMANTICAL_MEANING          = 'E_NO_SEMANTICAL_MEANING';
    const E_INVALID_STRING_START           = 'E_INVALID_STRING_START';
    const E_PATTERN_NOT_RELATED_TO_ELEMENT = 'E_PATTERN_NOT_RELATED_TO_ELEMENT';
    
    // elements contants
    const S_ELEMENT_NAME   = 'element_name';
    const S_PATTERNS       = 'patterns';
    const S_COLOURS        = 'colours';
    const S_COLOUR         = 'colour';
    const S_DESCRIPTION    = 'description';
    
    const ELEMENT_SHIRT = 'shirt';
    
    /**
     *
     * @var Lexer 
     */
    protected $lexer;
    
    /**
     *
     * @var string 
     */
    protected $jerseyName;
    
    /**
     *
     * @var array 
     */
    protected $elementsFound;
    
    /**
     *
     * @var array
     */
    protected $errors;
    
    //-------------------------------------------------
    //
    //  Abstract methods
    //  
    //-------------------------------------------------
    
    abstract public function parse($tokens);
    abstract public function parseBase($items);
    
    //-------------------------------------------------
    //
    //  Public methods
    //  
    //-------------------------------------------------
    
    /**
     * Constructor
     */
    public function __construct(Lexer $pLexer) {
        $this->elementsFound = array();
        $this->errors = array();
        $this->jerseyName = "";
        $this->lexer = $pLexer;
    }
    
    /**
     * Return all elements found
     * 
     * @return array
     */
    public function getElements() {
        return $this->elementsFound;
    }
    
    /**
     * This method return an element based on $elementName.
     * 
     * @param string $elementName
     */
    public function findElement($elementName) {
        if ($this->getElements()) {
            foreach($this->getElements() as $element) {
                if (isset($element[self::S_ELEMENT_NAME]) && strtolower(trim($element[self::S_ELEMENT_NAME])) == strtolower(trim($elementName))) {
                    return $element;
                }
            }
        }
        return null;
    }
    
    /**
     * Return all errors
     * 
     * @return array
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * Returns jersey name
     * 
     * @return string
     */
    public function getJerseyName() {
        return $this->jerseyName;
    }
    
    /**
     * Include new parser error
     * 
     * @param string $errorType
     * @param string $errorMessage
     */
    public function includeError($errorType, $errorMessage, $errorExpression) {
        $this->_addError(array('type' => $errorType, 'message' => $errorMessage, 'expression' => $errorExpression));
    }
    
    /**
     * Returns the original term of $term.
     * 
     * @param string $term
     * @return string
     */
    public function getOriginalTerm($term) {
        return $this->lexer->getOriginalTerm($term);
    }
    
    //-------------------------------------------------
    //
    //  Protected methods
    //  
    //-------------------------------------------------
    
    /**
     * Sets jersey name
     * 
     * @param string $name
     */
    protected function _setJerseyName($name) {
        $this->jerseyName = $name;
    }
    
    /**
     * Adds new element found
     * 
     * @param array $element
     */
    protected function _addElement($element) {
        $this->elementsFound[] = $element;
    }
    
    /**
     * Adds new error
     * @param array $error
     */
    protected function _addError($error) {
        $this->errors[] = $error;
    }
}
