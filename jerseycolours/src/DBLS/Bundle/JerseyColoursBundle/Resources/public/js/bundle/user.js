JockerColour = window.JockerColour || {};

JockerColour.User = {
    
    init: function()
    {
	var form  = $( 'form' );
	submit = function()
	{
	    var obj = {
		callback: function( response )
		{
		    if ( response.status ) {
			General.waitTo( 
			    function()
			    {
				General.go( Routing.generate('user') );
			    },
			    3
			);
                    } else {
                        // form has errors.. So, display them
                        Form.addErrorsMessages( $( 'form' ), response.description.children );
                    }
                    
		}
	    };

	    Form.submitAjax( form, obj );
	    return false;
	};
    
	//Form.addValidate( form, submit );
    }
};

$( document ).ready(
    function()
    {
	JockerColour.User.init();
    }
);