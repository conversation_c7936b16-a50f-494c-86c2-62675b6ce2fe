<?php

namespace DBLS\Bundle\JerseyColoursBundle\Services;

use Doctrine\ORM\EntityManager;
use Symfony\Component\DependencyInjection\ContainerAware;

use DBLS\Bundle\JerseyColoursBundle\Entity\Synonym;

class SynonymOptions extends ContainerAware
{

    /**
     * Holds an instance of database connection
     * @var EntityManager
     */
    private $em = null;

    public function __construct(EntityManager $em) 
    {
        $this->em = $em;
    }

    /**
    * @param  string
    */
    public function getOptions($term = null) 
    {
        $options = array();

        $colours = $this->getColoursOptions($term);
        if ( !empty($colours) )
            $options[] = array('text' => 'Colours', 'children' => $colours);

        $elements = $this->getElementsOptions($term);
        if ( !empty($elements) )
            $options[] = array('text' => 'Elements', 'children' => $elements);

        $patterns = $this->getPatternsOptions($term);
        if ( !empty($patterns) )
            $options[] = array('text' => 'Pattern', 'children' => $patterns);

        return $options;
    }

    /**
     * 
     * @param  string $term
     * @return array
     */
    public function getColoursOptions($term = null)
    {
        $dataColours = $this->em->getRepository('JerseyColoursBundle:Colour')->listOptionsCombo($term);
        return $this->_addTypeField($dataColours, Synonym::TYPE_COLOUR);
    }

    /**
     * 
     * @param  string $term
     * @return array
     */
    public function getElementsOptions($term = null)
    {
        $dataElements = $this->em->getRepository('JerseyColoursBundle:ElementType')->listOptionsCombo($term);
        return $this->_addTypeField($dataElements, Synonym::TYPE_ELEMENT);
    }

    /**
     * 
     * @param  string $term
     * @return array
     */
    public function getPatternsOptions($term = null)
    {
        $dataPatterns = $this->em->getRepository('JerseyColoursBundle:Pattern')->listOptionsCombo($term);
        return $this->_addTypeField($dataPatterns, Synonym::TYPE_PATTERN);
    }

    /**
     * 
     * @param array $options
     * @param int $type
     */
    protected function _addTypeField($options, $type)
    {
        foreach ($options as $key => $value) {

            $options[$key]['id'] = implode('|', array($type, $value['id']));
            $options[$key]['type'] = $type;
        }

        return $options;
    }

    /**
     * 
     * @param  int $id
     * @param  int $type
     */
    public function getOriginalTerm($id, $type)
    {
        switch($type) {
            case Synonym::TYPE_COLOUR:
                $color = $this->em->getRepository('JerseyColoursBundle:Colour')->find($id);
                return $color->getName();
                break;
            case Synonym::TYPE_ELEMENT:
                $elementType = $this->em->getRepository('JerseyColoursBundle:ElementType')->find($id);
                return $elementType->getName();
                break;
            case Synonym::TYPE_PATTERN:
                $pattern = $this->em->getRepository('JerseyColoursBundle:Pattern')->find($id);
                return $pattern->getDescription();
                break;
            default:
                throw new \Exception('Could not find the Synonym type');
        }
    }

    /**
     * 
     * @param  Synonym $synonym
     * @return string
     */
    public function buildFormTerm(Synonym $synonym)
    {
        switch($synonym->getType()) {
            case Synonym::TYPE_COLOUR:
                $termId = $synonym->getColour()->getId();
                break;
            case Synonym::TYPE_ELEMENT:
                $termId = $synonym->getElementType()->getId();
                break;
            case Synonym::TYPE_PATTERN:
                $termId = $synonym->getPattern()->getId();
                break;
            default:
                throw new \Exception('Could not find the Synonym type');
        }

        $originalTerm = array($synonym->getType(), $termId);
        return implode('|', $originalTerm);
    }
}
