/* Table styles */
.dataTable .ui-state-default {
    .box-shadow(none);
    border-right: 0;
    border-left: 0;
}
.dataTables_length {
    color: #878787;
    margin: 7px 5px 0;
    position: absolute;
    right: 2px;
    top: -2px;
}
.dataTables_length div {
	vertical-align: middle;
}


div.dataTables_wrapper .ui-widget-header {
    border-right: medium none;
    border-top: 1px solid #D5D5D5;
    font-weight: normal;
    margin-top: -1px;
    @media (max-width: 979px) {
        height: 68px;
    }
}
.dataTables_wrapper .ui-toolbar {
    padding: 5px;
    margin: 0;
    border-bottom: 0 !important;
}
.dataTables_filter {
    color: #878787;
    font-size: 11px;
    margin: -1px 8px 2px 10px;
    text-align: left;
    float: left;
    @media (max-width: 979px) {
        position: relative;
    }
}
.dataTables_filter input {
	margin-bottom: 0;
}
.DataTables_sort_icon {
    margin-top: 1px;
}

.table {

    th {
    	height: auto;
    	font-size: 10px;
    	padding: 5px 10px 2px;
    	border-bottom: 0;
    	text-align: center;
    	color: #666666;
        vertical-align: middle !important;
    }
    tr.checked td {
        background-color: #FFFFE3 !important;
    }
    &.with-check {
        tr {
            th:first-child, td:first-child {
            	width: 10px;
            }
            th:first-child i {
            	margin-top: -2px;
            	opacity: 0.6;
            }
            td:first-child .checker {
            	margin-right: 0;
            }
        }
    }
    &.table-striped tbody > tr:nth-child(2n+1) {
        > th, > td {
            background-color: #FCFCFC;
        }
    }
    &.table-hover tbody > tr:hover > td {
        background-color: #f5f5f5;
    }
}

span.icon .checker {
    margin-top: -5px;
    margin-right: 0;
}
@media (max-width: 979px) {
    .dataTables_filter,
    .dataTables_paginate {
        text-align: center;
        @media (max-width: @screen-xs-max) {
            float: none !important;
        }
    }
}
