class JerseyElement < ApplicationRecord
  belongs_to :jersey
  belongs_to :element_type
  belongs_to :colour, optional: true
  has_many :jersey_element_patterns, dependent: :destroy
  has_many :patterns, through: :jersey_element_patterns

  validates :order, presence: true, uniqueness: { scope: :jersey_id }

  scope :ordered, -> { order(:order) }

  def to_string
    parts = []
    parts << colour.name if colour
    parts << element_type.name

    # Add pattern information if present
    jersey_element_patterns.each do |jep|
      pattern_info = jep.pattern.description
      pattern_colors = jep.jersey_pattern_colours.ordered.map { |jpc| jpc.colour.name }
      pattern_info += " (#{pattern_colors.join(', ')})" if pattern_colors.any?
      parts << pattern_info
    end

    parts.join(" ")
  end
end
