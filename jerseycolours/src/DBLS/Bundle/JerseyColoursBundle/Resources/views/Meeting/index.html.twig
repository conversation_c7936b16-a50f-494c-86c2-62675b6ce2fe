{% extends '::base.html.twig' %}

{% block body -%}
    <h1>Meeting list</h1>

    <table class="records_list">
        <thead>
            <tr>
                <th>Id</th>
                <th>Name</th>
                <th>Startdate</th>
                <th class="no-sort">Actions</th>
            </tr>
        </thead>
        <tbody>
        {% for entity in entities %}
            <tr>
                <td><a href="{{ path('meeting_show', { 'id': entity.id }) }}">{{ entity.id }}</a></td>
                <td>{{ entity.name }}</td>
                <td>{% if entity.startDate %}{{ entity.startDate|date('Y-m-d H:i:s') }}{% endif %}</td>
                <td>
                <ul>
                    <li>
                        <a href="{{ path('meeting_show', { 'id': entity.id }) }}">show</a>
                    </li>
                </ul>
                </td>
            </tr>
        {% endfor %}
        </tbody>
    </table>

    {% endblock %}
