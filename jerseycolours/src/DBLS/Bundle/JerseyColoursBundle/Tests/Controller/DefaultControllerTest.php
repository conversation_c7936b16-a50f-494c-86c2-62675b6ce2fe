<?php

namespace DBLS\Bundle\JerseyColoursBundle\Tests\Controller;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class DefaultControllerTest extends WebTestCase
{
    public function testDummy()
    {
        $this->assertEquals(1,1);
    }
    
    public function xtestIndex()
    {
        $client = static::createClient();
        $crawler = $client->request('GET', '/');
        $this->assertTrue($crawler->filter('html:contains("Dashboard")')->count() > 0);
    }
}
