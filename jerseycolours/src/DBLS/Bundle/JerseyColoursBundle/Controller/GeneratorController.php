<?php

namespace DBLS\Bundle\JerseyColoursBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;

use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Template;

/**
 * Generator controller.
 *
 * @Route("/generator")
 */
class GeneratorController extends Controller
{

    /**
     * Lists all Colour entities.
     *
     * @Route("/{id}/{view}", name="jersey_generator", defaults={"view" = "front"})
     * @Method("GET")
     * @Template("JerseyColoursBundle:Generator:canvas.html.twig")
     */
    public function indexAction($id, $view)
    {
        $em = $this->getDoctrine()->getEntityManager();

        $entity = $em->getRepository('JerseyColoursBundle:Jersey')->find($id);

        if ( !$entity ) {
            throw $this->createNotFoundException('Unable to find Jersey entity.');
        }

        $jerseyGenerator = $this->get('services.jersey_generator');
        $entityJson = $jerseyGenerator->jerseyToJson($entity);

        $entityJson['view'] = $view;

        return array( 'jersey' => $entityJson );
    }
}
