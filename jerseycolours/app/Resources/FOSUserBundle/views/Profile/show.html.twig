{% extends 'site.html.twig' %}

{% block page_header %}
    <div id="content-header">
        <h1>Change Password</h1>        
    </div>
{% endblock page_header %}

{% block breadcrumb %}
    <div id="breadcrumb">
        <a href="{{ path('homepage') }}" class="tip-bottom" data-original-title="Go to Home"><i class="fa fa-home"></i> Home</a>
        <a href="{{ path('fos_user_change_password') }}" class="current">Change Password</a>
    </div>
{% endblock breadcrumb %}

{% block content %}

    <div class="row">
        <div class="col-xs-12">

            <div class="alert alert-success alert-block">                
                <h4 class="alert-heading">Success!</h4>

                {% if is_granted("IS_AUTHENTICATED_REMEMBERED") %}
                    {{ 'layout.logged_in_as'|trans({'%username%': app.user.username}, 'FOSUserBundle') }} |
                    <a href="{{ path('fos_user_security_logout') }}">
                        {{ 'layout.logout'|trans({}, 'FOSUserBundle') }}
                    </a>
                {% else %}
                    <a href="{{ path('fos_user_security_login') }}">{{ 'layout.login'|trans({}, 'FOSUserBundle') }}</a>
                {% endif %}

            </div>

            <!--
            <p>{{ 'profile.show.username'|trans({}, 'FOSUserBundle') }}: {{ user.username }}</p>
            <p>{{ 'profile.show.email'|trans({}, 'FOSUserBundle') }}: {{ user.email }}</p>
            -->
        </div>
    </div>

{% endblock %}