{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#composer-lock-the-lock-file", "This file is @generated automatically"], "hash": "21eca26d385951183cfe25e26a13100e", "content-hash": "d5bace60723dc717cfceff6f4e435aa4", "packages": [{"name": "doctrine/annotations", "version": "v1.2.7", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "f25c8aab83e0c3e976fd7d19875f198ccf2f7535"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/f25c8aab83e0c3e976fd7d19875f198ccf2f7535", "reference": "f25c8aab83e0c3e976fd7d19875f198ccf2f7535", "shasum": ""}, "require": {"doctrine/lexer": "1.*", "php": ">=5.3.2"}, "require-dev": {"doctrine/cache": "1.*", "phpunit/phpunit": "4.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-0": {"Doctrine\\Common\\Annotations\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "http://www.doctrine-project.org", "keywords": ["annotations", "doc<PERSON>", "parser"], "time": "2015-08-31 12:32:49"}, {"name": "doctrine/cache", "version": "v1.4.2", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "8c434000f420ade76a07c64cbe08ca47e5c101ca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/8c434000f420ade76a07c64cbe08ca47e5c101ca", "reference": "8c434000f420ade76a07c64cbe08ca47e5c101ca", "shasum": ""}, "require": {"php": ">=5.3.2"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"phpunit/phpunit": ">=3.7", "predis/predis": "~1.0", "satooshi/php-coveralls": "~0.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-0": {"Doctrine\\Common\\Cache\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Caching library offering an object-oriented API for many cache backends", "homepage": "http://www.doctrine-project.org", "keywords": ["cache", "caching"], "time": "2015-08-31 12:36:41"}, {"name": "doctrine/collections", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "6c1e4eef75f310ea1b3e30945e9f06e652128b8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/6c1e4eef75f310ea1b3e30945e9f06e652128b8a", "reference": "6c1e4eef75f310ea1b3e30945e9f06e652128b8a", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-0": {"Doctrine\\Common\\Collections\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Collections Abstraction library", "homepage": "http://www.doctrine-project.org", "keywords": ["array", "collections", "iterator"], "time": "2015-04-14 22:21:58"}, {"name": "doctrine/common", "version": "v2.5.1", "source": {"type": "git", "url": "https://github.com/doctrine/common.git", "reference": "0009b8f0d4a917aabc971fb089eba80e872f83f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/common/zipball/0009b8f0d4a917aabc971fb089eba80e872f83f9", "reference": "0009b8f0d4a917aabc971fb089eba80e872f83f9", "shasum": ""}, "require": {"doctrine/annotations": "1.*", "doctrine/cache": "1.*", "doctrine/collections": "1.*", "doctrine/inflector": "1.*", "doctrine/lexer": "1.*", "php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "~3.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6.x-dev"}}, "autoload": {"psr-0": {"Doctrine\\Common\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Common Library for Doctrine projects", "homepage": "http://www.doctrine-project.org", "keywords": ["annotations", "collections", "eventmanager", "persistence", "spl"], "time": "2015-08-31 13:00:22"}, {"name": "doctrine/data-fixtures", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/data-fixtures.git", "reference": "bd44f6b6e40247b6530bc8abe802e4e4d914976a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/data-fixtures/zipball/bd44f6b6e40247b6530bc8abe802e4e4d914976a", "reference": "bd44f6b6e40247b6530bc8abe802e4e4d914976a", "shasum": ""}, "require": {"doctrine/common": "~2.2", "php": ">=5.3.2"}, "conflict": {"doctrine/orm": "< 2.4"}, "require-dev": {"doctrine/orm": "~2.4"}, "suggest": {"doctrine/mongodb-odm": "For loading MongoDB ODM fixtures", "doctrine/orm": "For loading ORM fixtures", "doctrine/phpcr-odm": "For loading PHPCR ODM fixtures"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-0": {"Doctrine\\Common\\DataFixtures": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Data Fixtures for all Doctrine Object Managers", "homepage": "http://www.doctrine-project.org", "keywords": ["database"], "time": "2015-03-30 12:14:13"}, {"name": "doctrine/dbal", "version": "v2.4.4", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "a370e5b95e509a7809d11f3d280acfc9310d464b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/a370e5b95e509a7809d11f3d280acfc9310d464b", "reference": "a370e5b95e509a7809d11f3d280acfc9310d464b", "shasum": ""}, "require": {"doctrine/common": "~2.4", "php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "3.7.*", "symfony/console": "~2.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "type": "library", "autoload": {"psr-0": {"Doctrine\\DBAL\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Database Abstraction Layer", "homepage": "http://www.doctrine-project.org", "keywords": ["database", "dbal", "persistence", "queryobject"], "time": "2015-01-12 21:57:01"}, {"name": "doctrine/doctrine-bundle", "version": "v1.5.1", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineBundle.git", "reference": "8c5cedb4f2f7ebb66a963ae46ad9daa1e31cee01"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineBundle/zipball/8c5cedb4f2f7ebb66a963ae46ad9daa1e31cee01", "reference": "8c5cedb4f2f7ebb66a963ae46ad9daa1e31cee01", "shasum": ""}, "require": {"doctrine/dbal": "~2.3", "doctrine/doctrine-cache-bundle": "~1.0", "jdorn/sql-formatter": "~1.1", "php": ">=5.3.2", "symfony/console": "~2.3|~3.0", "symfony/doctrine-bridge": "~2.2|~3.0", "symfony/framework-bundle": "~2.3|~3.0"}, "require-dev": {"doctrine/orm": "~2.3", "phpunit/phpunit": "~4", "satooshi/php-coveralls": "~0.6.1", "symfony/validator": "~2.2|~3.0", "symfony/yaml": "~2.2|~3.0", "twig/twig": "~1.10"}, "suggest": {"doctrine/orm": "The Doctrine ORM integration is optional in the bundle.", "symfony/web-profiler-bundle": "to use the data collector"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\DoctrineBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org/"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony DoctrineBundle", "homepage": "http://www.doctrine-project.org", "keywords": ["database", "dbal", "orm", "persistence"], "time": "2015-08-12 15:52:00"}, {"name": "doctrine/doctrine-cache-bundle", "version": "v1.0.1", "target-dir": "Doctrine/Bundle/DoctrineCacheBundle", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineCacheBundle.git", "reference": "e4b6f810aa047f9cbfe41c3d6a3d7e83d7477a9d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineCacheBundle/zipball/e4b6f810aa047f9cbfe41c3d6a3d7e83d7477a9d", "reference": "e4b6f810aa047f9cbfe41c3d6a3d7e83d7477a9d", "shasum": ""}, "require": {"doctrine/cache": "~1.3", "doctrine/inflector": "~1.0", "php": ">=5.3.2", "symfony/doctrine-bridge": "~2.2", "symfony/framework-bundle": "~2.2", "symfony/security": "~2.2"}, "require-dev": {"instaclick/coding-standard": "~1.1", "instaclick/object-calisthenics-sniffs": "dev-master", "instaclick/symfony2-coding-standard": "dev-remaster", "phpunit/phpunit": "~3.7", "satooshi/php-coveralls": "~0.6.1", "squizlabs/php_codesniffer": "dev-master", "symfony/console": "~2.2", "symfony/finder": "~2.2", "symfony/validator": "~2.2", "symfony/yaml": "~2.2"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"Doctrine\\Bundle\\DoctrineCacheBundle": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "g<PERSON><PERSON><PERSON><PERSON><EMAIL>"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org/"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony2 Bundle for Doctrine Cache", "homepage": "http://www.doctrine-project.org", "keywords": ["cache", "caching"], "time": "2014-11-28 09:43:36"}, {"name": "doctrine/doctrine-fixtures-bundle", "version": "v2.2.1", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineFixturesBundle.git", "reference": "817c2d233fde0fe85cb7e4d25d43fbfcd028aef8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineFixturesBundle/zipball/817c2d233fde0fe85cb7e4d25d43fbfcd028aef8", "reference": "817c2d233fde0fe85cb7e4d25d43fbfcd028aef8", "shasum": ""}, "require": {"doctrine/data-fixtures": "~1.0", "doctrine/doctrine-bundle": "~1.0", "php": ">=5.3.2", "symfony/doctrine-bridge": "~2.1"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\FixturesBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony DoctrineFixturesBundle", "homepage": "http://www.doctrine-project.org", "keywords": ["Fixture", "persistence"], "time": "2015-08-04 22:43:14"}, {"name": "doctrine/inflector", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "0bcb2e79d8571787f18b7eb036ed3d004908e604"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/0bcb2e79d8571787f18b7eb036ed3d004908e604", "reference": "0bcb2e79d8571787f18b7eb036ed3d004908e604", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "4.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"Doctrine\\Common\\Inflector\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Common String Manipulations with regard to casing and singular/plural rules.", "homepage": "http://www.doctrine-project.org", "keywords": ["inflection", "pluralize", "singularize", "string"], "time": "2014-12-20 21:24:13"}, {"name": "doctrine/instantiator", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/8e884e78f9f0eb1329e445619e04456e64d8051d", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d", "shasum": ""}, "require": {"php": ">=5.3,<8.0-DEV"}, "require-dev": {"athletic/athletic": "~0.1.8", "ext-pdo": "*", "ext-phar": "*", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://github.com/doctrine/instantiator", "keywords": ["constructor", "instantiate"], "time": "2015-06-14 21:17:01"}, {"name": "doctrine/lexer", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "83893c552fd2045dd78aef794c31e694c37c0b8c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/83893c552fd2045dd78aef794c31e694c37c0b8c", "reference": "83893c552fd2045dd78aef794c31e694c37c0b8c", "shasum": ""}, "require": {"php": ">=5.3.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"Doctrine\\Common\\Lexer\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Base library for a lexer that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "http://www.doctrine-project.org", "keywords": ["lexer", "parser"], "time": "2014-09-09 13:34:57"}, {"name": "doctrine/orm", "version": "v2.4.8", "source": {"type": "git", "url": "https://github.com/doctrine/doctrine2.git", "reference": "5aedac1e5c5caaeac14798822c70325dc242d467"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/doctrine2/zipball/5aedac1e5c5caaeac14798822c70325dc242d467", "reference": "5aedac1e5c5caaeac14798822c70325dc242d467", "shasum": ""}, "require": {"doctrine/collections": "~1.1", "doctrine/dbal": "~2.4", "ext-pdo": "*", "php": ">=5.3.2", "symfony/console": "~2.0"}, "require-dev": {"satooshi/php-coveralls": "dev-master", "symfony/yaml": "~2.1"}, "suggest": {"symfony/yaml": "If you want to use YAML Metadata Mapping Driver"}, "bin": ["bin/doctrine", "bin/doctrine.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.4.x-dev"}}, "autoload": {"psr-0": {"Doctrine\\ORM\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Object-Relational-Mapper for PHP", "homepage": "http://www.doctrine-project.org", "keywords": ["database", "orm"], "time": "2015-08-31 13:19:01"}, {"name": "fredcido/phpsvg", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/fredcido/phpsvg.git", "reference": "c55ace2e9e7edae1d6d0e334ea4019ce2a0d14cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fredcido/phpsvg/zipball/c55ace2e9e7edae1d6d0e334ea4019ce2a0d14cd", "reference": "c55ace2e9e7edae1d6d0e334ea4019ce2a0d14cd", "shasum": ""}, "type": "library", "autoload": {"psr-0": {"Fredcido\\PHPSVG": "src"}}, "support": {"source": "https://github.com/fredcido/phpsvg/tree/master", "issues": "https://github.com/fredcido/phpsvg/issues"}, "time": "2015-08-30 14:11:45"}, {"name": "friendsofsymfony/jsrouting-bundle", "version": "1.5.4", "source": {"type": "git", "url": "https://github.com/FriendsOfSymfony/FOSJsRoutingBundle.git", "reference": "539a6ac0c1fb6b010f92a1c877426a4caa5abba1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfSymfony/FOSJsRoutingBundle/zipball/539a6ac0c1fb6b010f92a1c877426a4caa5abba1", "reference": "539a6ac0c1fb6b010f92a1c877426a4caa5abba1", "shasum": ""}, "require": {"php": ">=5.3.2", "symfony/console": "~2.0", "symfony/framework-bundle": "~2.0", "symfony/serializer": "~2.0", "willdurand/jsonp-callback-validator": "~1.0"}, "require-dev": {"symfony/expression-language": "~2.4"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-4": {"FOS\\JsRoutingBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "FriendsOfSymfony Community", "homepage": "https://github.com/friendsofsymfony/FOSJsRoutingBundle/contributors"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A pretty nice way to expose your Symfony2 routing to client applications.", "homepage": "http://friendsofsymfony.github.com", "keywords": ["Js Routing", "javascript", "routing"], "time": "2015-01-23 02:44:26"}, {"name": "friendsofsymfony/rest-bundle", "version": "1.7.1", "target-dir": "FOS/RestBundle", "source": {"type": "git", "url": "https://github.com/FriendsOfSymfony/FOSRestBundle.git", "reference": "3fb2d30c58cde59213dbddd031bc36171b8b68b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfSymfony/FOSRestBundle/zipball/3fb2d30c58cde59213dbddd031bc36171b8b68b6", "reference": "3fb2d30c58cde59213dbddd031bc36171b8b68b6", "shasum": ""}, "require": {"doctrine/inflector": "~1.0", "php": ">=5.3.9", "psr/log": "~1.0", "symfony/framework-bundle": "~2.3", "symfony/http-kernel": "~2.3,>=2.3.24", "willdurand/jsonp-callback-validator": "~1.0", "willdurand/negotiation": "~1.2"}, "conflict": {"jms/serializer": "<0.12", "jms/serializer-bundle": "<0.11", "symfony/validator": ">=2.5.0,<2.5.5"}, "require-dev": {"jms/serializer": "~0.13", "jms/serializer-bundle": "~0.12", "phpoption/phpoption": "~1.1.0", "sensio/framework-extra-bundle": "~3.0", "symfony/browser-kit": "~2.3", "symfony/dependency-injection": "~2.3", "symfony/form": "~2.3", "symfony/security": "~2.3", "symfony/serializer": "~2.3", "symfony/validator": "~2.3", "symfony/yaml": "~2.3"}, "suggest": {"jms/serializer-bundle": "Add support for advanced serialization capabilities, recommended, requires ~0.12", "sensio/framework-extra-bundle": "Add support for route annotations and the view response listener, requires ~3.0", "symfony/serializer": "Add support for basic serialization capabilities and xml decoding, requires ~2.3", "symfony/validator": "Add support for validation capabilities in the ParamFetcher, requires ~2.3"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "autoload": {"psr-0": {"FOS\\RestBundle": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "FriendsOfSymfony Community", "homepage": "https://github.com/friendsofsymfony/FOSRestBundle/contributors"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "This Bundle provides various tools to rapidly develop RESTful API's with Symfony2", "homepage": "http://friendsofsymfony.github.com", "keywords": ["rest"], "time": "2015-06-16 08:39:26"}, {"name": "friendsofsymfony/user-bundle", "version": "v1.3.6", "target-dir": "FOS/UserBundle", "source": {"type": "git", "url": "https://github.com/FriendsOfSymfony/FOSUserBundle.git", "reference": "2022ac8463a8c50dd631f93a17064bd41795fff2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfSymfony/FOSUserBundle/zipball/2022ac8463a8c50dd631f93a17064bd41795fff2", "reference": "2022ac8463a8c50dd631f93a17064bd41795fff2", "shasum": ""}, "require": {"php": ">=5.3.2", "symfony/framework-bundle": "~2.1", "symfony/security-bundle": "~2.1"}, "require-dev": {"doctrine/doctrine-bundle": "~1.3", "swiftmailer/swiftmailer": "~4.3|~5", "symfony/validator": "~2.1", "symfony/yaml": "~2.1", "twig/twig": "~1.5", "willdurand/propel-typehintable-behavior": "~1.0"}, "suggest": {"willdurand/propel-typehintable-behavior": "Needed when using the propel implementation"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-0": {"FOS\\UserBundle": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "FriendsOfSymfony Community", "homepage": "https://github.com/friendsofsymfony/FOSUserBundle/contributors"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony FOSUserBundle", "homepage": "http://friendsofsymfony.github.com", "keywords": ["User management"], "time": "2015-06-01 00:57:03"}, {"name": "glanchow/doctrine-fuzzy", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/glanchow/doctrine-fuzzy.git", "reference": "b093f6b3cf258abab3f98976bf728bd210fe1845"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/glanchow/doctrine-fuzzy/zipball/b093f6b3cf258abab3f98976bf728bd210fe1845", "reference": "b093f6b3cf258abab3f98976bf728bd210fe1845", "shasum": ""}, "require": {"doctrine/orm": ">=2.2.0"}, "type": "library", "autoload": {"psr-0": {"WOK": "lib/"}}, "license": ["MIT"], "description": "Doctrine2 DQL fuzzy functions", "keywords": ["against", "doctrine", "<PERSON><PERSON><PERSON><PERSON>", "match", "soundex", "symfony"], "support": {"source": "https://github.com/glanchow/doctrine-fuzzy/tree/master", "issues": "https://github.com/glanchow/doctrine-fuzzy/issues"}, "time": "2014-11-08 12:07:09"}, {"name": "guzzle/guzzle", "version": "v3.9.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle3.git", "reference": "0645b70d953bc1c067bbc8d5bc53194706b628d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle3/zipball/0645b70d953bc1c067bbc8d5bc53194706b628d9", "reference": "0645b70d953bc1c067bbc8d5bc53194706b628d9", "shasum": ""}, "require": {"ext-curl": "*", "php": ">=5.3.3", "symfony/event-dispatcher": "~2.1"}, "replace": {"guzzle/batch": "self.version", "guzzle/cache": "self.version", "guzzle/common": "self.version", "guzzle/http": "self.version", "guzzle/inflection": "self.version", "guzzle/iterator": "self.version", "guzzle/log": "self.version", "guzzle/parser": "self.version", "guzzle/plugin": "self.version", "guzzle/plugin-async": "self.version", "guzzle/plugin-backoff": "self.version", "guzzle/plugin-cache": "self.version", "guzzle/plugin-cookie": "self.version", "guzzle/plugin-curlauth": "self.version", "guzzle/plugin-error-response": "self.version", "guzzle/plugin-history": "self.version", "guzzle/plugin-log": "self.version", "guzzle/plugin-md5": "self.version", "guzzle/plugin-mock": "self.version", "guzzle/plugin-oauth": "self.version", "guzzle/service": "self.version", "guzzle/stream": "self.version"}, "require-dev": {"doctrine/cache": "~1.3", "monolog/monolog": "~1.0", "phpunit/phpunit": "3.7.*", "psr/log": "~1.0", "symfony/class-loader": "~2.1", "zendframework/zend-cache": "2.*,<2.3", "zendframework/zend-log": "2.*,<2.3"}, "suggest": {"guzzlehttp/guzzle": "Guzzle 5 has moved to a new package name. The package you have installed, Guzzle 3, is deprecated."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.9-dev"}}, "autoload": {"psr-0": {"Guzzle": "src/", "Guzzle\\Tests": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "Guzzle Community", "homepage": "https://github.com/guzzle/guzzle/contributors"}], "description": "PHP HTTP client. This library is deprecated in favor of https://packagist.org/packages/guzzlehttp/guzzle", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2015-03-18 18:23:50"}, {"name": "incenteev/composer-parameter-handler", "version": "v2.1.1", "source": {"type": "git", "url": "https://github.com/Incenteev/ParameterHandler.git", "reference": "84a205fe80a46101607bafbc423019527893ddd0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Incenteev/ParameterHandler/zipball/84a205fe80a46101607bafbc423019527893ddd0", "reference": "84a205fe80a46101607bafbc423019527893ddd0", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/yaml": "~2.0"}, "require-dev": {"composer/composer": "1.0.*@dev", "phpspec/prophecy-phpunit": "~1.0", "symfony/filesystem": "~2.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Incenteev\\ParameterHandler\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Composer script handling your ignored parameter file", "homepage": "https://github.com/Incenteev/ParameterHandler", "keywords": ["parameters management"], "time": "2015-06-03 08:27:03"}, {"name": "jakoch/phantomjs-installer", "version": "1.9.8", "source": {"type": "git", "url": "https://github.com/jakoch/phantomjs-installer.git", "reference": "fd6459094404f698a4edb9609e5e8c55c6418824"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jakoch/phantomjs-installer/zipball/fd6459094404f698a4edb9609e5e8c55c6418824", "reference": "fd6459094404f698a4edb9609e5e8c55c6418824", "shasum": ""}, "require": {"ext-openssl": "*"}, "type": "custom-installer", "autoload": {"psr-0": {"PhantomInstaller\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A Composer package which installs the PhantomJS binary (Linux, Windows, Mac) into `/bin` of your project.", "keywords": ["binaries", "headless", "phantom<PERSON>s"], "time": "2014-12-05 17:57:27"}, {"name": "jdorn/sql-formatter", "version": "v1.2.17", "source": {"type": "git", "url": "https://github.com/jdorn/sql-formatter.git", "reference": "64990d96e0959dff8e059dfcdc1af130728d92bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jdorn/sql-formatter/zipball/64990d96e0959dff8e059dfcdc1af130728d92bc", "reference": "64990d96e0959dff8e059dfcdc1af130728d92bc", "shasum": ""}, "require": {"php": ">=5.2.4"}, "require-dev": {"phpunit/phpunit": "3.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["lib"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://jeremydorn.com/"}], "description": "a PHP SQL highlighting library", "homepage": "https://github.com/jdorn/sql-formatter/", "keywords": ["highlight", "sql"], "time": "2014-01-12 16:20:24"}, {"name": "jms/metadata", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/schmittjoh/metadata.git", "reference": "22b72455559a25777cfd28c4ffda81ff7639f353"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/metadata/zipball/22b72455559a25777cfd28c4ffda81ff7639f353", "reference": "22b72455559a25777cfd28c4ffda81ff7639f353", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"doctrine/cache": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-0": {"Metadata\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h", "role": "Developer of wrapped JMSSerializerBundle"}], "description": "Class/method/property metadata management in PHP", "keywords": ["annotations", "metadata", "xml", "yaml"], "time": "2014-07-12 07:13:19"}, {"name": "jms/parser-lib", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/parser-lib.git", "reference": "c509473bc1b4866415627af0e1c6cc8ac97fa51d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/parser-lib/zipball/c509473bc1b4866415627af0e1c6cc8ac97fa51d", "reference": "c509473bc1b4866415627af0e1c6cc8ac97fa51d", "shasum": ""}, "require": {"phpoption/phpoption": ">=0.9,<2.0-dev"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-0": {"JMS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache2"], "description": "A library for easily creating recursive-descent parsers.", "time": "2012-11-18 18:08:43"}, {"name": "jms/serializer", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/serializer.git", "reference": "a29d9a204efc3ca3f39a9d182a83fd34462fef3f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/serializer/zipball/a29d9a204efc3ca3f39a9d182a83fd34462fef3f", "reference": "a29d9a204efc3ca3f39a9d182a83fd34462fef3f", "shasum": ""}, "require": {"doctrine/annotations": "1.*", "doctrine/instantiator": "~1.0.3", "jms/metadata": "~1.1", "jms/parser-lib": "1.*", "php": ">=5.4.0", "phpcollection/phpcollection": "~0.1"}, "require-dev": {"doctrine/orm": "~2.1", "doctrine/phpcr-odm": "~1.0.1", "jackalope/jackalope-doctrine-dbal": "1.0.*", "phpunit/phpunit": "~4.0", "propel/propel1": "~1.7", "symfony/filesystem": "2.*", "symfony/form": "~2.1", "symfony/translation": "~2.0", "symfony/validator": "~2.0", "symfony/yaml": "2.*", "twig/twig": ">=1.8,<2.0-dev"}, "suggest": {"symfony/yaml": "Required if you'd like to serialize data to YAML format."}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.17-dev"}}, "autoload": {"psr-0": {"JMS\\Serializer": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache2"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Library for (de-)serializing data of any complexity; supports XML, JSON, and YAML.", "homepage": "http://jmsyst.com/libs/serializer", "keywords": ["deserialization", "jaxb", "json", "serialization", "xml"], "time": "2015-06-16 11:50:24"}, {"name": "jms/serializer-bundle", "version": "1.0.0", "target-dir": "JMS/SerializerBundle", "source": {"type": "git", "url": "https://github.com/schmittjoh/JMSSerializerBundle.git", "reference": "0be35615b5bae1ce42567244a321aa1be3ed0280"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/JMSSerializerBundle/zipball/0be35615b5bae1ce42567244a321aa1be3ed0280", "reference": "0be35615b5bae1ce42567244a321aa1be3ed0280", "shasum": ""}, "require": {"jms/serializer": "^1.0.0", "php": ">=5.3.2", "symfony/framework-bundle": "~2.1"}, "require-dev": {"doctrine/doctrine-bundle": "*", "doctrine/orm": "*", "symfony/browser-kit": "*", "symfony/class-loader": "*", "symfony/css-selector": "*", "symfony/finder": "*", "symfony/form": "*", "symfony/process": "*", "symfony/stopwatch": "*", "symfony/twig-bundle": "*", "symfony/validator": "*", "symfony/yaml": "*"}, "suggest": {"jms/di-extra-bundle": "Required to get lazy loading (de)serialization visitors, ~1.3"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "0.13-dev"}}, "autoload": {"psr-0": {"JMS\\SerializerBundle": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache2"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Allows you to easily serialize, and deserialize data of any complexity", "homepage": "http://jmsyst.com/bundles/JMSSerializerBundle", "keywords": ["deserialization", "jaxb", "json", "serialization", "xml"], "time": "2015-06-23 19:27:08"}, {"name": "knplabs/knp-components", "version": "1.3.2", "source": {"type": "git", "url": "https://github.com/KnpLabs/knp-components.git", "reference": "4503275e4f1a0e9667aa65b0ebed842ef2d8d8ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/knp-components/zipball/4503275e4f1a0e9667aa65b0ebed842ef2d8d8ba", "reference": "4503275e4f1a0e9667aa65b0ebed842ef2d8d8ba", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"doctrine/mongodb-odm": "~1.0@beta", "doctrine/orm": "~2.4", "phpunit/phpunit": "~4.2", "ruflin/elastica": "~1.0", "symfony/event-dispatcher": "~2.5"}, "suggest": {"doctrine/common": "to allow usage pagination with Doctrine ArrayCollection", "doctrine/mongodb-odm": "to allow usage pagination with Doctrine ODM MongoDB", "doctrine/orm": "to allow usage pagination with Doctrine ORM", "propel/propel1": "to allow usage pagination with Propel ORM", "ruflin/Elastica": "to allow usage pagination with ElasticSearch Client", "solarium/solarium": "to allow usage pagination with Solarium Client"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-0": {"Knp\\Component": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KnpLabs Team", "homepage": "http://knplabs.com"}, {"name": "Symfony Community", "homepage": "http://github.com/KnpLabs/knp-components/contributors"}], "description": "Knplabs component library", "homepage": "http://github.com/KnpLabs/knp-components", "keywords": ["components", "knp", "knplabs", "pager", "paginator"], "time": "2015-09-01 10:54:53"}, {"name": "knplabs/knp-menu", "version": "v2.1.0", "source": {"type": "git", "url": "https://github.com/KnpLabs/KnpMenu.git", "reference": "c6ad49933babd06a27b2f962a3469601ec9038b8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/KnpMenu/zipball/c6ad49933babd06a27b2f962a3469601ec9038b8", "reference": "c6ad49933babd06a27b2f962a3469601ec9038b8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"pimple/pimple": "~1.0", "silex/silex": "~1.0", "symfony/phpunit-bridge": "~2.7", "twig/twig": "~1.16|~2.0"}, "suggest": {"pimple/pimple": "for the built-in implementations of the menu provider and renderer provider", "silex/silex": "for the integration with your silex application", "twig/twig": "for the TwigRenderer and the integration with your templates"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"psr-4": {"Knp\\Menu\\": "src/Knp/Menu"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Knplabs", "homepage": "http://knplabs.com"}, {"name": "Symfony Community", "homepage": "https://github.com/KnpLabs/KnpMenu/contributors"}], "description": "An object oriented menu library", "homepage": "http://knplabs.com", "keywords": ["menu", "tree"], "time": "2015-09-20 08:23:47"}, {"name": "knplabs/knp-menu-bundle", "version": "v2.0.0", "target-dir": "Knp/Bundle/MenuBundle", "source": {"type": "git", "url": "https://github.com/KnpLabs/KnpMenuBundle.git", "reference": "bdfc95da5ff7e4e67f948aaa9ea5da835a3a9088"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/KnpMenuBundle/zipball/bdfc95da5ff7e4e67f948aaa9ea5da835a3a9088", "reference": "bdfc95da5ff7e4e67f948aaa9ea5da835a3a9088", "shasum": ""}, "require": {"knplabs/knp-menu": "~2.0", "symfony/framework-bundle": "~2.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-0": {"Knp\\Bundle\\MenuBundle": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Knplabs", "homepage": "http://knplabs.com"}, {"name": "Symfony Community", "homepage": "https://github.com/KnpLabs/KnpMenuBundle/contributors"}], "description": "This bundle provides an integration of the KnpMenu library", "keywords": ["menu"], "time": "2014-08-01 09:57:23"}, {"name": "knplabs/knp-paginator-bundle", "version": "2.5.0", "source": {"type": "git", "url": "https://github.com/KnpLabs/KnpPaginatorBundle.git", "reference": "4a1daf55560ca7cf9c13b357135d72f4dc13375f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/KnpPaginatorBundle/zipball/4a1daf55560ca7cf9c13b357135d72f4dc13375f", "reference": "4a1daf55560ca7cf9c13b357135d72f4dc13375f", "shasum": ""}, "require": {"knplabs/knp-components": "~1.2", "php": ">=5.3.3", "symfony/framework-bundle": "~2.3", "twig/twig": "~1.12"}, "require-dev": {"symfony/expression-language": "~2.4"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.4.x-dev"}}, "autoload": {"psr-4": {"Knp\\Bundle\\PaginatorBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KnpLabs Team", "homepage": "http://knplabs.com"}, {"name": "Symfony2 Community", "homepage": "http://github.com/KnpLabs/KnpPaginatorBundle/contributors"}], "description": "Paginator bundle for Symfony2 to automate pagination and simplify sorting and other features", "homepage": "http://github.com/KnpLabs/KnpPaginatorBundle", "keywords": ["Symfony2", "bundle", "knp", "knplabs", "pager", "pagination", "paginator"], "time": "2015-09-07 04:31:31"}, {"name": "knplabs/knp-snappy", "version": "0.4.2", "source": {"type": "git", "url": "https://github.com/KnpLabs/snappy.git", "reference": "99941b6f8475e2f02b8b89748504e5d471bded73"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/snappy/zipball/99941b6f8475e2f02b8b89748504e5d471bded73", "reference": "99941b6f8475e2f02b8b89748504e5d471bded73", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/process": "~2.1"}, "require-dev": {"phpunit/phpunit": "4.7.*"}, "suggest": {"h4cc/wkhtmltopdf-amd64": "Provides wkhtmltopdf-amd64 binary for Linux-compatible machines, use version `~0.12` as dependency", "h4cc/wkhtmltopdf-i386": "Provides wkhtmltopdf-i386 binary for Linux-compatible machines, use version `~0.12` as dependency", "wemersonjanuario/wkhtmltopdf-windows": "Provides wkhtmltopdf executable for Windows, use version `~0.12` as dependency"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.5.x-dev"}}, "autoload": {"psr-0": {"Knp\\Snappy": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KnpLabs Team", "homepage": "http://knplabs.com"}, {"name": "Symfony Community", "homepage": "http://github.com/KnpLabs/snappy/contributors"}], "description": "PHP5 library allowing thumbnail, snapshot or PDF generation from a url or a html page. Wrapper for wkhtmltopdf/wkhtmltoimage.", "homepage": "http://github.com/KnpLabs/snappy", "keywords": ["knp", "knplabs", "pdf", "snapshot", "thumbnail", "wkhtmltopdf"], "time": "2015-09-16 12:34:39"}, {"name": "knplabs/knp-snappy-bundle", "version": "v1.3", "source": {"type": "git", "url": "https://github.com/KnpLabs/KnpSnappyBundle.git", "reference": "2aa510cb0192658428ff29206cef85342ec24d44"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/KnpSnappyBundle/zipball/2aa510cb0192658428ff29206cef85342ec24d44", "reference": "2aa510cb0192658428ff29206cef85342ec24d44", "shasum": ""}, "require": {"knplabs/knp-snappy": "*", "php": ">=5.3.2", "symfony/finder": ">=2.0.0", "symfony/framework-bundle": ">=2.0.0", "symfony/validator": ">=2.0.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Knp\\Bundle\\SnappyBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KnpLabs Team", "homepage": "http://knplabs.com"}, {"name": "Symfony Community", "homepage": "http://github.com/KnpLabs/KnpSnappyBundle/contributors"}], "description": "Easily create PDF and images in Symfony2 by converting Twig/HTML templates.", "homepage": "http://github.com/KnpLabs/KnpSnappyBundle", "keywords": ["bundle", "knp", "knplabs", "pdf", "snappy"], "time": "2015-08-24 21:40:00"}, {"name": "kriswallsmith/assetic", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/kriswallsmith/assetic.git", "reference": "56cb5d6dec9e7a68a4da2fa89844f39d41092f31"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kriswallsmith/assetic/zipball/56cb5d6dec9e7a68a4da2fa89844f39d41092f31", "reference": "56cb5d6dec9e7a68a4da2fa89844f39d41092f31", "shasum": ""}, "require": {"php": ">=5.3.1", "symfony/process": "~2.1"}, "conflict": {"twig/twig": "<1.12"}, "require-dev": {"cssmin/cssmin": "*", "joliclic/javascript-packer": "*", "kamicane/packager": "*", "leafo/lessphp": "^0.3.7", "leafo/scssphp": "*@dev", "leafo/scssphp-compass": "*@dev", "mrclay/minify": "*", "patchwork/jsqueeze": "~1.0|~2.0", "phpunit/phpunit": "~4.8", "psr/log": "~1.0", "ptachoire/cssembed": "*", "symfony/phpunit-bridge": "~2.7", "twig/twig": "~1.8|~2.0"}, "suggest": {"leafo/lessphp": "Assetic provides the integration with the lessphp LESS compiler", "leafo/scssphp": "Assetic provides the integration with the scssphp SCSS compiler", "leafo/scssphp-compass": "Assetic provides the integration with the SCSS compass plugin", "patchwork/jsqueeze": "Assetic provides the integration with the JSqueeze JavaScript compressor", "ptachoire/cssembed": "Assetic provides the integration with phpcssembed to embed data uris", "twig/twig": "Assetic provides the integration with the Twig templating engine"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"psr-0": {"Assetic": "src/"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://kriswallsmith.net/"}], "description": "Asset Management for PHP", "homepage": "https://github.com/kriswallsmith/assetic", "keywords": ["assets", "compression", "minification"], "time": "2015-08-31 19:07:16"}, {"name": "monolog/monolog", "version": "1.17.1", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "0524c87587ab85bc4c2d6f5b41253ccb930a5422"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/0524c87587ab85bc4c2d6f5b41253ccb930a5422", "reference": "0524c87587ab85bc4c2d6f5b41253ccb930a5422", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "php-console/php-console": "^3.1.3", "phpunit/phpunit": "~4.5", "phpunit/phpunit-mock-objects": "2.3.0", "raven/raven": "~0.11", "ruflin/elastica": ">=0.90 <3.0", "swiftmailer/swiftmailer": "~5.3", "videlalvaro/php-amqplib": "~2.4"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "php-console/php-console": "Allow sending log messages to Google Chrome", "raven/raven": "Allow sending log messages to a Sentry server", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "videlalvaro/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.16.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "time": "2015-08-31 09:17:37"}, {"name": "mopa/bootstrap-bundle", "version": "v2.3.1", "target-dir": "Mopa/Bundle/BootstrapBundle", "source": {"type": "git", "url": "https://github.com/phiamo/MopaBootstrapBundle.git", "reference": "82523811144266910868d51f5396d110338d7fea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phiamo/MopaBootstrapBundle/zipball/82523811144266910868d51f5396d110338d7fea", "reference": "82523811144266910868d51f5396d110338d7fea", "shasum": ""}, "require": {"mopa/composer-bridge": "1.3.*", "symfony/symfony": "~2.3"}, "suggest": {"craue/formflow-bundle": "dev-master", "jlong/sass-twitter-bootstrap": "dev-master", "knplabs/knp-menu-bundle": "2.0.*", "knplabs/knp-paginator-bundle": "dev-master", "mopa/bootstrap-sandbox-bundle": "~2.3", "twbs/bootstrap": "2.*"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.3.x-dev"}}, "autoload": {"psr-0": {"Mopa\\Bundle\\BootstrapBundle": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Community contributions", "homepage": "https://github.com/phiamo/MopaBootstrapBundle/contributors"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Easy integration of twitters bootstrap into symfony2", "keywords": ["bootstrap", "bootstrap form", "extra form", "form", "template"], "time": "2014-08-26 22:53:53"}, {"name": "mopa/composer-bridge", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/phiamo/MopaComposerBridge.git", "reference": "bbbd9b4595993cda248a996f21af3298f12fa331"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phiamo/MopaComposerBridge/zipball/bbbd9b4595993cda248a996f21af3298f12fa331", "reference": "bbbd9b4595993cda248a996f21af3298f12fa331", "shasum": ""}, "require": {"symfony/console": ">=2.0.0"}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-0": {"Mopa\\Bridge\\Composer": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Community contributions", "homepage": "https://github.com/phiamo/MopaComposerBridge/contributors"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Symfony2 Composer Bridge", "homepage": "http://bootstrap.mohrenweiserpartner.de", "keywords": ["Symfony2", "composer"], "time": "2014-12-29 13:24:12"}, {"name": "phpcollection/phpcollection", "version": "0.4.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-collection.git", "reference": "b8bf55a0a929ca43b01232b36719f176f86c7e83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-collection/zipball/b8bf55a0a929ca43b01232b36719f176f86c7e83", "reference": "b8bf55a0a929ca43b01232b36719f176f86c7e83", "shasum": ""}, "require": {"phpoption/phpoption": "1.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.3-dev"}}, "autoload": {"psr-0": {"PhpCollection": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache2"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h", "role": "Developer of wrapped JMSSerializerBundle"}], "description": "General-Purpose Collection Library for PHP", "keywords": ["collection", "list", "map", "sequence", "set"], "time": "2014-03-11 13:46:42"}, {"name": "phpoption/phpoption", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "94e644f7d2051a5f0fcf77d81605f152eecff0ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/94e644f7d2051a5f0fcf77d81605f152eecff0ed", "reference": "94e644f7d2051a5f0fcf77d81605f152eecff0ed", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "4.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"psr-0": {"PhpOption\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache2"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "time": "2015-07-25 16:39:46"}, {"name": "playbloom/guzzle-bundle", "version": "v1.1.0", "target-dir": "Playbloom/Bundle/GuzzleBundle", "source": {"type": "git", "url": "https://github.com/ludofleury/GuzzleBundle.git", "reference": "1308292f2ae0eb2ed41df5581925e06e1ce8a1d8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ludofleury/GuzzleBundle/zipball/1308292f2ae0eb2ed41df5581925e06e1ce8a1d8", "reference": "1308292f2ae0eb2ed41df5581925e06e1ce8a1d8", "shasum": ""}, "require": {"guzzle/guzzle": "3.*", "php": ">=5.3.0", "symfony/config": "~2.1", "symfony/dependency-injection": "~2.1", "symfony/http-foundation": "~2.1", "symfony/http-kernel": "~2.1", "symfony/web-profiler-bundle": "~2.1"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"Playbloom\\Bundle\\GuzzleBundle": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Provide Symfony2 web profiler for Guzzle", "homepage": "https://github.com/ludofleury/GuzzleBundle", "keywords": ["Guzzle", "Symfony2", "profiler"], "time": "2015-05-12 04:26:40"}, {"name": "psr/log", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "fe0936ee26643249e916849d48e3a51d5f5e278b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/fe0936ee26643249e916849d48e3a51d5f5e278b", "reference": "fe0936ee26643249e916849d48e3a51d5f5e278b", "shasum": ""}, "type": "library", "autoload": {"psr-0": {"Psr\\Log\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "keywords": ["log", "psr", "psr-3"], "time": "2012-12-21 11:40:51"}, {"name": "samj/doctrine-sluggable-bundle", "version": "v2.0", "source": {"type": "git", "url": "https://github.com/samjarrett/DoctrineSluggableBundle.git", "reference": "9f9bf6504b8d8cd6552999de85fdcaa7b29f2e23"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/samjarrett/DoctrineSluggableBundle/zipball/9f9bf6504b8d8cd6552999de85fdcaa7b29f2e23", "reference": "9f9bf6504b8d8cd6552999de85fdcaa7b29f2e23", "shasum": ""}, "require": {"php": ">=5.3.2"}, "type": "library", "autoload": {"psr-0": {"SamJ": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "cameron<PERSON><PERSON>@gmail.com", "role": "Original Author"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "time": "2014-01-02 00:06:43"}, {"name": "sensio/distribution-bundle", "version": "v4.0.1", "target-dir": "Sensio/Bundle/DistributionBundle", "source": {"type": "git", "url": "https://github.com/sensiolabs/SensioDistributionBundle.git", "reference": "62d99ba41144e704300e40f755346553702b27c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/SensioDistributionBundle/zipball/62d99ba41144e704300e40f755346553702b27c9", "reference": "62d99ba41144e704300e40f755346553702b27c9", "shasum": ""}, "require": {"php": ">=5.3.9", "sensiolabs/security-checker": "~3.0", "symfony/class-loader": "~2.2", "symfony/framework-bundle": "~2.3", "symfony/process": "~2.2"}, "require-dev": {"symfony/form": "~2.2", "symfony/validator": "~2.2", "symfony/yaml": "~2.2"}, "suggest": {"symfony/form": "If you want to use the configurator", "symfony/validator": "If you want to use the configurator", "symfony/yaml": "If you want to use  the configurator"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-0": {"Sensio\\Bundle\\DistributionBundle": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Base bundle for Symfony Distributions", "keywords": ["configuration", "distribution"], "time": "2015-08-03 10:07:56"}, {"name": "sensio/framework-extra-bundle", "version": "v3.0.10", "source": {"type": "git", "url": "https://github.com/sensiolabs/SensioFrameworkExtraBundle.git", "reference": "18fc2063c4d6569cdca47a39fbac32342eb65f3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/SensioFrameworkExtraBundle/zipball/18fc2063c4d6569cdca47a39fbac32342eb65f3c", "reference": "18fc2063c4d6569cdca47a39fbac32342eb65f3c", "shasum": ""}, "require": {"doctrine/common": "~2.2", "symfony/framework-bundle": "~2.3"}, "require-dev": {"symfony/expression-language": "~2.4", "symfony/security-bundle": "~2.4"}, "suggest": {"symfony/expression-language": "", "symfony/psr-http-message-bridge": "To use the PSR-7 converters", "symfony/security-bundle": ""}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Sensio\\Bundle\\FrameworkExtraBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This bundle provides a way to configure your controllers with annotations", "keywords": ["annotations", "controllers"], "time": "2015-08-03 11:59:27"}, {"name": "sensiolabs/security-checker", "version": "v3.0.1", "source": {"type": "git", "url": "https://github.com/sensiolabs/security-checker.git", "reference": "7735fd97ff7303d9df776b8dbc970f949399abc9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/security-checker/zipball/7735fd97ff7303d9df776b8dbc970f949399abc9", "reference": "7735fd97ff7303d9df776b8dbc970f949399abc9", "shasum": ""}, "require": {"symfony/console": "~2.0"}, "bin": ["security-checker"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-0": {"SensioLabs\\Security": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A security checker for your composer.lock", "time": "2015-08-11 12:11:25"}, {"name": "swiftmailer/swiftmailer", "version": "v5.4.1", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "0697e6aa65c83edf97bb0f23d8763f94e3f11421"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/0697e6aa65c83edf97bb0f23d8763f94e3f11421", "reference": "0697e6aa65c83edf97bb0f23d8763f94e3f11421", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"mockery/mockery": "~0.9.1,<0.9.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "http://swiftmailer.org", "keywords": ["email", "mail", "mailer"], "time": "2015-06-06 14:19:39"}, {"name": "symfony/assetic-bundle", "version": "v2.7.0", "source": {"type": "git", "url": "https://github.com/symfony/assetic-bundle.git", "reference": "3ae5c8ca3079b6e0033cc9fbfb6500e2bc964da5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/assetic-bundle/zipball/3ae5c8ca3079b6e0033cc9fbfb6500e2bc964da5", "reference": "3ae5c8ca3079b6e0033cc9fbfb6500e2bc964da5", "shasum": ""}, "require": {"kriswallsmith/assetic": "~1.3", "php": ">=5.3.0", "symfony/console": "~2.3", "symfony/dependency-injection": "~2.3", "symfony/framework-bundle": "~2.3", "symfony/yaml": "~2.3"}, "conflict": {"kriswallsmith/spork": "<=0.2", "twig/twig": "<1.20"}, "require-dev": {"kriswallsmith/spork": "~0.3", "patchwork/jsqueeze": "~1.0", "symfony/class-loader": "~2.3", "symfony/css-selector": "~2.3", "symfony/dom-crawler": "~2.3", "symfony/phpunit-bridge": "~2.7", "symfony/twig-bundle": "~2.3"}, "suggest": {"kriswallsmith/spork": "to be able to dump assets in parallel", "symfony/twig-bundle": "to use the Twig integration"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\AsseticBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://kriswallsmith.net/"}], "description": "Integrates Assetic into Symfony2", "homepage": "https://github.com/symfony/AsseticBundle", "keywords": ["assets", "compression", "minification"], "time": "2015-09-01 00:05:29"}, {"name": "symfony/monolog-bundle", "version": "v2.7.1", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bundle.git", "reference": "9320b6863404c70ebe111e9040dab96f251de7ac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bundle/zipball/9320b6863404c70ebe111e9040dab96f251de7ac", "reference": "9320b6863404c70ebe111e9040dab96f251de7ac", "shasum": ""}, "require": {"monolog/monolog": "~1.8", "php": ">=5.3.2", "symfony/config": "~2.3", "symfony/dependency-injection": "~2.3", "symfony/http-kernel": "~2.3", "symfony/monolog-bridge": "~2.3"}, "require-dev": {"symfony/console": "~2.3", "symfony/yaml": "~2.3"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.7.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MonologBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony MonologBundle", "homepage": "http://symfony.com", "keywords": ["log", "logging"], "time": "2015-01-04 20:21:17"}, {"name": "symfony/swiftmailer-bundle", "version": "v2.3.8", "source": {"type": "git", "url": "https://github.com/symfony/swiftmailer-bundle.git", "reference": "970b13d01871207e81d17b17ddda025e7e21e797"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/swiftmailer-bundle/zipball/970b13d01871207e81d17b17ddda025e7e21e797", "reference": "970b13d01871207e81d17b17ddda025e7e21e797", "shasum": ""}, "require": {"php": ">=5.3.2", "swiftmailer/swiftmailer": ">=4.2.0,~5.0", "symfony/swiftmailer-bridge": "~2.1"}, "require-dev": {"symfony/config": "~2.1", "symfony/dependency-injection": "~2.1", "symfony/http-kernel": "~2.1", "symfony/yaml": "~2.1"}, "suggest": {"psr/log": "Allows logging"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\SwiftmailerBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony SwiftmailerBundle", "homepage": "http://symfony.com", "time": "2014-12-01 17:44:50"}, {"name": "symfony/symfony", "version": "v2.7.4", "source": {"type": "git", "url": "https://github.com/symfony/symfony.git", "reference": "1fdf23fe28876844b887b0e1935c9adda43ee645"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/symfony/zipball/1fdf23fe28876844b887b0e1935c9adda43ee645", "reference": "1fdf23fe28876844b887b0e1935c9adda43ee645", "shasum": ""}, "require": {"doctrine/common": "~2.3", "php": ">=5.3.9", "psr/log": "~1.0", "twig/twig": "~1.20|~2.0"}, "replace": {"symfony/asset": "self.version", "symfony/browser-kit": "self.version", "symfony/class-loader": "self.version", "symfony/config": "self.version", "symfony/console": "self.version", "symfony/css-selector": "self.version", "symfony/debug": "self.version", "symfony/debug-bundle": "self.version", "symfony/dependency-injection": "self.version", "symfony/doctrine-bridge": "self.version", "symfony/dom-crawler": "self.version", "symfony/event-dispatcher": "self.version", "symfony/expression-language": "self.version", "symfony/filesystem": "self.version", "symfony/finder": "self.version", "symfony/form": "self.version", "symfony/framework-bundle": "self.version", "symfony/http-foundation": "self.version", "symfony/http-kernel": "self.version", "symfony/intl": "self.version", "symfony/locale": "self.version", "symfony/monolog-bridge": "self.version", "symfony/options-resolver": "self.version", "symfony/process": "self.version", "symfony/property-access": "self.version", "symfony/proxy-manager-bridge": "self.version", "symfony/routing": "self.version", "symfony/security": "self.version", "symfony/security-acl": "self.version", "symfony/security-bundle": "self.version", "symfony/security-core": "self.version", "symfony/security-csrf": "self.version", "symfony/security-http": "self.version", "symfony/serializer": "self.version", "symfony/stopwatch": "self.version", "symfony/swiftmailer-bridge": "self.version", "symfony/templating": "self.version", "symfony/translation": "self.version", "symfony/twig-bridge": "self.version", "symfony/twig-bundle": "self.version", "symfony/validator": "self.version", "symfony/var-dumper": "self.version", "symfony/web-profiler-bundle": "self.version", "symfony/yaml": "self.version"}, "require-dev": {"doctrine/data-fixtures": "1.0.*", "doctrine/dbal": "~2.2", "doctrine/doctrine-bundle": "~1.2", "doctrine/orm": "~2.2,>=2.2.3", "egulias/email-validator": "~1.2", "ircmaxell/password-compat": "~1.0", "monolog/monolog": "~1.11", "ocramius/proxy-manager": "~0.4|~1.0", "symfony/phpunit-bridge": "self.version"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\Doctrine\\": "src/Symfony/Bridge/Doctrine/", "Symfony\\Bridge\\Monolog\\": "src/Symfony/Bridge/Monolog/", "Symfony\\Bridge\\ProxyManager\\": "src/Symfony/Bridge/ProxyManager/", "Symfony\\Bridge\\Swiftmailer\\": "src/Symfony/Bridge/Swiftmailer/", "Symfony\\Bridge\\Twig\\": "src/Symfony/Bridge/Twig/", "Symfony\\Bundle\\": "src/Symfony/Bundle/", "Symfony\\Component\\": "src/Symfony/Component/"}, "classmap": ["src/Symfony/Component/HttpFoundation/Resources/stubs", "src/Symfony/Component/Intl/Resources/stubs"], "files": ["src/Symfony/Component/Intl/Resources/stubs/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "The Symfony PHP framework", "homepage": "https://symfony.com", "keywords": ["framework"], "time": "2015-09-08 14:26:39"}, {"name": "twig/extensions", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/twigphp/Twig-extensions.git", "reference": "449e3c8a9ffad7c2479c7864557275a32b037499"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig-extensions/zipball/449e3c8a9ffad7c2479c7864557275a32b037499", "reference": "449e3c8a9ffad7c2479c7864557275a32b037499", "shasum": ""}, "require": {"twig/twig": "~1.20|~2.0"}, "require-dev": {"symfony/translation": "~2.3"}, "suggest": {"symfony/translation": "Allow the time_diff output to be translated"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"psr-0": {"Twig_Extensions_": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common additional features for Twig that do not directly belong in core", "homepage": "http://twig.sensiolabs.org/doc/extensions/index.html", "keywords": ["i18n", "text"], "time": "2015-08-22 16:38:35"}, {"name": "twig/twig", "version": "v1.22.1", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "b7fc2469fa009897871fb95b68237286fc54a5ad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/b7fc2469fa009897871fb95b68237286fc54a5ad", "reference": "b7fc2469fa009897871fb95b68237286fc54a5ad", "shasum": ""}, "require": {"php": ">=5.2.7"}, "require-dev": {"symfony/debug": "~2.7", "symfony/phpunit-bridge": "~2.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.22-dev"}}, "autoload": {"psr-0": {"Twig_": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}, {"name": "Twig Team", "homepage": "http://twig.sensiolabs.org/contributors", "role": "Contributors"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "http://twig.sensiolabs.org", "keywords": ["templating"], "time": "2015-09-15 06:50:16"}, {"name": "vich/uploader-bundle", "version": "v0.14.0", "source": {"type": "git", "url": "https://github.com/dustin10/VichUploaderBundle.git", "reference": "65d244d5e6ef62b4978585cdeb283b6cdab05f6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dustin10/VichUploaderBundle/zipball/65d244d5e6ef62b4978585cdeb283b6cdab05f6b", "reference": "65d244d5e6ef62b4978585cdeb283b6cdab05f6b", "shasum": ""}, "require": {"jms/metadata": "~1.5", "php": ">=5.3.2", "symfony/finder": ">=2.0", "symfony/framework-bundle": "~2.3", "symfony/property-access": "~2.3"}, "require-dev": {"doctrine/doctrine-bundle": "*", "doctrine/mongodb-odm": "@dev", "doctrine/orm": "*", "knplabs/knp-gaufrette-bundle": "*", "matthiasnoback/symfony-dependency-injection-test": "0.*", "mikey179/vfsstream": "~1.0", "oneup/flysystem-bundle": "dev-master", "phpunit/phpunit": "~4.0", "symfony/symfony": "*"}, "suggest": {"doctrine/doctrine-bundle": "*", "doctrine/mongodb-odm-bundle": "*", "doctrine/orm": ">=2.2.3", "doctrine/phpcr-odm": "~1.0", "knplabs/knp-gaufrette-bundle": "*", "symfony/yaml": "@stable", "willdurand/propel-eventdispatcher-bundle": ">=1.2"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Vich\\UploaderBundle\\": "", "Vich\\TestBundle\\": "Tests/Fixtures/App/src/TestBundle/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "d<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Ease file uploads attached to entities", "homepage": "https://github.com/dustin10/VichUploaderBundle", "keywords": ["file uploads", "upload"], "time": "2014-12-12 10:26:46"}, {"name": "<PERSON><PERSON><PERSON>/jsonp-callback-validator", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/willdurand/JsonpCallbackValidator.git", "reference": "1a7d388bb521959e612ef50c5c7b1691b097e909"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/willdurand/JsonpCallbackValidator/zipball/1a7d388bb521959e612ef50c5c7b1691b097e909", "reference": "1a7d388bb521959e612ef50c5c7b1691b097e909", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~3.7"}, "type": "library", "autoload": {"psr-0": {"JsonpCallbackValidator": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.willdurand.fr"}], "description": "JSONP callback validator.", "time": "2014-01-20 22:35:06"}, {"name": "will<PERSON><PERSON>/negotiation", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/willdurand/Negotiation.git", "reference": "8a84c5956e765f432542fc52a8c6e9aff4508eb3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/willdurand/Negotiation/zipball/8a84c5956e765f432542fc52a8c6e9aff4508eb3", "reference": "8a84c5956e765f432542fc52a8c6e9aff4508eb3", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"Negotiation\\": "src/Negotiation"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Content Negotiation tools for PHP provided as a standalone library.", "homepage": "http://williamdurand.fr/Negotiation/", "keywords": ["accept", "content", "format", "header", "negotiation"], "time": "2015-07-28 13:10:50"}], "packages-dev": [{"name": "sensio/generator-bundle", "version": "v2.5.3", "target-dir": "Sensio/Bundle/GeneratorBundle", "source": {"type": "git", "url": "https://github.com/sensiolabs/SensioGeneratorBundle.git", "reference": "e50108c2133ee5c9c484555faed50c17a61221d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/SensioGeneratorBundle/zipball/e50108c2133ee5c9c484555faed50c17a61221d3", "reference": "e50108c2133ee5c9c484555faed50c17a61221d3", "shasum": ""}, "require": {"symfony/console": "~2.5", "symfony/framework-bundle": "~2.2"}, "require-dev": {"doctrine/orm": "~2.2,>=2.2.3", "symfony/doctrine-bridge": "~2.2", "twig/twig": "~1.11"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.5.x-dev"}}, "autoload": {"psr-0": {"Sensio\\Bundle\\GeneratorBundle": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This bundle generates code for you", "time": "2015-03-17 06:36:52"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"friendsofsymfony/jsrouting-bundle": 0, "fredcido/phpsvg": 20, "glanchow/doctrine-fuzzy": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=5.3.9"}, "platform-dev": []}