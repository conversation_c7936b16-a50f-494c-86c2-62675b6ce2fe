<?php

namespace DBLS\Bundle\JerseyColoursBundle\Entity;

use Symfony\Component\HttpFoundation\File\File;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\Common\Collections\ArrayCollection;
use Vich\UploaderBundle\Mapping\Annotation as Vich;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Validator\Constraints as Assert;
use DBLS\Bundle\JerseyColoursBundle\Validator\Constraints as JerseyAssert;
use SamJ\DoctrineSluggableBundle\Slugger;
use SamJ\DoctrineSluggableBundle\SluggableInterface;

/**
 * Pattern
 *
 * @ORM\Table()
 * @JerseyAssert\Pattern(
 *   repositoryClass = "JerseyColoursBundle:Pattern",
 *   fields = {
 *      {"description", "This pattern exists for this element, please choose another description."}
 *   }
 * )
 * @ORM\Entity(repositoryClass="DBLS\Bundle\JerseyColoursBundle\Entity\PatternRepository")
 * @ORM\HasLifecycleCallbacks
 * @Vich\Uploadable
 */
class Pattern implements SluggableInterface
{

    /**
     * @var integer
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="description", type="string", length=255)
     */
    private $description;

    /**
     * @var string
     * 
     * @ORM\Column(name="pathSVG", type="string", length=255)
     */
    private $pathSVG;

    /**
     * @ORM\Column(type="datetime")
     *
     * @var \DateTime $updatedAt
     */
    protected $updatedAt;

    /**
     * @ORM\Column(type="string")
     */
    protected $slug;

    /**
     * 
     * @Vich\UploadableField(mapping="svg_pattern", fileNameProperty="pathSVG")
     * 
     * @JerseyAssert\FileExtension(
     *   errorMessage = "File extension '{{user_extension}}' is invalid. Allowed file extension(s) is(are): '{{allowed_extensions}}'",
     *   allowExtensions = {"svg"}
     * )
     * 
     *  @var File $imageFile
     */
    protected $imageFile;

    /**
     * @var string
     * 
     * @ORM\Column(name="sidePathSVG", type="string", length=255, nullable=True)
     */
    private $sidePathSVG;

    /**
     * 
     * @Vich\UploadableField(mapping="svg_pattern", fileNameProperty="sidePathSVG")
     * 
     * @JerseyAssert\FileExtension(
     *   errorMessage = "File extension '{{user_extension}}' is invalid. Allowed file extension(s) is(are): '{{allowed_extensions}}'",
     *   allowExtensions = {"svg"}
     * )
     * 
     *  @var File $sideImageFile
     */
    protected $sideImageFile;
    
    /**
     * @var boolean
     *
     * @ORM\Column(name="status", type="boolean")
     */
    private $status;

    /**
     * @ORM\ManyToOne(targetEntity="ElementType")
     * @ORM\JoinColumn(name="element_type_id", referencedColumnName="id")
     **/
   protected $element_type;

    /**
     * @ORM\OneToMany(targetEntity="JerseyElementPattern", mappedBy="pattern")
    */
    protected $jersey_element_pattern_associations;
    
    /**
     * @ORM\OneToMany(targetEntity="Synonym", mappedBy="pattern")
     */
    private $synonyms;

   public function __construct() {
        $this->jersey_element_pattern_associations = new ArrayCollection();
        $this->status = true;
        $this->updatedAt = new \DateTime("now");
    }

    /**
     * Get id
     *
     * @return integer 
     */
    public function getId() {
        return $this->id;
    }

    /**
     * Set description
     *
     * @param string $description
     * @return Pattern
     */
    public function setDescription($description) {
        $this->description = $description;

        return $this;
    }

    /**
     * Get description
     *
     * @return string 
     */
    public function getDescription() {
        return $this->description;
    }

    /**
     * Set pathSVG
     *
     * @param string $pathSVG
     * @return Pattern
     */
    public function setPathSVG($pathSVG) {
        $this->pathSVG = $pathSVG;

        return $this;
    }

    /**
     * Get pathSVG
     *
     * @return string 
     */
    public function getPathSVG() {
        return $this->pathSVG;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     * @return Pattern
     */
    public function setUpdatedAt($updatedAt) {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime 
     */
    public function getUpdatedAt() {
        return $this->updatedAt;
    }

    /**
     * Set status
     *
     * @param boolean $status
     * @return Pattern
     */
    public function setStatus($status) {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status
     *
     * @return boolean 
     */
    public function getStatus() {
        return $this->status;
    }

    /**
     * If manually uploading a file (i.e. not using Symfony Form) ensure an instance
     * of 'UploadedFile' is injected into this setter to trigger the  update. If this
     * bundle's configuration parameter 'inject_on_load' is set to 'true' this setter
     * must be able to accept an instance of 'File' as the bundle will inject one here
     * during Doctrine hydration.
     *
     * @param File|\Symfony\Component\HttpFoundation\File\UploadedFile $image
     */
    public function setImageFile(File $image = null) {
        $this->imageFile = $image;

        if ($image) {
            // It is required that at least one field changes if you are using doctrine
            // otherwise the event listeners won't be called and the file is lost
            $this->updatedAt = new \DateTime('now');
        }
    }

    /**
     * @return File
     */
    public function getImageFile() {
        return $this->imageFile;
    }

    /**
     * Set sidePathSVG
     *
     * @param string $sidePathSVG
     * @return Pattern
     */
    public function setSidePathSVG($sidePathSVG)
    {
        $this->sidePathSVG = $sidePathSVG;

        return $this;
    }

    /**
     * Get sidePathSVG
     *
     * @return string 
     */
    public function getSidePathSVG()
    {
        return $this->sidePathSVG;
    }
    
    /**
     * If manually uploading a file (i.e. not using Symfony Form) ensure an instance
     * of 'UploadedFile' is injected into this setter to trigger the  update. If this
     * bundle's configuration parameter 'inject_on_load' is set to 'true' this setter
     * must be able to accept an instance of 'File' as the bundle will inject one here
     * during Doctrine hydration.
     *
     * @param File|\Symfony\Component\HttpFoundation\File\UploadedFile $image
     */
    public function setSideImageFile(File $image = null) {
        $this->sideImageFile = $image;

        /*if ($image) {
            // It is required that at least one field changes if you are using doctrine
            // otherwise the event listeners won't be called and the file is lost
            $this->updatedAt = new \DateTime('now');
        }*/
    }

    /**
     * @return File
     */
    public function getSideImageFile() {
        return $this->sideImageFile;
    }

    

    /**
     * Set element_type
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\ElementType $elementType
     * @return Pattern
     */
    public function setElementType(\DBLS\Bundle\JerseyColoursBundle\Entity\ElementType $elementType = null)
    {
        $this->element_type = $elementType;

        return $this;
    }

    /**
     * Get element_type
     *
     * @return \DBLS\Bundle\JerseyColoursBundle\Entity\ElementType 
     */
    public function getElementType()
    {
        return $this->element_type;
    }
    
    /**
     * 
     * @return string
     */
    public function __toString()
    {
       $description = array( $this->getDescription(), $this->getElementType()->getName());
	   return implode( ' ', $description );
    }
    

    /**
     * Add jersey_element_pattern_associations
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElementPattern $jerseyElementPatternAssociations
     * @return Pattern
     */
    public function addJerseyElementPatternAssociation(\DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElementPattern $jerseyElementPatternAssociations)
    {
        $this->jersey_element_pattern_associations[] = $jerseyElementPatternAssociations;

        return $this;
    }

    /**
     * Remove jersey_element_pattern_associations
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElementPattern $jerseyElementPatternAssociations
     */
    public function removeJerseyElementPatternAssociation(\DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElementPattern $jerseyElementPatternAssociations)
    {
        $this->jersey_element_pattern_associations->removeElement($jerseyElementPatternAssociations);
    }

    /**
     * Get jersey_element_pattern_associations
     *
     * @return \Doctrine\Common\Collections\Collection 
     */
    public function getJerseyElementPatternAssociations()
    {
        return $this->jersey_element_pattern_associations;
    }

    /**
    * @return string
    */
    public function getSlug()
    {
        return $this->slug;
    }

    /**
    * @return string
    */
    public function buildSlug()
    {
        $slug = $this->getSlug();
        if ( !$slug ) {

            $slugger = new Slugger('-', '-');
            $fields = $this->getSlugFields();
            $slug = $slugger->getSlug($fields);
            $this->slug = $slug;
        }

        return $slug;
    }

    /**
    * @param string
    */
    public function setSlug($slug)
    {
        if (!empty($this->slug)) return false;
        $this->slug = $slug;
    }

    /**
    * @return array
    */
    public function getSlugFields() 
    {
        $elementName = null;
        if ( null != $this->getElementType() )
            $elementName = $this->getElementType()->getName();

        return array( $this->getDescription(), $elementName);
    }

    /**
    * @return string
    */
    public function getFrontViewPath($fallback = true)
    {
        $path = implode(
                DIRECTORY_SEPARATOR, 
                array($this->getSlug(), $this->getPathSVG())
            );
        
        $file = "images/patterns/" . $path;
        
        if( !$fallback || (file_exists( $file ) && filesize( $file ) > 0 ))
            return $file;
        else
            return 'images/no-image.svg';
    }

    /**
     *
     * @return string
     */
    public function getSideViewPath($fallback = true)
    {
        $path = implode(
                DIRECTORY_SEPARATOR, 
                array($this->getSlug(), $this->getSidePathSVG())
            );
        
        $file = "images/patterns/" . $path;
        
        if( !$fallback || ( file_exists( $file ) && filesize( $file ) > 0 ) )
            return $file;
        else
            return 'images/no-image.svg';
    }
    

    /**
     * Add synonyms
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Synonym $synonyms
     * @return Pattern
     */
    public function addSynonym(\DBLS\Bundle\JerseyColoursBundle\Entity\Synonym $synonyms)
    {
        $this->synonyms[] = $synonyms;

        return $this;
    }

    /**
     * Remove synonyms
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Synonym $synonyms
     */
    public function removeSynonym(\DBLS\Bundle\JerseyColoursBundle\Entity\Synonym $synonyms)
    {
        $this->synonyms->removeElement($synonyms);
    }

    /**
     * Get synonyms
     *
     * @return \Doctrine\Common\Collections\Collection 
     */
    public function getSynonyms()
    {
        return $this->synonyms;
    }
}
