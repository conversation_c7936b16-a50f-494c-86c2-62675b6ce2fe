{"mappings": ";;;;AAAA;;;;;;;;;;CAUC;;;AAuCD,MAAM,0CAAoB;AAMnB,SAAS,0CAAa,KAAqB;IAChD,IAAI,cACF,UAAU,oBACV,gBAAgB,kBAChB,cAAc,eACd,WAAW,aACX,YAAY,mEACZ,wBAAwB,EACzB,GAAG;IAEJ,MAAM,UAAU,CAAA,GAAA,aAAK,EAA6C;IAClE,IAAI,qBAAC,iBAAiB,wBAAE,oBAAoB,EAAC,GAAG,CAAA,GAAA,yBAAiB;IAEjE,IAAI,cAAC,UAAU,EAAC,GAAG,CAAA,GAAA,yCAAO,EAAE;oBAC1B;QACA,cAAa,CAAC;YACZ,EAAE,mBAAmB;YACrB,IAAI,EAAE,WAAW,KAAK,WAAW,EAAE,WAAW,KAAK,SAAS;gBAC1D,IAAI,kBACF,iBAAiB;oBACf,GAAG,CAAC;oBACJ,MAAM;gBACR;gBAGF,QAAQ,OAAO,GAAG,WAAW;oBAC3B,iEAAiE;oBACjE,EAAE,MAAM,CAAC,aAAa,CAAC,IAAI,aAAa,iBAAiB;wBAAC,SAAS;oBAAI;oBAEvE,sFAAsF;oBACtF,IAAI,CAAA,GAAA,uBAAe,EAAE,EAAE,MAAM,EAAE,aAAa,KAAK,EAAE,MAAM,EACvD,CAAA,GAAA,4BAAoB,EAAE,EAAE,MAAM;oBAGhC,IAAI,aACF,YAAY;wBACV,GAAG,CAAC;wBACJ,MAAM;oBACR;oBAEF,QAAQ,OAAO,GAAG;gBACpB,GAAG;gBAEH,2EAA2E;gBAC3E,IAAI,EAAE,WAAW,KAAK,SAAS;oBAC7B,IAAI,gBAAgB,CAAA;wBAClB,EAAE,cAAc;oBAClB;oBAEA,kBAAkB,EAAE,MAAM,EAAE,eAAe,eAAe;wBAAC,MAAM;oBAAI;oBACrE,kBAAkB,QAAQ,aAAa;wBACrC,+EAA+E;wBAC/E,wEAAwE;wBACxE,WAAW;4BACT,qBAAqB,EAAE,MAAM,EAAE,eAAe;wBAChD,GAAG;oBACL,GAAG;wBAAC,MAAM;oBAAI;gBAChB;YACF;QACF;QACA,YAAW,CAAC;YACV,IAAI,QAAQ,OAAO,EACjB,aAAa,QAAQ,OAAO;YAG9B,IAAI,kBAAmB,CAAA,EAAE,WAAW,KAAK,WAAW,EAAE,WAAW,KAAK,OAAM,GAC1E,eAAe;gBACb,GAAG,CAAC;gBACJ,MAAM;YACR;QAEJ;IACF;IAEA,IAAI,mBAAmB,CAAA,GAAA,qBAAa,EAAE,eAAe,CAAC,aAAa,2BAA2B;IAE9F,OAAO;QACL,gBAAgB,CAAA,GAAA,iBAAS,EAAE,YAAY;IACzC;AACF", "sources": ["packages/@react-aria/interactions/src/useLongPress.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, FocusableElement, LongPressEvent} from '@react-types/shared';\nimport {focusWithoutScrolling, getOwnerDocument, mergeProps, useDescription, useGlobalListeners} from '@react-aria/utils';\nimport {usePress} from './usePress';\nimport {useRef} from 'react';\n\nexport interface LongPressProps {\n  /** Whether long press events should be disabled. */\n  isDisabled?: boolean,\n  /** Handler that is called when a long press interaction starts. */\n  onLongPressStart?: (e: LongPressEvent) => void,\n  /**\n   * Handler that is called when a long press interaction ends, either\n   * over the target or when the pointer leaves the target.\n   */\n  onLongPressEnd?: (e: LongPressEvent) => void,\n  /**\n   * Handler that is called when the threshold time is met while\n   * the press is over the target.\n   */\n  onLongPress?: (e: LongPressEvent) => void,\n  /**\n   * The amount of time in milliseconds to wait before triggering a long press.\n   * @default 500ms\n   */\n  threshold?: number,\n  /**\n   * A description for assistive techology users indicating that a long press\n   * action is available, e.g. \"Long press to open menu\".\n   */\n  accessibilityDescription?: string\n}\n\nexport interface LongPressResult {\n  /** Props to spread on the target element. */\n  longPressProps: DOMAttributes\n}\n\nconst DEFAULT_THRESHOLD = 500;\n\n/**\n * Handles long press interactions across mouse and touch devices. Supports a customizable time threshold,\n * accessibility description, and normalizes behavior across browsers and devices.\n */\nexport function useLongPress(props: LongPressProps): LongPressResult {\n  let {\n    isDisabled,\n    onLongPressStart,\n    onLongPressEnd,\n    onLongPress,\n    threshold = DEFAULT_THRESHOLD,\n    accessibilityDescription\n  } = props;\n\n  const timeRef = useRef<ReturnType<typeof setTimeout> | undefined>(undefined);\n  let {addGlobalListener, removeGlobalListener} = useGlobalListeners();\n\n  let {pressProps} = usePress({\n    isDisabled,\n    onPressStart(e) {\n      e.continuePropagation();\n      if (e.pointerType === 'mouse' || e.pointerType === 'touch') {\n        if (onLongPressStart) {\n          onLongPressStart({\n            ...e,\n            type: 'longpressstart'\n          });\n        }\n\n        timeRef.current = setTimeout(() => {\n          // Prevent other usePress handlers from also handling this event.\n          e.target.dispatchEvent(new PointerEvent('pointercancel', {bubbles: true}));\n\n          // Ensure target is focused. On touch devices, browsers typically focus on pointer up.\n          if (getOwnerDocument(e.target).activeElement !== e.target) {\n            focusWithoutScrolling(e.target as FocusableElement);\n          }\n\n          if (onLongPress) {\n            onLongPress({\n              ...e,\n              type: 'longpress'\n            });\n          }\n          timeRef.current = undefined;\n        }, threshold);\n\n        // Prevent context menu, which may be opened on long press on touch devices\n        if (e.pointerType === 'touch') {\n          let onContextMenu = e => {\n            e.preventDefault();\n          };\n\n          addGlobalListener(e.target, 'contextmenu', onContextMenu, {once: true});\n          addGlobalListener(window, 'pointerup', () => {\n            // If no contextmenu event is fired quickly after pointerup, remove the handler\n            // so future context menu events outside a long press are not prevented.\n            setTimeout(() => {\n              removeGlobalListener(e.target, 'contextmenu', onContextMenu);\n            }, 30);\n          }, {once: true});\n        }\n      }\n    },\n    onPressEnd(e) {\n      if (timeRef.current) {\n        clearTimeout(timeRef.current);\n      }\n\n      if (onLongPressEnd && (e.pointerType === 'mouse' || e.pointerType === 'touch')) {\n        onLongPressEnd({\n          ...e,\n          type: 'longpressend'\n        });\n      }\n    }\n  });\n\n  let descriptionProps = useDescription(onLongPress && !isDisabled ? accessibilityDescription : undefined);\n\n  return {\n    longPressProps: mergeProps(pressProps, descriptionProps)\n  };\n}\n"], "names": [], "version": 3, "file": "useLongPress.module.js.map"}