<?php

namespace DBLS\Bundle\JerseyColoursBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class DictionaryImportCommand extends ContainerAwareCommand
{
    protected $container;

    protected $em;

    protected $serviceDictionary;

    protected $output;

    protected $input;

    /**
     * 
     */
    protected function configure()
    {
        $this->setName('jersey:dictionary:import')
             ->setDescription('Import the existing terms in the database to the dictionary');
    }

    /**
     * 
     * @param  InputInterface  $input
     * @param  OutputInterface $output
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->container = $this->getContainer();
        $this->em = $this->container->get('doctrine.orm.entity_manager');
        $this->serviceDictionary = $this->container->get('services.dictionary');

        $this->output = $output;
        $this->input = $input;

        $this->_importContentsToDictionary();
    }

    /**
     * 
     */
    protected function _importContentsToDictionary()
    {
        try {

            $this->_clearTerms();

            $this->_importColoursToDictionary();
            $this->_importPatternsToDictionary();
            $this->_importElementTypesToDictionary();
            $this->_importSynonymsToDictionary();

        } catch ( \Exception $e ) {
            $this->output->writeln('Error importing terms to dictionary: ' . $e->getMessage());
        }
    }

    /**
     * 
     */
    protected function _clearTerms()
    {
        $this->serviceDictionary->clearColours();
        $this->serviceDictionary->clearPatterns();
        $this->serviceDictionary->clearElementTypes();
        $this->serviceDictionary->clearSynonyms();
    }

    /**
     * 
     */
    protected function _importColoursToDictionary()
    {
        $colours = $this->em->getRepository('JerseyColoursBundle:Colour')->findAll();
        foreach ($colours as $colour)
            $this->serviceDictionary->saveColour($colour);
    }

    /**
     * 
     */
    protected function _importPatternsToDictionary()
    {
        $patterns = $this->em->getRepository('JerseyColoursBundle:Pattern')->findAll();
        foreach ($patterns as $pattern)
            $this->serviceDictionary->savePattern($pattern);
    }

    /**
     * 
     */
    protected function _importElementTypesToDictionary()
    {
        $elementTypes = $this->em->getRepository('JerseyColoursBundle:ElementType')->findAll();
        foreach ($elementTypes as $elementType)
            $this->serviceDictionary->saveElement($elementType);
    }

    /**
     * 
     */
    protected function _importSynonymsToDictionary()
    {
        $synonyms = $this->em->getRepository('JerseyColoursBundle:Synonym')->findAll();
        foreach ($synonyms as $synonym)
            $this->serviceDictionary->saveSynonym($synonym);
    }
}