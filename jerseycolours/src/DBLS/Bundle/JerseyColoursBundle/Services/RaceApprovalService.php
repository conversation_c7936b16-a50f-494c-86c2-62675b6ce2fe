<?php

namespace DBLS\Bundle\JerseyColoursBundle\Services;

use Doctrine\ORM\EntityManager;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Util\Debug;
use DBLS\Bundle\JerseyColoursBundle\Entity\Race;
use Symfony\Component\DependencyInjection\ContainerAware;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Finder\Finder;

class RaceApprovalService extends ContainerAware
{

    /**
     * Holds an instance of database connection
     * @var EntityManager
     */
    private $em = null;

    /**
     *
     * @var array
     */
    private $_approvedJerseys = array();


    /**
     *
     * @var Race
     */
    private $_race;


    /**
     * @Filesystem
     */
    private $fs;

    public function __construct(EntityManager $em)
    {
        $this->em = $em;
        $this->fs = new Filesystem();
    }

    public function approveRace(Race $race)
    {
        $this->_race = $race;

        if ( $race->hasError() ) {
            return false;
        }

        // set all jerseys as approved
        $this->_setJerseyStatus($race, true);
        
        // move all jerseys to the final path
        $this->moveJerseysToFinalPath($race);
        
        // save changes
        $this->em->flush();
        
        return true;
    }
    
    /**
     * Move all Race's jersey to the final path
     *
     * @param Race $race
     */
    public function moveJerseysToFinalPath(Race $race, $draft = true)
    {
        $jerseyGenerator = $this->container->get('services.jersey_generator');
        $silksDir = realpath($jerseyGenerator->getJerseysDir());
        $draftDir = realpath(sprintf('%s/../draft', $silksDir));

        $baseRaceDir = $draft ? $draftDir : $silksDir;

        $draftRaceDir = realpath(sprintf('%s/%s', $baseRaceDir, $race->getFolderPath()));
        
        $infos = array(
            'race_date'     => $race->getMeeting()->getDate()->format('Y_m_d'),
            'meeting_name'  => $race->getMeeting()->getCleanFolder(),
            'race_id'       => $race->getNumber(),
            'race_name'     => $race->getCleanFolder(),
            'silks_dir'     => $silksDir
        );
        
        $pageMastersDir = $this->_getPageMastersDir($infos, true);
        $aapDir = $this->_getAAPDir($infos, true);
        $wanDir = $this->_getWANDir($infos, true);
        $pngDir = $this->_getPNGDir($infos, true);

        /*$pngDir = $aapDir . DIRECTORY_SEPARATOR . 'png';
        if (!$this->fs->exists($pngDir)) {
            $this->fs->mkdir($pngDir);
        }*/
        
        //
        //-- First, move silks from draft to PageMasters
        //

        $svgUtils = $this->container->get('services.jpeg.generator');

        if ( !is_dir($draftRaceDir) )
            return true;
        
        // Move all the jerseys folder in the temporary race dir to the final dir
        $tmpDirectoryIterator = new \FilesystemIterator($draftRaceDir, \FilesystemIterator::SKIP_DOTS);

        $silksJpeg = array();
        foreach ( $tmpDirectoryIterator as $jerseyTmpDir ) {

            if ( !$jerseyTmpDir->isDir() )
                continue;

            $jerseyBaseName = $jerseyTmpDir->getBasename();

            $newFileFront = $this->_getNewFileName(sprintf('%s/%s', $pageMastersDir, $jerseyBaseName . '_front.svg'));
            $newFileSide = $this->_getNewFileName(sprintf('%s/%s', $pageMastersDir, $jerseyBaseName . '_side.svg'));

            if ($this->fs->exists($jerseyTmpDir->getPathname() . '/front.svg')) {
                $this->fs->rename($jerseyTmpDir->getPathname() . '/front.svg', $newFileFront);
                
                $jpegFile = $aapDir . DIRECTORY_SEPARATOR . $jerseyBaseName . '_front.jpg';
                $svgUtils->toJpeg($newFileFront, $jpegFile);

                $pngFile = $pngDir . DIRECTORY_SEPARATOR . $jerseyBaseName . '_front.png';
                $svgUtils->toPng($newFileFront, $pngFile);

                $silksJpeg[] = $jpegFile;
            }
            
            if ($this->fs->exists($jerseyTmpDir->getPathname() . '/side.svg')) {
                $this->fs->rename($jerseyTmpDir->getPathname() . '/side.svg', $newFileSide);
                
                $jpegFile = $aapDir . DIRECTORY_SEPARATOR . $jerseyBaseName . '_side.jpg';
                $svgUtils->toJpeg($newFileSide, $jpegFile);

                $pngFile = $pngDir . DIRECTORY_SEPARATOR . $jerseyBaseName . '_side.png';
                $svgUtils->toPng($newFileSide, $pngFile);
            }
        }

        $this->_generateJpeg($wanDir);
        
        //
        //-- Finally, clean up empty folders
        //
        $folderRemover = $this->container->get('services.utils.remove_folders');
        $folderRemover->clean($draftDir);

        return true;
    }

   
    /**
     *
     * @param  string $folder
     */
    private function _generateJpeg($folder)
    {
        $jpegGenerator = $this->container->get('services.jpeg.generator');

        $pdfFile = $this->_race->getPdfName();
        $finalFile = $folder . $pdfFile;

        $jpegGenerator->outputFile($this->_race, $this->_approvedJerseys, $finalFile);
    }

    /**
     *
     * @param  [type] $newFileName [description]
     * @return [type]              [description]
     */
    private function _getNewFileName($newFileName)
    {
        // if the jersey folder already exists in the the target race folder
        if ( $this->fs->exists($newFileName) ) {
            $this->fs->remove($newFileName);
        }
        
        return $newFileName;
    }
    
    /**
     * Returns Page Masters dir
     *
     * @param type $infos
     * @param type $createIfNotExists
     */
    private function _getPageMastersDir($infos, $createIfNotExists = false) {
        $pageMasterDir = sprintf(
                "%s/page-masters/%s/%s/%s",
                $infos['silks_dir'],
                $infos['race_date'],
                $infos['meeting_name'],
                $infos['race_id'] . '_' . $infos['race_name']
        );
        
        // Check if the race folder already exists
        if ( $createIfNotExists && (!$this->fs->exists($pageMasterDir)) ) {
            $this->fs->mkdir($pageMasterDir);
        }
        
        return $pageMasterDir;
    }
    
    /**
     * Returns APP dir
     *
     * @param type $infos
     * @param type $createIfNotExists
     */
    private function _getAAPDir($infos, $createIfNotExists = false) {
        $pageMasterDir = sprintf(
                "%s/aap/%s/%s/%s",
                $infos['silks_dir'],
                $infos['race_date'],
                $infos['meeting_name'],
                $infos['race_id'] . '_' . $infos['race_name']
                
        );
        
        // Check if the race folder already exists
        if ( $createIfNotExists && (!$this->fs->exists($pageMasterDir)) ) {
            $this->fs->mkdir($pageMasterDir);
        }
        
        return $pageMasterDir;
    }

    /**
     * Returns APP dir
     *
     * @param type $infos
     * @param type $createIfNotExists
     */
    private function _getPNGDir($infos, $createIfNotExists = false) {
        $pngDir = sprintf(
                "%s/png/%s/%s/%s",
                $infos['silks_dir'],
                $infos['race_date'],
                $infos['meeting_name'],
                $infos['race_id'] . '_' . $infos['race_name']
                
        );
        
        // Check if the race folder already exists
        if ( $createIfNotExists && (!$this->fs->exists($pngDir)) ) {
            $this->fs->mkdir($pngDir);
        }
        
        return $pngDir;
    }

    /**
     * Returns WAN dir
     *
     * @param type $infos
     * @param type $createIfNotExists
     */
    private function _getWANDir($infos, $createIfNotExists = false) {
        $pageMasterDir = sprintf(
                "%s/wan/%s/%s/",
                $infos['silks_dir'],
                $infos['race_date'],
                $infos['meeting_name']
                
        );
        
        // Check if the race folder already exists
        if ( $createIfNotExists && (!$this->fs->exists($pageMasterDir)) ) {
            $this->fs->mkdir($pageMasterDir);
        }
        
        return $pageMasterDir;
    }
    
    
    /**
     * Set status on Race's jersey
     *
     * @param Race $race
     * @param boolean $status
     */
    private function _setJerseyStatus(Race $race, $status)
    {
        foreach ( $race->getUnapprovedJerseys() as $jersey ) {
            
            $this->_approvedJerseys[] = $jersey;

            $jersey->setStatus($status);
            $this->em->persist($jersey);
        }
    }
    
}
