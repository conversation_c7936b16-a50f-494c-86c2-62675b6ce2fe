<?php

namespace DBLS\Bundle\JerseyColoursBundle\Menu;

use Knp\Menu\FactoryInterface;
use Symfony\Component\DependencyInjection\ContainerAware;

class Builder extends ContainerAware
{
    
    /**
     * 
     * @param \Knp\Menu\FactoryInterface $factory
     * @param array $options
     * @return type
     */
    public function mainMenu(FactoryInterface $factory, array $options)
    {
        $menu = $factory->createItem('root');

        $menu->addChild('Dashboard', array('route' => 'homepage', 'extras' => array( 'icon' => 'home' )));

        $user   = $menu->addChild('User', array('route' => 'user', 'extras' => array( 'icon' => 'user' ) ));
        $colour = $menu->addChild('Colour', array('route' => 'colour', 'extras' => array( 'icon' => 'bullseye' )));
        $pattern = $menu->addChild('Pattern', array('route' => 'pattern', 'extras' => array( 'icon' => 'puzzle-piece' )));
        $jersey = $menu->addChild('Silk', array('route' => 'jersey_meeting', 'extras' => array( 'icon' => 'gears' )));

        $user->addChild('List users', array('route' => 'user'));
        $user->addChild('Add user', array('route' => 'user_new'));

        $colour->addChild('List colours', array('route' => 'colour'));
        $colour->addChild('Add colour', array('route' => 'colour_new'));

        $pattern->addChild('List patterns', array('route' => 'pattern'));
        $pattern->addChild('Add pattern', array('route' => 'pattern_new'));

        //$jersey->addChild('Processing', array('route' => 'jersey'));
        //$jersey->addChild('Approved', array('route' => 'jersey_approved'));
        
        $menu->addChild('Error Report', array('route' => 'error', 'extras' => array( 'icon' => 'exclamation-triangle' )));
        
        $menu->addChild('Synonym', array('route' => 'synonym', 'extras' => array( 'icon' => 'plus-square' )));

        return $menu;
    }
}
