import React from 'react';
import { useParams } from 'react-router-dom';

export const JerseyDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  return (
    <div>
      <h1 className="text-xl font-semibold text-gray-900">Jersey Detail</h1>
      <p className="mt-2 text-sm text-gray-700">
        Viewing jersey with ID: {id}
      </p>
      
      <div className="mt-8 bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <p className="text-gray-500">Jersey detail view coming soon...</p>
        </div>
      </div>
    </div>
  );
};
