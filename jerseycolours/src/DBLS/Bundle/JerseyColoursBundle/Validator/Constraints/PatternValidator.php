<?php

namespace DBLS\Bundle\JerseyColoursBundle\Validator\Constraints;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;

/**
 * Prevents duplicate entries in a column.
 *
 * <AUTHOR>
 */
class PatternValidator extends ConstraintValidator {

    /**
     * Holds an instance of database connection
     * @var \Doctrine\ORM\EntityManager
     */
    private $em = null;

    public function __construct(\Doctrine\ORM\EntityManager $em) {
        $this->em = $em;
    }

    public function validate($entity, Constraint $constraint) {
        
        $repo = $this->em->getRepository($constraint->repositoryClass);
        
        if (!$repo) {
            throw new Exception(sprintf("Repository '%s' not found", $constraint->repositoryClass));
        }

        if (!is_null($constraint->fields)) {
                
            if (is_null($entity->getDescription())) {
                throw new \Exception(sprintf("Incorrect use of Pattern Validator. It does not have a field name."));
            }

            $fieldMessage = $constraint->fields[0][1];
            if ($fieldMessage == '') {
                throw new \Exception(sprintf("Incorrect use of Pattern Validator. It does not have an error message."));
            }

            $query = $this->createQuery($repo, $entity);
            $r = $query->getResult();
            if (count($r) > 0) {
                /* Raise an error for current field */
                $this->context->buildViolation($fieldMessage)
                        ->atPath($constraint->fields[0][0])
                        ->addViolation();
            }
            
        }

    }
    
    protected function createQuery($repo, $entity) {
        
        $id = '';
        if (method_exists($entity, 'getId')) {
            $id = $entity->getId();
        }

        if ($id == '' || $id == null) {
            $id = 0;
        }
        
        $description = $entity->getDescription();
        $elementType = $entity->getElementType()->getId();

        $qb = $repo->createQueryBuilder('t');
        $qb->where($qb->expr()->andX(
                    $qb->expr()->neq('t.id', ':pId'), 
                    $qb->expr()->eq($qb->expr()->lower('t.description'), ':pDescription'), 
                    $qb->expr()->eq('t.element_type', ':pElementType')
                ))
                ->setParameter('pId', $id)
                ->setParameter('pDescription', strtolower(trim($description)))
                ->setParameter('pElementType', $elementType);

        $query = $qb->getQuery();
        return $query;
    }

}
