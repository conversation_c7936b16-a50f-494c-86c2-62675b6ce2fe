class CreateJerseys < ActiveRecord::Migration[8.0]
  def change
    create_table :jerseys do |t|
      t.string :name
      t.string :description
      t.text :string_value
      t.boolean :status
      t.boolean :parsed
      t.boolean :parsing
      t.datetime :parsing_time
      t.string :external_jersey_id
      t.integer :number
      t.references :race, null: false, foreign_key: true
      t.references :trainer, null: false, foreign_key: true

      t.timestamps
    end
  end
end
