{"mappings": ";;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;AAyBM,SAAS,0CAAiC,GAAG,IAAO;IACzD,oFAAoF;IACpF,uDAAuD;IACvD,IAAI,SAAgB;QAAC,GAAG,IAAI,CAAC,EAAE;IAAA;IAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,IAAI,QAAQ,IAAI,CAAC,EAAE;QACnB,IAAK,IAAI,OAAO,MAAO;YACrB,IAAI,IAAI,MAAM,CAAC,IAAI;YACnB,IAAI,IAAI,KAAK,CAAC,IAAI;YAElB,eAAe;YACf,IACE,OAAO,MAAM,cACb,OAAO,MAAM,cACb,qCAAqC;YACrC,GAAG,CAAC,EAAE,KAAK,OACX,GAAG,CAAC,EAAE,KAAK,OACX,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,MAC/B,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,IAE/B,MAAM,CAAC,IAAI,GAAG,CAAA,GAAA,+BAAI,EAAE,GAAG;iBAGlB,IACL,AAAC,CAAA,QAAQ,eAAe,QAAQ,kBAAiB,KACjD,OAAO,MAAM,YACb,OAAO,MAAM,UAEb,MAAM,CAAC,IAAI,GAAG,CAAA,GAAA,qCAAG,EAAE,GAAG;iBACjB,IAAI,QAAQ,QAAQ,KAAK,GAC9B,OAAO,EAAE,GAAG,CAAA,GAAA,kCAAO,EAAE,GAAG;iBAGxB,MAAM,CAAC,IAAI,GAAG,MAAM,YAAY,IAAI;QAExC;IACF;IAEA,OAAO;AACT", "sources": ["packages/@react-aria/utils/src/mergeProps.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {chain} from './chain';\nimport clsx from 'clsx';\nimport {mergeIds} from './useId';\n\ninterface Props {\n  [key: string]: any\n}\n\ntype PropsArg = Props | null | undefined;\n\n// taken from: https://stackoverflow.com/questions/51603250/typescript-3-parameter-list-intersection-type/51604379#51604379\ntype TupleTypes<T> = { [P in keyof T]: T[P] } extends { [key: number]: infer V } ? NullToObject<V> : never;\ntype NullToObject<T> = T extends (null | undefined) ? {} : T;\n\ntype UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends ((k: infer I) => void) ? I : never;\n\n/**\n * Merges multiple props objects together. Event handlers are chained,\n * classNames are combined, and ids are deduplicated - different ids\n * will trigger a side-effect and re-render components hooked up with `useId`.\n * For all other props, the last prop object overrides all previous ones.\n * @param args - Multiple sets of props to merge together.\n */\nexport function mergeProps<T extends PropsArg[]>(...args: T): UnionToIntersection<TupleTypes<T>> {\n  // Start with a base clone of the first argument. This is a lot faster than starting\n  // with an empty object and adding properties as we go.\n  let result: Props = {...args[0]};\n  for (let i = 1; i < args.length; i++) {\n    let props = args[i];\n    for (let key in props) {\n      let a = result[key];\n      let b = props[key];\n\n      // Chain events\n      if (\n        typeof a === 'function' &&\n        typeof b === 'function' &&\n        // This is a lot faster than a regex.\n        key[0] === 'o' &&\n        key[1] === 'n' &&\n        key.charCodeAt(2) >= /* 'A' */ 65 &&\n        key.charCodeAt(2) <= /* 'Z' */ 90\n      ) {\n        result[key] = chain(a, b);\n\n        // Merge classnames, sometimes classNames are empty string which eval to false, so we just need to do a type check\n      } else if (\n        (key === 'className' || key === 'UNSAFE_className') &&\n        typeof a === 'string' &&\n        typeof b === 'string'\n      ) {\n        result[key] = clsx(a, b);\n      } else if (key === 'id' && a && b) {\n        result.id = mergeIds(a, b);\n        // Override others\n      } else {\n        result[key] = b !== undefined ? b : a;\n      }\n    }\n  }\n\n  return result as UnionToIntersection<TupleTypes<T>>;\n}\n"], "names": [], "version": 3, "file": "mergeProps.main.js.map"}