class Race < ApplicationRecord
  belongs_to :meeting
  has_many :jerseys, dependent: :destroy

  validates :name, presence: true
  validates :external_race_id, presence: true, uniqueness: { scope: :meeting_id }
  validates :external_meet_id, presence: true
  validates :number, presence: true, uniqueness: { scope: :meeting_id }
  validates :racetime, presence: true

  scope :by_meeting, ->(meeting) { where(meeting: meeting) }
  scope :by_date, ->(date) { joins(:meeting).where(meetings: { date: date }) }
  scope :ordered, -> { order(:number) }

  def to_s
    "Race #{number}: #{name}"
  end
end
