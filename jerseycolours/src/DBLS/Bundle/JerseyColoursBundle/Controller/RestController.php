<?php

namespace DBLS\Bundle\JerseyColoursBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\View\View as RestView;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use DBLS\Bundle\JerseyColoursBundle\Adapters\GenericAdapter;
use FOS\RestBundle\Controller\FOSRestController;

/**
 * Meeting controller.
 *
 * @Route("/api")
 */
class RestController extends FOSRestController
{

    /**
     * Approve the meeting
     *
     * @Route("/import", name="meeting_import")
     * @Method("POST")
     * @Rest\View(statusCode=201)
     */
    public function importAction(Request $request)
    {
        try {

            $json = $request->getContent();
            $data = json_decode($json);

            $adapter = new GenericAdapter();
            $adapter->setData($data);

            $service = $this->container->get('services.importer');        
            $service->setAdapter($adapter);
            $digest = $service->processCreate();

            $stats = array(
                'created' => true,
                'stats'   => $digest
            );    

            $response = new JsonResponse($stats, Response::HTTP_CREATED);

        } catch (\Exception $e) {

            $return = array(
                'type'  => 'importing_error',
                'title' => 'There as an error in the importing process'
            );

            $response = new JsonResponse($return, Response::HTTP_INTERNAL_SERVER_ERROR);

            throw $e;
        }
        
        return $response;
    }
}
