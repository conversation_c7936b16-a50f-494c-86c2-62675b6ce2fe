<?php
namespace DBLS\Bundle\JerseyColoursBundle\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Template;

use DBLS\Bundle\JerseyColoursBundle\Entity\Synonym;
use DBLS\Bundle\JerseyColoursBundle\Form\SynonymType;

/**
 * Synonym controller.
 *
 * @Route("/synonym")
 */
class SynonymController extends Controller
{
    
    /**
     * Lists all Synonym entities.
     *
     * @Route("/", name="synonym", options={"expose"=true})
     * @Method("GET")
     * @Template("JerseyColoursBundle:Synonym:index.html.twig")
     */
    public function indexAction()
    {
        $em       = $this->getDoctrine()->getManager();
        
        $synonyms = $em->getRepository( 'JerseyColoursBundle:Synonym' )->findAll();
        
        return array(
            'synonyms'        => $synonyms,
        );
    }
    
    /**
     * Displays a form to create a new Synonym entity.
     *
     * @Route("/new", name="synonym_new", options={"expose"=true})
     * @Method("GET")
     * @Template("JerseyColoursBundle:Synonym:form.html.twig")
     */
    public function newAction()
    {
        $entity = new Synonym();
        $form   = $this->createSynonymForm( $entity );
        
        return array(
            'entity'        => $entity,
            'form'        => $form->createView() ,
        );
    }

    /**
     * Displays a form to edit an existing Synonym entity.
     *
     * @Route("/edit/{id}", name="synonym_edit", options={"expose"=true} )
     * @Method("GET")
     * @Template("JerseyColoursBundle:Synonym:form.html.twig")
     */
    public function editAction($id)
    {
        $em = $this->getDoctrine()->getManager();

        $entity = $em->getRepository('JerseyColoursBundle:Synonym')->find($id);

        if (!$entity) {
            throw $this->createNotFoundException('Unable to find Synonym entity.');
        }

        $editForm = $this->createSynonymForm($entity, $id, true);

        return array(
            'entity'  => $entity,
            'form'  => $editForm->createView()
        );
    }
    
    /**
     * Creates a new Synonym entity.
     *
     * @Route("/save/{id}", name="synonym_save", defaults={"id": null})
     * @Method("POST")
     */
    public function saveAction( Request $request, $id )
    {
        $em = $this->getDoctrine()->getManager();

        if( !$id ) {
            $entity = new Synonym();
        } else {
            
            $entity = $em->getRepository( 'JerseyColoursBundle:Synonym' )->find( $id );
            
            if( !$entity ) {
                throw $this->createNotFoundException( 'Unable to find Synonym entity.' );
            }
        }
        
        $form = $this->createSynonymForm( $entity, $id );
        $form->handleRequest( $request );
        
        //Validate Term existing
        $termExist = $em->getRepository( 'JerseyColoursBundle:Synonym' )->findByTerm( $entity->getTerm() );
        if( $termExist ){
            $return = array('status' => false, 'description' => 'Synonym term already used, please choose new term.');
            return new JsonResponse($return);
        }
        
        if( $form->isValid() ) {
            
            $em->persist( $entity );
            $em->flush();

            $data = array(
                'id'        => $entity->getId(),
                'term'      => $entity->getTerm(),
                'original'  => $entity->getOriginalTerm(),
                'type'      => $entity->getTypeName()
            );

            $return = array('status' => true, 'data' => $data);

        } else {
            $return = array('status' => false, 'description' => $form->getErrorsAsString());
        }
        
        return new JsonResponse($return);
    }
    
    /**
     * Creates a form to save a Synonym entity.
     *
     * @param Synonym $entity The entity
     *
     * @return \Symfony\Component\Form\Form The form
     */
    private function createSynonymForm( Synonym $entity, $id   = null )
    {
        $synoymOptions = $this->container->get('services.synonym_options');
        $form = $this->createForm( new SynonymType($synoymOptions) , $entity, array(
            'action' => $this->generateUrl( 'synonym_save', array(
                'id' => $id
            ) ),
            'method' => 'POST',
        ) );
        
        return $form;
    }
    
    /**
     * Deletes a Synonym entity.
     *
     * @Route("/delete/{id}", name="synonym_delete", options={"expose"=true})
     * @Method("POST")
     */
    public function deleteAction( Request $request, $id )
    {
        $em     = $this->getDoctrine()->getManager();
        $entity = $em->getRepository( 'JerseyColoursBundle:Synonym' )->find( $id );
        
        if( !$entity ) {
            throw $this->createNotFoundException( 'Unable to find Synonym entity.' );
        }
        
        $em->remove( $entity );
        $em->flush();
        
        return new JsonResponse( array('status' => true ) );
    }

    /**
     * Lists all colour entities.
     *
     * @Route("/original-terms", name="get_original_terms", options={"expose"=true})
     * @Method("GET")
     */
    public function getOriginalTerms(Request $request)
    {
        $em = $this->getDoctrine()->getManager();
       
        $term = $request->get('term');
        
        $options = $this->container->get('services.synonym_options')->getOptions($term);
        return new JsonResponse($options);
    }
}
