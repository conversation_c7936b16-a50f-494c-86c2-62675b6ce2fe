{"mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;AAwBM,SAAS,0CAAQ,KAAiB;IACvC,IAAI,eAAC,WAAW,UAAE,MAAM,aAAE,SAAS,EAAC,GAAG;IAEvC,IAAI,QAAQ,CAAA,GAAA,mBAAK,EAId;QAAC,SAAS;QAAO,cAAc;QAAM,IAAI;IAAI;IAEhD,IAAI,qBAAC,iBAAiB,wBAAE,oBAAoB,EAAC,GAAG,CAAA,GAAA,wCAAiB;IAEjE,IAAI,OAAO,CAAA,GAAA,oCAAa,EAAE,CAAC,eAA0B,aAA0B,QAAgB;QAC7F,IAAI,WAAW,KAAK,WAAW,GAC7B;QAGF,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO,EAAE;YAC1B,MAAM,OAAO,CAAC,OAAO,GAAG;YACxB,wBAAA,kCAAA,YAAc;gBACZ,MAAM;6BACN;gBACA,UAAU,cAAc,QAAQ;gBAChC,SAAS,cAAc,OAAO;gBAC9B,SAAS,cAAc,OAAO;gBAC9B,QAAQ,cAAc,MAAM;YAC9B;QACF;QACA,mBAAA,6BAAA,OAAS;YACP,MAAM;yBACN;YACA,QAAQ;YACR,QAAQ;YACR,UAAU,cAAc,QAAQ;YAChC,SAAS,cAAc,OAAO;YAC9B,SAAS,cAAc,OAAO;YAC9B,QAAQ,cAAc,MAAM;QAC9B;IACF;IAEA,IAAI,MAAM,CAAA,GAAA,oCAAa,EAAE,CAAC,eAA0B;QAClD,CAAA,GAAA,8CAAmB;QACnB,IAAI,MAAM,OAAO,CAAC,OAAO,EACvB,sBAAA,gCAAA,UAAY;YACV,MAAM;yBACN;YACA,UAAU,cAAc,QAAQ;YAChC,SAAS,cAAc,OAAO;YAC9B,SAAS,cAAc,OAAO;YAC9B,QAAQ,cAAc,MAAM;QAC9B;IAEJ;IAEA,IAAI,YAAY,CAAA,GAAA,oBAAM,EAAE;QACtB,IAAI,YAA2B,CAAC;QAEhC,IAAI,QAAQ;YACV,CAAA,GAAA,8CAAmB;YACnB,MAAM,OAAO,CAAC,OAAO,GAAG;QAC1B;QAEA,IAAI,OAAO,iBAAiB,eAAe,QAAQ,GAAG,CAAC,QAAQ,KAAK,QAAQ;YAC1E,IAAI,cAAc,CAAC;gBACjB,IAAI,EAAE,MAAM,KAAK,GAAG;wBACU,6BAAoD;wBAApD,mCAAoD;oBAAhF,KAAK,GAAG,SAAS,EAAE,KAAK,GAAI,CAAA,CAAA,qCAAA,8BAAA,MAAM,OAAO,CAAC,YAAY,cAA1B,kDAAA,4BAA4B,KAAK,cAAjC,+CAAA,oCAAqC,CAAA,GAAI,EAAE,KAAK,GAAI,CAAA,CAAA,qCAAA,+BAAA,MAAM,OAAO,CAAC,YAAY,cAA1B,mDAAA,6BAA4B,KAAK,cAAjC,+CAAA,oCAAqC,CAAA;oBACrH,MAAM,OAAO,CAAC,YAAY,GAAG;wBAAC,OAAO,EAAE,KAAK;wBAAE,OAAO,EAAE,KAAK;oBAAA;gBAC9D;YACF;YACA,IAAI,YAAY,CAAC;gBACf,IAAI,EAAE,MAAM,KAAK,GAAG;oBAClB,IAAI,GAAG;oBACP,qBAAqB,QAAQ,aAAa,aAAa;oBACvD,qBAAqB,QAAQ,WAAW,WAAW;gBACrD;YACF;YACA,UAAU,WAAW,GAAG,CAAC;gBACvB,IAAI,EAAE,MAAM,KAAK,GAAG;oBAClB;oBACA,EAAE,eAAe;oBACjB,EAAE,cAAc;oBAChB,MAAM,OAAO,CAAC,YAAY,GAAG;wBAAC,OAAO,EAAE,KAAK;wBAAE,OAAO,EAAE,KAAK;oBAAA;oBAC5D,kBAAkB,QAAQ,aAAa,aAAa;oBACpD,kBAAkB,QAAQ,WAAW,WAAW;gBAClD;YACF;YAEA,IAAI,cAAc,CAAC;gBACjB,IAAI,QAAQ;uBAAI,EAAE,cAAc;iBAAC,CAAC,SAAS,CAAC,CAAC,cAAC,UAAU,EAAC,GAAK,eAAe,MAAM,OAAO,CAAC,EAAE;gBAC7F,IAAI,SAAS,GAAG;wBAEY,6BAAkD;oBAD5E,IAAI,SAAC,KAAK,SAAE,KAAK,EAAC,GAAG,EAAE,cAAc,CAAC,MAAM;wBAClB,mCAAkD;oBAA5E,KAAK,GAAG,SAAS,QAAS,CAAA,CAAA,qCAAA,8BAAA,MAAM,OAAO,CAAC,YAAY,cAA1B,kDAAA,4BAA4B,KAAK,cAAjC,+CAAA,oCAAqC,CAAA,GAAI,QAAS,CAAA,CAAA,qCAAA,+BAAA,MAAM,OAAO,CAAC,YAAY,cAA1B,mDAAA,6BAA4B,KAAK,cAAjC,+CAAA,oCAAqC,CAAA;oBACjH,MAAM,OAAO,CAAC,YAAY,GAAG;+BAAC;+BAAO;oBAAK;gBAC5C;YACF;YACA,IAAI,aAAa,CAAC;gBAChB,IAAI,QAAQ;uBAAI,EAAE,cAAc;iBAAC,CAAC,SAAS,CAAC,CAAC,cAAC,UAAU,EAAC,GAAK,eAAe,MAAM,OAAO,CAAC,EAAE;gBAC7F,IAAI,SAAS,GAAG;oBACd,IAAI,GAAG;oBACP,MAAM,OAAO,CAAC,EAAE,GAAG;oBACnB,qBAAqB,QAAQ,aAAa;oBAC1C,qBAAqB,QAAQ,YAAY;oBACzC,qBAAqB,QAAQ,eAAe;gBAC9C;YACF;YACA,UAAU,YAAY,GAAG,CAAC;gBACxB,IAAI,EAAE,cAAc,CAAC,MAAM,KAAK,KAAK,MAAM,OAAO,CAAC,EAAE,IAAI,MACvD;gBAGF,IAAI,SAAC,KAAK,SAAE,KAAK,cAAE,UAAU,EAAC,GAAG,EAAE,cAAc,CAAC,EAAE;gBACpD;gBACA,EAAE,eAAe;gBACjB,EAAE,cAAc;gBAChB,MAAM,OAAO,CAAC,YAAY,GAAG;2BAAC;2BAAO;gBAAK;gBAC1C,MAAM,OAAO,CAAC,EAAE,GAAG;gBACnB,kBAAkB,QAAQ,aAAa,aAAa;gBACpD,kBAAkB,QAAQ,YAAY,YAAY;gBAClD,kBAAkB,QAAQ,eAAe,YAAY;YACvD;QACF,OAAO;YACL,IAAI,gBAAgB,CAAC;gBACnB,IAAI,EAAE,SAAS,KAAK,MAAM,OAAO,CAAC,EAAE,EAAE;wBAMJ,6BAAoD;oBALpF,IAAI,cAAe,EAAE,WAAW,IAAI;wBAKJ,mCAAoD;oBAHpF,kDAAkD;oBAClD,qCAAqC;oBACrC,iFAAiF;oBACjF,KAAK,GAAG,aAAa,EAAE,KAAK,GAAI,CAAA,CAAA,qCAAA,8BAAA,MAAM,OAAO,CAAC,YAAY,cAA1B,kDAAA,4BAA4B,KAAK,cAAjC,+CAAA,oCAAqC,CAAA,GAAI,EAAE,KAAK,GAAI,CAAA,CAAA,qCAAA,+BAAA,MAAM,OAAO,CAAC,YAAY,cAA1B,mDAAA,6BAA4B,KAAK,cAAjC,+CAAA,oCAAqC,CAAA;oBACzH,MAAM,OAAO,CAAC,YAAY,GAAG;wBAAC,OAAO,EAAE,KAAK;wBAAE,OAAO,EAAE,KAAK;oBAAA;gBAC9D;YACF;YAEA,IAAI,cAAc,CAAC;gBACjB,IAAI,EAAE,SAAS,KAAK,MAAM,OAAO,CAAC,EAAE,EAAE;oBACpC,IAAI,cAAe,EAAE,WAAW,IAAI;oBACpC,IAAI,GAAG;oBACP,MAAM,OAAO,CAAC,EAAE,GAAG;oBACnB,qBAAqB,QAAQ,eAAe,eAAe;oBAC3D,qBAAqB,QAAQ,aAAa,aAAa;oBACvD,qBAAqB,QAAQ,iBAAiB,aAAa;gBAC7D;YACF;YAEA,UAAU,aAAa,GAAG,CAAC;gBACzB,IAAI,EAAE,MAAM,KAAK,KAAK,MAAM,OAAO,CAAC,EAAE,IAAI,MAAM;oBAC9C;oBACA,EAAE,eAAe;oBACjB,EAAE,cAAc;oBAChB,MAAM,OAAO,CAAC,YAAY,GAAG;wBAAC,OAAO,EAAE,KAAK;wBAAE,OAAO,EAAE,KAAK;oBAAA;oBAC5D,MAAM,OAAO,CAAC,EAAE,GAAG,EAAE,SAAS;oBAC9B,kBAAkB,QAAQ,eAAe,eAAe;oBACxD,kBAAkB,QAAQ,aAAa,aAAa;oBACpD,kBAAkB,QAAQ,iBAAiB,aAAa;gBAC1D;YACF;QACF;QAEA,IAAI,sBAAsB,CAAC,GAAc,QAAgB;YACvD;YACA,KAAK,GAAG,YAAY,QAAQ;YAC5B,IAAI,GAAG;QACT;QAEA,UAAU,SAAS,GAAG,CAAC;YACrB,OAAQ,EAAE,GAAG;gBACX,KAAK;gBACL,KAAK;oBACH,EAAE,cAAc;oBAChB,EAAE,eAAe;oBACjB,oBAAoB,GAAG,IAAI;oBAC3B;gBACF,KAAK;gBACL,KAAK;oBACH,EAAE,cAAc;oBAChB,EAAE,eAAe;oBACjB,oBAAoB,GAAG,GAAG;oBAC1B;gBACF,KAAK;gBACL,KAAK;oBACH,EAAE,cAAc;oBAChB,EAAE,eAAe;oBACjB,oBAAoB,GAAG,GAAG;oBAC1B;gBACF,KAAK;gBACL,KAAK;oBACH,EAAE,cAAc;oBAChB,EAAE,eAAe;oBACjB,oBAAoB,GAAG,GAAG;oBAC1B;YACJ;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAO;QAAmB;QAAsB;QAAM;KAAI;IAE9D,OAAO;mBAAC;IAAS;AACnB", "sources": ["packages/@react-aria/interactions/src/useMove.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {disableTextSelection, restoreTextSelection}  from './textSelection';\nimport {DOMAttributes, MoveEvents, PointerType} from '@react-types/shared';\nimport React, {useMemo, useRef} from 'react';\nimport {useEffectEvent, useGlobalListeners} from '@react-aria/utils';\n\nexport interface MoveResult {\n  /** Props to spread on the target element. */\n  moveProps: DOMAttributes\n}\n\ninterface EventBase {\n  shiftKey: boolean,\n  ctrlKey: boolean,\n  metaKey: boolean,\n  altKey: boolean\n}\n\n/**\n * <PERSON>les move interactions across mouse, touch, and keyboard, including dragging with\n * the mouse or touch, and using the arrow keys. Normalizes behavior across browsers and\n * platforms, and ignores emulated mouse events on touch devices.\n */\nexport function useMove(props: MoveEvents): MoveResult {\n  let {onMoveStart, onMove, onMoveEnd} = props;\n\n  let state = useRef<{\n    didMove: boolean,\n    lastPosition: {pageX: number, pageY: number} | null,\n    id: number | null\n  }>({didMove: false, lastPosition: null, id: null});\n\n  let {addGlobalListener, removeGlobalListener} = useGlobalListeners();\n\n  let move = useEffectEvent((originalEvent: EventBase, pointerType: PointerType, deltaX: number, deltaY: number) => {\n    if (deltaX === 0 && deltaY === 0) {\n      return;\n    }\n\n    if (!state.current.didMove) {\n      state.current.didMove = true;\n      onMoveStart?.({\n        type: 'movestart',\n        pointerType,\n        shiftKey: originalEvent.shiftKey,\n        metaKey: originalEvent.metaKey,\n        ctrlKey: originalEvent.ctrlKey,\n        altKey: originalEvent.altKey\n      });\n    }\n    onMove?.({\n      type: 'move',\n      pointerType,\n      deltaX: deltaX,\n      deltaY: deltaY,\n      shiftKey: originalEvent.shiftKey,\n      metaKey: originalEvent.metaKey,\n      ctrlKey: originalEvent.ctrlKey,\n      altKey: originalEvent.altKey\n    });\n  });\n\n  let end = useEffectEvent((originalEvent: EventBase, pointerType: PointerType) => {\n    restoreTextSelection();\n    if (state.current.didMove) {\n      onMoveEnd?.({\n        type: 'moveend',\n        pointerType,\n        shiftKey: originalEvent.shiftKey,\n        metaKey: originalEvent.metaKey,\n        ctrlKey: originalEvent.ctrlKey,\n        altKey: originalEvent.altKey\n      });\n    }\n  });\n\n  let moveProps = useMemo(() => {\n    let moveProps: DOMAttributes = {};\n\n    let start = () => {\n      disableTextSelection();\n      state.current.didMove = false;\n    };\n\n    if (typeof PointerEvent === 'undefined' && process.env.NODE_ENV === 'test') {\n      let onMouseMove = (e: MouseEvent) => {\n        if (e.button === 0) {\n          move(e, 'mouse', e.pageX - (state.current.lastPosition?.pageX ?? 0), e.pageY - (state.current.lastPosition?.pageY ?? 0));\n          state.current.lastPosition = {pageX: e.pageX, pageY: e.pageY};\n        }\n      };\n      let onMouseUp = (e: MouseEvent) => {\n        if (e.button === 0) {\n          end(e, 'mouse');\n          removeGlobalListener(window, 'mousemove', onMouseMove, false);\n          removeGlobalListener(window, 'mouseup', onMouseUp, false);\n        }\n      };\n      moveProps.onMouseDown = (e: React.MouseEvent) => {\n        if (e.button === 0) {\n          start();\n          e.stopPropagation();\n          e.preventDefault();\n          state.current.lastPosition = {pageX: e.pageX, pageY: e.pageY};\n          addGlobalListener(window, 'mousemove', onMouseMove, false);\n          addGlobalListener(window, 'mouseup', onMouseUp, false);\n        }\n      };\n\n      let onTouchMove = (e: TouchEvent) => {\n        let touch = [...e.changedTouches].findIndex(({identifier}) => identifier === state.current.id);\n        if (touch >= 0) {\n          let {pageX, pageY} = e.changedTouches[touch];\n          move(e, 'touch', pageX - (state.current.lastPosition?.pageX ?? 0), pageY - (state.current.lastPosition?.pageY ?? 0));\n          state.current.lastPosition = {pageX, pageY};\n        }\n      };\n      let onTouchEnd = (e: TouchEvent) => {\n        let touch = [...e.changedTouches].findIndex(({identifier}) => identifier === state.current.id);\n        if (touch >= 0) {\n          end(e, 'touch');\n          state.current.id = null;\n          removeGlobalListener(window, 'touchmove', onTouchMove);\n          removeGlobalListener(window, 'touchend', onTouchEnd);\n          removeGlobalListener(window, 'touchcancel', onTouchEnd);\n        }\n      };\n      moveProps.onTouchStart = (e: React.TouchEvent) => {\n        if (e.changedTouches.length === 0 || state.current.id != null) {\n          return;\n        }\n\n        let {pageX, pageY, identifier} = e.changedTouches[0];\n        start();\n        e.stopPropagation();\n        e.preventDefault();\n        state.current.lastPosition = {pageX, pageY};\n        state.current.id = identifier;\n        addGlobalListener(window, 'touchmove', onTouchMove, false);\n        addGlobalListener(window, 'touchend', onTouchEnd, false);\n        addGlobalListener(window, 'touchcancel', onTouchEnd, false);\n      };\n    } else {\n      let onPointerMove = (e: PointerEvent) => {\n        if (e.pointerId === state.current.id) {\n          let pointerType = (e.pointerType || 'mouse') as PointerType;\n\n          // Problems with PointerEvent#movementX/movementY:\n          // 1. it is always 0 on macOS Safari.\n          // 2. On Chrome Android, it's scaled by devicePixelRatio, but not on Chrome macOS\n          move(e, pointerType, e.pageX - (state.current.lastPosition?.pageX ?? 0), e.pageY - (state.current.lastPosition?.pageY ?? 0));\n          state.current.lastPosition = {pageX: e.pageX, pageY: e.pageY};\n        }\n      };\n\n      let onPointerUp = (e: PointerEvent) => {\n        if (e.pointerId === state.current.id) {\n          let pointerType = (e.pointerType || 'mouse') as PointerType;\n          end(e, pointerType);\n          state.current.id = null;\n          removeGlobalListener(window, 'pointermove', onPointerMove, false);\n          removeGlobalListener(window, 'pointerup', onPointerUp, false);\n          removeGlobalListener(window, 'pointercancel', onPointerUp, false);\n        }\n      };\n\n      moveProps.onPointerDown = (e: React.PointerEvent) => {\n        if (e.button === 0 && state.current.id == null) {\n          start();\n          e.stopPropagation();\n          e.preventDefault();\n          state.current.lastPosition = {pageX: e.pageX, pageY: e.pageY};\n          state.current.id = e.pointerId;\n          addGlobalListener(window, 'pointermove', onPointerMove, false);\n          addGlobalListener(window, 'pointerup', onPointerUp, false);\n          addGlobalListener(window, 'pointercancel', onPointerUp, false);\n        }\n      };\n    }\n\n    let triggerKeyboardMove = (e: EventBase, deltaX: number, deltaY: number) => {\n      start();\n      move(e, 'keyboard', deltaX, deltaY);\n      end(e, 'keyboard');\n    };\n\n    moveProps.onKeyDown = (e) => {\n      switch (e.key) {\n        case 'Left':\n        case 'ArrowLeft':\n          e.preventDefault();\n          e.stopPropagation();\n          triggerKeyboardMove(e, -1, 0);\n          break;\n        case 'Right':\n        case 'ArrowRight':\n          e.preventDefault();\n          e.stopPropagation();\n          triggerKeyboardMove(e, 1, 0);\n          break;\n        case 'Up':\n        case 'ArrowUp':\n          e.preventDefault();\n          e.stopPropagation();\n          triggerKeyboardMove(e, 0, -1);\n          break;\n        case 'Down':\n        case 'ArrowDown':\n          e.preventDefault();\n          e.stopPropagation();\n          triggerKeyboardMove(e, 0, 1);\n          break;\n      }\n    };\n\n    return moveProps;\n  }, [state, addGlobalListener, removeGlobalListener, move, end]);\n\n  return {moveProps};\n}\n"], "names": [], "version": 3, "file": "useMove.main.js.map"}