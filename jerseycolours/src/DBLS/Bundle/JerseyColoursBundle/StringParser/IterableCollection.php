<?php

namespace DBLS\Bundle\JerseyColoursBundle\StringParser;

/**
 *
 * <AUTHOR>
 */
class IterableCollection implements \Iterator {

    /**
     *
     * @var array
     */
    private $_container;

    /**
     *
     * @var int
     */
    private $_position;

    /**
     *
     * @param array $container
     */
    public function __construct($container) {
        $this->_container = array_unique($container);
        usort($this->_container, function($a, $b) {
            return strlen($b) - strlen($a);
        });
        $this->_position = 0;
    }

    /**
     * Reset position
     */
    public function rewind() {
        $this->_position = 0;
    }

    /**
     * Return current element
     * @return string
     */
    public function current() {
        return $this->_container[$this->_position];
    }

    /**
     * Return current key
     * @return int
     */
    public function key() {
        return $this->_position;
    }

    /**
     * Set point to the next element
     */
    public function next() {
        ++$this->_position;
    }

    /**
     * Check if current position is valid
     * @return bool
     */
    public function valid() {
        return isset($this->_container[$this->_position]);
    }

    /**
     * Check if $item exists in the container
     * @param bool $item
     */
    public function exists($item) {
        return in_array($item, $this->_container);
    }
}
