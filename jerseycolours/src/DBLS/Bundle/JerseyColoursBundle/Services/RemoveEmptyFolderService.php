<?php

namespace DBLS\Bundle\JerseyColoursBundle\Services;

class RemoveEmptyFolderService
{

    /**
    * @param  string
    */
    public function clean($path) {
        $empty = true;
        foreach (glob($path.DIRECTORY_SEPARATOR."*") as $file)
        {
            if (is_dir($file))
            {
                if (!$this->clean($file)) $empty=false;
            }
            else
            {
                $empty=false;
            }
        }
        if ($empty) rmdir($path);
        return $empty;
    }

    /**
     * @param  string
     */
    public function clean_old($dir)
    {
        $directoryIterator = new \RecursiveDirectoryIterator($dir, \RecursiveDirectoryIterator::SKIP_DOTS);
        $iterator = new \RecursiveIteratorIterator($directoryIterator, \RecursiveIteratorIterator::CHILD_FIRST);

        foreach ( $iterator as $directory ) {

            if ( !$directory->isDir() )
                continue;

            /**
             * @TODO CHECK IF IS EMPTY
             */
            $iterator = new \FilesystemIterator($directory);
            $isDirEmpty = !$iterator->valid();

            if ( $isDirEmpty )
                rmdir($directory->getPathname());
        }
    }
}
