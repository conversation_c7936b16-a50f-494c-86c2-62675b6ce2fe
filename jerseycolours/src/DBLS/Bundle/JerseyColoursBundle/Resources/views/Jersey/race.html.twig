<div id="content-header" style="margin-top: 0px !important ">
	<h3>{{ race.name }}</h3>
        <h4>{{ race.racetime|date('d M, Y') }}. {{ race.meeting.location }}</h4>
	<div class="btn-group">
	    <a class="btn btn-large" href="{{ path('jersey_meeting') }}" title="Go Back."><i class="fa fa-arrow-circle-left"></i> Go Back.</a>
	</div>
    </div>

<div class="well">
       
    <div class="row">
    	{% for jersey in race.unapprovedJerseys %}
    	    {{ include('JerseyColoursBundle:Jersey:detail.html.twig', { 'jersey': jersey, 'id':race.id }) }}

            <!-- {% if loop.index % 4 == 0 and not loop.last %}
              </div><div class="row">
            {% endif %} -->
    	{% endfor %}

    </div>
    
    <div class="row">
    	<div class="col-sm-3 pull-right">
    	    <button type="button" data-id="{{ race.id }}" class="btn btn-success btn-block approve-race btn-race-{{ race.id }}" 
    		      {% if race.hasError or race.approved %}disabled{% endif %}>
    		      Approve
    	    </button>
    	</div>
    </div>

<input type="hidden" id="race" value="{{ race.id }}">                      
    <!--
    <input type="hidden" id="page" value="{# page #}">
    <div class="navigation">
        {# knp_pagination_render(pagination) #}
    </div>
    -->              
</div>