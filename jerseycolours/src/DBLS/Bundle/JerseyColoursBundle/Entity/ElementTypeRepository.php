<?php

namespace DBLS\Bundle\JerseyColoursBundle\Entity;

use Doctrine\ORM\EntityRepository;

/**
 * ElementTypeRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class ElementTypeRepository extends EntityRepository
{
    /**
     * Find element type by name
     * 
     * @param string $name
     * @return ElementType
     */
    public function findByName($name) 
    {
        $qb = $this->_em->createQueryBuilder();
        $qb
            ->select('t')
            ->from('JerseyColoursBundle:ElementType', 't')
            ->where($qb->expr()->eq(
                $qb->expr()->lower($qb->expr()->trim('t.name')), ":name"
            ))
            ->setParameter('name', trim(strtolower($name)));

        $q = $qb->getQuery();
        return $q->getSingleResult();
    }

    /**
     * 
     * @param  string $name
     * @return array
     */
    public function listOptionsCombo($name)
    {
        $qb = $this->createQueryBuilder('e');

        $elements = $qb->where($qb->expr()->like(
                        $qb->expr()->lower($qb->expr()->trim('e.name')), ":name"
                    ))
                    ->setParameter('name', '%' . strtolower(trim($name)) . '%')
                    ->orderBy('e.name','ASC')
                    ->getQuery()
                    ->getResult();

        $dataElements = array();
        foreach ( $elements as $element ) {
            $dataElements[] = array(
                'id' => $element->getId(),
                'text' => $element->getName()
            );
        }

        return $dataElements;
    }
}
