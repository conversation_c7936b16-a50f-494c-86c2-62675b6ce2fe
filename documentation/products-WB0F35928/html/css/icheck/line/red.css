/* iCheck plugin Line skin, red
----------------------------------- */
.icheckbox_line-red,
.iradio_line-red {
    position: relative;
    display: block;
    margin: 0;
    padding: 5px 15px 5px 38px;
    font-size: 13px;
    line-height: 17px;
    color: #fff;
    background: #e56c69;
    border: none;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    cursor: pointer;
}
    .icheckbox_line-red .icheck_line-icon,
    .iradio_line-red .icheck_line-icon {
        position: absolute;
        top: 50%;
        left: 13px;
        width: 13px;
        height: 11px;
        margin: -5px 0 0 0;
        padding: 0;
        overflow: hidden;
        background: url(line.png) no-repeat;
        border: none;
    }
    .icheckbox_line-red.hover,
    .icheckbox_line-red.checked.hover,
    .iradio_line-red.hover {
        background: #E98582;
    }
    .icheckbox_line-red.checked,
    .iradio_line-red.checked {
        background: #e56c69;
    }
        .icheckbox_line-red.checked .icheck_line-icon,
        .iradio_line-red.checked .icheck_line-icon {
            background-position: -15px 0;
        }
    .icheckbox_line-red.disabled,
    .iradio_line-red.disabled {
        background: #F7D3D2;
        cursor: default;
    }
        .icheckbox_line-red.disabled .icheck_line-icon,
        .iradio_line-red.disabled .icheck_line-icon {
            background-position: -30px 0;
        }
    .icheckbox_line-red.checked.disabled,
    .iradio_line-red.checked.disabled {
        background: #F7D3D2;
    }
        .icheckbox_line-red.checked.disabled .icheck_line-icon,
        .iradio_line-red.checked.disabled .icheck_line-icon {
            background-position: -45px 0;
        }

/* Retina support */
@media only screen and (-webkit-min-device-pixel-ratio: 1.5),
       only screen and (-moz-min-device-pixel-ratio: 1.5),
       only screen and (-o-min-device-pixel-ratio: 3/2),
       only screen and (min-device-pixel-ratio: 1.5) {
    .icheckbox_line-red .icheck_line-icon,
    .iradio_line-red .icheck_line-icon {
        background-image: url(<EMAIL>);
        -webkit-background-size: 60px 13px;
        background-size: 60px 13px;
    }
}