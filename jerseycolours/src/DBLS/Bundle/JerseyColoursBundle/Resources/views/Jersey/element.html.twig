<div class="col-xs-12 col-sm-6 col-md-4 less-padding same-size">
    <div class="widget-box">
	<div class="widget-title">
	    <span class="icon">
		<i class="fa fa-puzzle-piece"></i>
	    </span>
	    <h5>{{ element.vars.data.elementType.name }}</h5>
	</div>
	<div class="widget-content">
	    <div class="row">
		<div class="col-xs-12 col-sm-12 less-padding">
		    <div class="panel panel-default">
			<div class="panel-heading">
			    <h6>Element colour</h6>
			</div>
			<div class="panel-body">
			    <div class="row less-padding color-control">
					<div class="col-xs-12 col-sm-12 less-padding">
					    {{ form_widget(element.colour) }}
					</div>
					<!-- <div class="col-xs-2 col-sm-2 less-padding">
					    {% if element.vars.data.colour %}
							<span style="background-color: {{ element.vars.data.colour.hexadecimal }}" class="colour-square pull-right"></span>
					    {% else %}
							<span class="colour-square pull-right"></span>
					    {% endif %}
					</div> -->
			    </div>
			</div>
		    </div>
		</div>
	    </div>

	    {{ include('JerseyColoursBundle:Jersey:pattern.html.twig', {'element':element}) }}
	    
	</div>
    </div>
</div>