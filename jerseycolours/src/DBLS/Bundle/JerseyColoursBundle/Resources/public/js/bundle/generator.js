Array.prototype.nextInfinite = function()
{
    if ( this.pos == undefined )
        this.pos = -1;

    this.pos++;

    if ( this.pos >= this.length )
        this.pos = 0;

    return this[this.pos];
};

Array.prototype.next = function()
{
    if ( this.pos == undefined )
        this.pos = -1;

    this.pos++;

    if ( this.post >= this.length )
        return null;

    return this[this.pos];
};

var patternsPath = '../images/patterns';
var baseFrontSvg = '../images/base_front.svg';
var baseSideSvg = '../images/base_side.svg';
var isFinished = false;
var snapObject = Snap("#svg-container");
var svgCode = '';

/**
*Generate the Jersey SVG
*/
function jerseyGenerator(jersey)
{
    var view = jersey.view || 'front';
    var filePath = 'front' == view ? 'front.svg' : 'side.svg';
    var baseSvg = 'side' == view ? baseSideSvg : baseFrontSvg;
    var countPatterns = 0;
    var elementsJersey = [
        'base', 'shirt', 'seams', 'sash',
        'collar', 'cap', 'pompom', 'tassel', 
        'peak', 'sleeves', 'armbands', 'cuffs', 'epaulettes'
    ];

    function formatElementSelector(element)
    {
        return '#' + element.name.toLowerCase();
    }

    function getPatternPath(pattern)
    {
        path = [patternsPath, pattern.slug, filePath];
        return path.join('/');
    }

    function getPatternIdentifier(pattern)
    {
        var patterIdentifier = ['pattern', pattern.slug];
        return patterIdentifier.join('-');
    }

    function orderElements()
    {
        last = snapObject.select('metadata');
        while ( element = elementsJersey.next() ) {

            var elementIdentifier = '#' + element;
            var node = snapObject.select(elementIdentifier);

            if ( !node )
                continue;

            var element = node.remove();
            element.insertAfter(last);
            last = element;
        }
    }

    function appendAll(node, list)
    {
        list.forEach(
            function(ele)
            {
                node.append(ele);
            }
        );

        return node;
    }

    function setSvgSize(root)
    {
        root.attr(
            {
                id: 'model', 
                width: 600, 
                height: 800, 
                //viewBox: '0 0 60 86'
            }
        );
    }

    function init()
    {
        isFinished = false;

        Snap.load(
            baseSvg, 
            function (f) 
            {
                snapObject.append(f);

                setSvgSize(snapObject.select('svg'));
                orderElements();
                
                setElementsColours();
                loadJerseyPatterns();

                finishProcess();
            }
        );
    }

    function fillColour(element, colour)
    {
        element.attr({fill: colour}).selectAll('*').forEach(
            function(ele)
            {
                if ( (/^logo/).exec(ele.attr('id')) )
                    return;

                fillColour(ele, colour);
            }
        );
    }

    function setElementsColours()
    {
        jersey.elements.forEach(
            function(element)
            {
                var selector = formatElementSelector(element);
                var color = element.colour;

                if ( !color ) return true;

                var svgObject = snapObject.select(selector);

                if ( !svgObject ) return true;
                
                fillColour(svgObject, color );
            }
        );
    }

    function loadJerseyPatterns()
    {
        jersey.elements.forEach(
            function(element)
            {
                if ( element.patterns && element.patterns.length > 0 )
                    loadPatterns(element.patterns, element);
            }
        );
    }

    function loadPatterns(patterns, element)
    {
        var elementPattern = '#' + element.name;
        var elementNode = snapObject.select(elementPattern);

        if ( !elementNode ) return false;

        patterns.forEach(
            function(pattern)
            {
                var file = getPatternPath(pattern);
                countPatterns++;
               
                Snap.load(
                    file, 
                    function (f) 
                    {
                        var g = snapObject.select('svg').g();
                        
                        fillColour(f.select('svg'), 'none');
                        
                        g.attr('id', getPatternIdentifier(pattern) );
                        g.append(f);


                        g.insertAfter(elementNode);
                        setPatternColour(pattern, g);

                        countPatterns--;
                        finishProcess();
                    }
                ); 
            }
        );

        return true;
    }

    function finishProcess()
    {
        if ( countPatterns <= 0 ) {
            isFinished = true;

            snapObject.selectAll('desc').forEach(function(ele){ ele.remove(); });
            snapObject.selectAll('style').forEach(function(ele){ ele.remove(); });
        }
    }

    function setPatternColour(pattern, patternNode)
    {
        if ( !pattern.colours || pattern.colours.length <= 0 )
            return true;

        var colours = pattern.colours;

        patternNode.selectAll('[id^=pattern]').forEach(
            function(elem)
            {
                var color = colours.nextInfinite();
                fillColour(elem, color);
            }
        );
    }

    init();
}