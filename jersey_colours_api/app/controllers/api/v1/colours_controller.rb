# frozen_string_literal: true

module Api
  module V1
    class ColoursController < BaseController
      before_action :set_colour, only: [:show, :update, :destroy]

      def index
        colours = Colour.all
        colours = colours.where('name ILIKE ?', "%#{params[:search]}%") if params[:search].present?
        colours = paginate(colours.order(:name))

        render_json_api(colours, ColourSerializer)
      end

      def show
        render_json_api(@colour, ColourSerializer)
      end

      def create
        colour = Colour.new(colour_params)
        
        if colour.save
          render_json_api(colour, ColourSerializer, status: :created)
        else
          render_error('Failed to create colour', :unprocessable_entity, colour.errors.full_messages)
        end
      end

      def update
        if @colour.update(colour_params)
          render_json_api(@colour, ColourSerializer)
        else
          render_error('Failed to update colour', :unprocessable_entity, @colour.errors.full_messages)
        end
      end

      def destroy
        if @colour.jersey_elements.exists? || @colour.jersey_pattern_colours.exists?
          render_error('Cannot delete colour that is in use', :unprocessable_entity)
        else
          @colour.destroy
          render_success(nil, 'Colour deleted successfully')
        end
      end

      private

      def set_colour
        @colour = Colour.find(params[:id])
      end

      def colour_params
        params.require(:colour).permit(:name, :cmyk, :hexadecimal)
      end
    end
  end
end
