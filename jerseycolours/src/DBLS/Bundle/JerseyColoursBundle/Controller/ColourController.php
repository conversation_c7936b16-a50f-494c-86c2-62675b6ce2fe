<?php

namespace DBLS\Bundle\JerseyColoursBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;

use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Template;

use DBLS\Bundle\JerseyColoursBundle\Entity\Colour;
use DBLS\Bundle\JerseyColoursBundle\Form\ColourType;

/**
 * Colour controller.
 *
 * @Route("/colour")
 */
class ColourController extends Controller
{

    /**
     * Lists all Colour entities.
     *
     * @Route("/", name="colour", options={"expose"=true})
     * @Method("GET")
     * @Template("JerseyColoursBundle:Colour:index.html.twig")
     */
    public function indexAction()
    {
        $em = $this->getDoctrine()->getManager();

        $entities = $em->getRepository('JerseyColoursBundle:Colour')->findAll();

        return array(
            'entities' => $entities,
        );
    }
    
    /**
     * Displays a form to create a new Colour entity.
     *
     * @Route("/new", name="colour_new")
     * @Method("GET")
     * @Template("JerseyColoursBundle:Colour:form.html.twig")
     */
    public function newAction()
    {
        $entity = new Colour();
        $form   = $this->createColourForm($entity);

        return array(
            'entity' => $entity,
            'form'   => $form->createView(),
        );
    }

    /**
     * Displays a form to edit an existing Colour entity.
     *
     * @Route("/edit/{id}", name="colour_edit")
     * @Method("GET")
     * @Template("JerseyColoursBundle:Colour:form.html.twig")
     */
    public function editAction($id)
    {
        $em = $this->getDoctrine()->getManager();

        $entity = $em->getRepository('JerseyColoursBundle:Colour')->find($id);

        if (!$entity) {
            throw $this->createNotFoundException('Unable to find Colour entity.');
        }

        $editForm = $this->createColourForm($entity, $id);

        return array(
            'entity'    => $entity,
            'form'	=> $editForm->createView()
        );
    }
    
    /**
     * Creates a new Colour entity.
     *
     * @Route("/save/{id}", name="colour_save", defaults={"id": null})
     * @Method("POST")
     * @Template("JerseyColoursBundle:Colour:form.html.twig")
     */
    public function saveAction(Request $request, $id)
    {
	if ( !$id ) {
	    $entity = new Colour();
	} else {
	    
	    $em = $this->getDoctrine()->getManager();
	    $entity = $em->getRepository('JerseyColoursBundle:Colour')->find($id);

	    if (!$entity) {
		throw $this->createNotFoundException('Unable to find Colour entity.');
	    }
	}
	
        $form = $this->createColourForm($entity, $id);
        $form->handleRequest($request);
	
	$messageBroker = $this->get('message.broker');
	$messageDefaults = $this->container->getParameter('messages');

        if ( $form->isValid() ) {
	    
            $em = $this->getDoctrine()->getManager();
            $em->persist($entity);
            $em->flush();
	 
	    $form = $this->createColourForm($entity, $entity->getId() );
	     
	    $messageBroker->addSuccess($messageDefaults['success']);
	    
        } else {
	    
	    $messageBroker->addError($messageDefaults['error']);
	}
	
	return array( 'form' => $form->createView() );
    }

    /**
     * Creates a form to save a Colour entity.
     *
     * @param Colour $entity The entity
     *
     * @return \Symfony\Component\Form\Form The form
     */
    private function createColourForm(Colour $entity, $id = null)
    {
        $form = $this->createForm(new ColourType(), $entity, array(
            'action' => $this->generateUrl('colour_save', array( 'id' => $id )),
            'method' => 'POST',
        ));

        return $form;
    }

    /**
     * Deletes a Colour entity.
     *
     * @Route("/delete/{id}", name="colour_delete", options={"expose"=true})
     * @Method("POST")
     */
    public function deleteAction(Request $request, $id)
    {
	$em = $this->getDoctrine()->getManager();
	$entity = $em->getRepository('JerseyColoursBundle:Colour')->find($id);

	if ( !$entity ) {
	    throw $this->createNotFoundException('Unable to find Colour entity.');
	}

	$em->remove($entity);
	$em->flush();

        $serializedEntity = $this->container->get('jms_serializer')->serialize($entity, 'json');
	return new JsonResponse($serializedEntity);
    }
}
