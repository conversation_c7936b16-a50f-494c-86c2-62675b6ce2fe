{% extends 'site.html.twig' %}

{% block page_header %}
    <div id="content-header">
	<h1>Error list</h1>
    </div>
{% endblock page_header %}

{% block breadcrumb %}
    <div id="breadcrumb">
	<a href="{{ path('homepage') }}" class="tip-bottom" data-original-title="Go to Home"><i class="fa fa-home"></i> Home</a>
	<a href="{{ path('error') }}" class="current">Error list</a>
    </div>
{% endblock breadcrumb %}

{% block content %}
    <div class="row">
	<div class="col-xs-12">
	    <div class="widget-box">
		<div class="widget-title">
		    <span class="icon">
			<i class="fa fa-th"></i>
		    </span>
		    <h5>Error list</h5>
		</div>
		<div class="widget-content nopadding">
		    <table class="table table-bordered table-striped table-hover data-table">
			<thead>
			    <tr>
				<th style="width: 10%">Amount</th>
				<th style="width: 25%">Expression</th>
				<th style="width: 50%" class="no-sort">Message</th>
				<th style="width: 15%" class="no-sort">Action</th>
			    </tr>
			</thead>
			<tbody>
                            {% for error in errors %}
				<tr>
                                    <td style="text-align: center">{{error.amount}}</td>
				    <td>{{error.expression}}</td>
				    <td>{{error.message|raw}}</td>
				    <td>
					<div class="btn-group">
                                            <button class="btn btn-default btn-xs dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                              Choose an Action <span class="caret"></span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a href="{{ path('pattern_new') }}">Add Pattern</a>
                                                </li>
                                                <li>
                                                    <a href="{{ path('colour_new') }}">Add Colour</a>
                                                </li>
                                                <!--
                                                <li>
                                                    <a href="">Synonym</a>
                                                </li>
                                                -->
                                            </ul>
                                        </div>
				    </td>
				</tr>
			    {% endfor %}
			</tbody>
		    </table>
		</div>
	    </div>
	</div>
    </div>
{% endblock content %}


{% block extrascripts %}
    {% javascripts 
	'@JerseyColoursBundle/Resources/public/js/bundle/error.js' %}
		<script type="text/javascript" src="{{ asset_url }}"></script>
    {% endjavascripts %}
{% endblock extrascripts %}