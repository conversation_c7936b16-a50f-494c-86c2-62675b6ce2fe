{% import '::macros.html.twig' as macros %}

<div class="col-sm-6 col-xs-12 col-md-15 col-lg-3">

	{% set class_silk = 'warning' %}		
	{% set tooltip = 'Silk has matched patterns and colours.' %}

    {% if jersey.copyFrom %}
		{% set class_silk = 'success' %}
		{% set tooltip = 'Silk has matched and previously approved.' %}
    {% endif %}

    {% if jersey.hasError %}
		{% set class_silk = 'error' %}
		{% set tooltip = 'There are some errors on the silk matching.' %}
    {% endif %}    

    <div data-original-title="{{ tooltip }}" class="silk-container-{{ jersey.id }} panel tip-top panel-default jersey-min-height silk {{ class_silk }}">

		<div class="panel-body">
			<a href="javascript:;" class="edit-jersey" data-id="{{ jersey.id }}">
				<span class="badge badge-info pull-right">
				    <i class="fa fa-pencil"></i>
				</span>
		    </a>
		    {{ macros.jersey_image(jersey) }}
		</div>
		<div class="panel-footer-jersey text-center detail" title="{{ jersey.description|title }}">
	        <h4>{{ jersey.number }} - {{ jersey.name }}</h4>                 
		    <span class="detail-{{ jersey.id }}">{{ jersey.stringValue|title }}</span>
		</div>
    </div>
</div>