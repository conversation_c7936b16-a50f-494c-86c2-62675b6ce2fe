JockerColour = window.JockerColour || {};

JockerColour.Pattern = {
    
    init: function()
    {
	var form  = $( 'form' );
	submit = function()
	{
	    var obj = {
		callback: function( response )
		{
		    if ( response.status )
			General.go( Routing.generate('pattern') );
		    
		}
	    };

	    Form.submitAjax( form, obj );
	    return false;
	};
    
	//Form.addValidate( form, submit );
	
	this.configDelete();
    },
    
    configDelete: function()
    {
	$(document).on('click', 'a.delete-item',
	    function()
	    {
		var id = $(this).data('id');
		
		bootbox.confirm("Are you sure you want to delete it?", 
		    function(result) 
		    {
			if ( !result ) return true;
			
                        Form.showLoading();
                        
			$.post( Routing.generate('pattern_delete', {id: id}))
				.done(
				    function() 
				    {
					Message.msgSuccess('Item was deleted.');
					location.reload(true);
				    })
				.fail(
				    function() 
				    {
					Message.msgError('Item could not be deleted.');
				    }
				)
                                .always(
                                    function()
                                    {
                                        Form.hideLoading();
                                    }                                    
                                );
		    }
		); 
	    }
	);
    },
    
    showPatternPreview: function(imgSrcFront, imgSrcSide, patternTitle) {
        bootbox.dialog({
            title: patternTitle,
             message: '<div class="row"><div class="col-sm-6 col-md-6"><div class="thumbnail"><img class="jersey-preview" src="'+imgSrcFront+'"/><div class="caption text-center"><h4>Front</h4></div></div></div><div class="col-sm-6 col-md-6"><div class="thumbnail"><img class="jersey-preview" src="'+imgSrcSide+'"/><div class="caption text-center"><h4>Side</h4></div></div></div></div>'
        });        
    }
};

$( document ).ready(
    function()
    {
	JockerColour.Pattern.init();
    }
);