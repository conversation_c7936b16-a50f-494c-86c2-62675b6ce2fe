import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  SwatchIcon,
  PaintBrushIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

export const Dashboard: React.FC = () => {
  const { user } = useAuth();

  const stats = [
    {
      name: 'Total Jerseys',
      value: '1,234',
      icon: SwatchIcon,
      color: 'bg-blue-500',
      href: '/jerseys',
    },
    {
      name: 'Parsed Successfully',
      value: '1,156',
      icon: ChartBarIcon,
      color: 'bg-green-500',
      href: '/jerseys?parsed=true',
    },
    {
      name: 'Parse Errors',
      value: '78',
      icon: ExclamationTriangleIcon,
      color: 'bg-red-500',
      href: '/jerseys?parsed=false',
    },
    {
      name: 'Available Colors',
      value: '156',
      icon: PaintBrushIcon,
      color: 'bg-purple-500',
      href: '/colours',
    },
  ];

  const recentActivity = [
    {
      id: 1,
      type: 'jersey_created',
      description: 'New jersey "Red with white stripes" created',
      time: '2 minutes ago',
    },
    {
      id: 2,
      type: 'jersey_parsed',
      description: 'Jersey "Blue and yellow hoops" parsed successfully',
      time: '5 minutes ago',
    },
    {
      id: 3,
      type: 'color_added',
      description: 'New color "Royal Blue" added to palette',
      time: '1 hour ago',
    },
    {
      id: 4,
      type: 'pattern_updated',
      description: 'Pattern "Diagonal stripes" updated',
      time: '2 hours ago',
    },
  ];

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">
          Welcome back, {user?.first_name}!
        </h1>
        <p className="mt-1 text-sm text-gray-600">
          Here's what's happening with your jersey colours today.
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        {stats.map((stat) => (
          <Link
            key={stat.name}
            to={stat.href}
            className="relative bg-white pt-5 px-4 pb-12 sm:pt-6 sm:px-6 shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow"
          >
            <dt>
              <div className={`absolute ${stat.color} rounded-md p-3`}>
                <stat.icon className="h-6 w-6 text-white" aria-hidden="true" />
              </div>
              <p className="ml-16 text-sm font-medium text-gray-500 truncate">
                {stat.name}
              </p>
            </dt>
            <dd className="ml-16 pb-6 flex items-baseline sm:pb-7">
              <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
            </dd>
          </Link>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Activity */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Recent Activity
            </h3>
            <div className="flow-root">
              <ul className="-mb-8">
                {recentActivity.map((activity, activityIdx) => (
                  <li key={activity.id}>
                    <div className="relative pb-8">
                      {activityIdx !== recentActivity.length - 1 ? (
                        <span
                          className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                          aria-hidden="true"
                        />
                      ) : null}
                      <div className="relative flex space-x-3">
                        <div>
                          <span className="h-8 w-8 rounded-full bg-primary-500 flex items-center justify-center ring-8 ring-white">
                            <SwatchIcon className="h-4 w-4 text-white" />
                          </span>
                        </div>
                        <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                          <div>
                            <p className="text-sm text-gray-500">
                              {activity.description}
                            </p>
                          </div>
                          <div className="text-right text-sm whitespace-nowrap text-gray-500">
                            {activity.time}
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Quick Actions
            </h3>
            <div className="space-y-3">
              <Link
                to="/jerseys/new"
                className="w-full flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                <SwatchIcon className="h-5 w-5 text-gray-400 mr-3" />
                Create New Jersey
              </Link>
              <Link
                to="/colours/new"
                className="w-full flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                <PaintBrushIcon className="h-5 w-5 text-gray-400 mr-3" />
                Add New Color
              </Link>
              <Link
                to="/jerseys?parsed=false"
                className="w-full flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                <ExclamationTriangleIcon className="h-5 w-5 text-gray-400 mr-3" />
                Review Parse Errors
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
