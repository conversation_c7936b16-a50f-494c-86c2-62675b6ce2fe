<?php

namespace DBLS\Bundle\JerseyColoursBundle\Validator\Constraints;

use Symfony\Component\Validator\Constraint;

/**
 * Annotation constraint to prevent entity duplicating
 *
 * @Annotation
 * <AUTHOR>
 */
class Pattern extends Constraint {

    public $fields = array();
    public $repositoryClass = "";

    public function validatedBy() {
        return 'pattern.validator';
    }

    public function getTargets() {
        return self::CLASS_CONSTRAINT;
    }

}
