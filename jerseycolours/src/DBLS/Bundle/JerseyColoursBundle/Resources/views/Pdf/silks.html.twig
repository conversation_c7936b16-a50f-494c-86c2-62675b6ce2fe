<style type="text/css">

    @font-face {
       font-family: 'Helvetica-Condensed';
       src: url({{ font_file }}) format('truetype');
    }

    * {
        margin: 0;
        padding: 0;
    }

    .container {
        overflow: auto;
    }

    .row {
        padding: 0;
        width: 100%;
        overflow: auto;
    }

    .silk-pdf {
        padding: 0;
        width: 215px; 
        float: left;
        text-align: center;
        /*margin-bottom: 13.75px;*/
        margin-right: 40px;
    }

    
    .silk-pdf img {
        width: 215px; 
        height: 215px;
        margin-top: -10px;
    }

    .silk-pdf.no-margin {
        margin-right: 0;
    }

    .silk-pdf p {
        text-align: center;
        font-family: 'Helvetica-Condensed', fantasy;
        height: 65px;
        font-size: 22pt;
    }
   
</style>

<div class="container">
    <div class="row">
        {% for silk in silks %}
            <div class="silk-pdf {% if loop.index % 8 == 0 %}no-margin{% endif %}">

                {% set font_size = '' %}
                {% if silk.name|length >= 13 and silk.name|length < 16 %}
                    {% set font_size = 'letter-spacing: -1px' %}
                {% elseif silk.name|length >= 16 and silk.name|length < 19 %}
                    {% set font_size = 'letter-spacing: -2px' %}
                {% elseif silk.name|length >= 19 %}
                    {% set font_size = 'letter-spacing: -3px' %}
                {% endif %}

                <img src="{{silk.getApprovedSvg}}">
                <p style="{{font_size}}">{{silk.name}}</p>
            </div>
            {% if loop.index % 8 == 0 and not loop.last %}
                </div><div class="row">
            {% endif %}
        {% endfor %}
    </div>
</div>