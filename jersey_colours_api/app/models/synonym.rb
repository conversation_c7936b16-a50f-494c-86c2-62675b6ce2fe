class Synonym < ApplicationRecord
  belongs_to :colour, optional: true

  validates :term, presence: true
  validates :original_term, presence: true
  validates :term, uniqueness: { scope: :original_term }

  scope :for_term, ->(term) { where("LOWER(term) = ?", term.downcase) }

  def self.find_original_term(term)
    synonym = for_term(term).first
    synonym&.original_term || term
  end

  def to_s
    "#{term} -> #{original_term}"
  end
end
