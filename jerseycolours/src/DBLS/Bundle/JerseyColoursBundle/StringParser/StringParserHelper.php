<?php

namespace DBLS\Bundle\JerseyColoursBundle\StringParser;


/**
 * StringParserHelper
 *
 * <AUTHOR> <adrianorc.com>
 */
class StringParserHelper implements IStringParserHelper {
    
    /**
     *
     * @var \Doctrine\ORM\EntityManager
     */
    private $em;

    
    /**
     * Constructor
     */
    public function __construct(\Doctrine\ORM\EntityManager $em) {
        $this->em = $em;
    }
    
    /**
     * Check if a pattern belong to an element
     * 
     * @param type $pattern
     * @param type $element
     */
    public function checkPatternBelongToElement($patternDescription, $elementName) {
        $patternRepo = $this->em->getRepository('JerseyColoursBundle:Pattern');
        return $patternRepo->findByDescriptionAndElement($patternDescription, $elementName);
    }
    
}
