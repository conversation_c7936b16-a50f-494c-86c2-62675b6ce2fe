<?php

namespace DBLS\Bundle\JerseyColoursBundle\Services;

use DBLS\Bundle\JerseyColoursBundle\Entity\Jersey;
use DBLS\Bundle\JerseyColoursBundle\Entity\ElementType;
use DB<PERSON>\Bundle\JerseyColoursBundle\Entity\Error;
use DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElement;
use Symfony\Component\DependencyInjection\ContainerAware;
use Doctrine\ORM\EntityManager;
use Fredcido\PHPSVG\SVGDocument;
use Fredcido\PHPSVG\SVGGroup;
use Fredcido\PHPSVG\SVGStyle;
use Symfony\Component\Filesystem\Filesystem;
use SamJ\DoctrineSluggableBundle\Slugger;


class SvgGenerator extends ContainerAware
{
    /**
     * Holds an instance of database connection
     * @var EntityManager
     */
    private $em = null;

    /**
     * 
     * @var string
     */
    protected $_view = 'front';

    /**
     * 
     * @var string
     */
    protected $_filePath;

    /**
     * 
     * @var string
     */
    protected $_baseSvgFile;

    /**
     * 
     * @var string
     */
    protected $_patternsPath;

    /**
     * 
     * @var SVGDocument
     */
    protected $_baseSvg;

    /**
     * 
     * @var Jersey
     */
    protected $_jersey;

    protected $_width = 600;

    protected $_height = 800;

    /**
     * 
     * @var array
     */
    protected $_elementsJersey = array(
        'base', 
        'shirt',
        'seams', 
        'sash',
        'crossed-sashes',
        'collar', 
        'cap', 
        'pompom', 
        'tassel', 
        'peak', 
        'sleeves', 
        'armbands', 
        'cuffs', 
        'epaulettes',
        'shading'
    );

    public function __construct(EntityManager $em) 
    {
        $this->em = $em;
    }

    /**
     * 
     * @param Jersey $jersey
     */
    public function setJersey(Jersey $jersey)
    {
        $this->_jersey = $jersey;
        return $this;
    }

    /**
     * 
     * @return SVGDocument
     */
    protected function _getBaseSvg()
    {
        $base = SVGDocument::getInstance( $this->_getBaseSvgFile() );
        $base->setWidth($this->_width)->setHeight($this->_height);

        return $base;
    }

    /**
     * 
     * @return string
     */
    protected function _getBaseSvgFile()
    {
        $file = 'front' == $this->_view ? 'base_front' : 'base_side';
        return $this->_prefixFile('images/' . $file . '.svg');
    }

    /**
     * 
     * @param  string $file
     * @return string
     */
    protected function _prefixFile($file)
    {
        $rootDir = $this->container->get('kernel')->getRootDir();
        $file = $rootDir . '/../web/' . $file;
        return $file;
    }

    /**
     * 
     * @param  string $element
     * @return string
     */
    protected function _formatElementSelector($element)
    {
        return '#' . strtolower($element);
    }

    /**
     * 
     * @param  Pattern $pattern
     * @return string
     */
    protected function _getPatternPath($pattern)
    {
        $file = $pattern->getFrontViewPath(false);

        if ( 'side' == $this->_view )
            $file = $pattern->getSideViewPath(false);

        return $this->_prefixFile($file);
    }

    /**
     * 
     * @param  Pattern $pattern
     * @return string
     */
    protected function _getPatternIdentifier($pattern)
    {
        $patterIdentifier = ['pattern', $pattern->getSlug()];
        return implode( '-', $patterIdentifier);
    }

    /**
     * 
     * @param  SimpleXMLElement $insert
     * @param  SimpleXMLElement $target
     * @return SimpleXMLElement
     */
    protected function _insertAfter(\SimpleXMLElement $insert, \SimpleXMLElement $target)
    {
        $target_dom = dom_import_simplexml($target);
        $insert_dom = $target_dom->ownerDocument->importNode(dom_import_simplexml($insert), true);
        if ($target_dom->nextSibling) {
            return $target_dom->parentNode->insertBefore($insert_dom, $target_dom->nextSibling);
        } else {
            return $target_dom->parentNode->appendChild($insert_dom);
        }
    }

    /**
     * 
     */
    protected function _orderElements()
    {
        $toReorder = array();
        foreach ( $this->_baseSvg->children() as $child ) {

            $id = $child->getId();
            if ( in_array($id, $this->_elementsJersey) ) {

                $toReorder[$id] = $child;
            }
        }
        
        foreach ( $this->_elementsJersey as $element ) {

            if ( empty( $toReorder[$element] ) )
                continue;

            $node = $toReorder[$element];

            $this->_baseSvg->removeElement($node);
            $this->_baseSvg->append($node);
        }
    }

    /**
     * 
     */
    protected function _setElementsColours()
    {
        foreach ( $this->_getElements() as $jerseyElement ) {

            $color = $jerseyElement->getColour();

            if ( !$color ) continue;

            $elementId = $this->_getElementId($jerseyElement);
            $elementNode = $this->_baseSvg->getElementById($elementId);

            if ( !$elementNode ) continue;

            $this->_fillColour($elementNode, $color);
        }
    }

    /**
     * 
     * @param  SVGDocument $node
     * @param  string $color
     */
    protected function _fillColour($node, $color)
    {
        $id = $node->getId();
        if ( preg_match('/^logo/i', $id) )
            return;

        if ( !is_string($color) ) {
            /*
            $colorShape = "%s device-cmyk(%s)";
            $color = sprintf($colorShape,
                        $color->getHexadecimal(),
                        $color->getCMYK()
                    );
            */
           
           $color = $color->getHexadecimal();
        }

        $style = new SVGStyle( array('fill' => $color ) );
        $node->setAttribute('fill', $color);
        $node->setStyle($style);

        if ( count($node->children()) > 0 ) {
            foreach ( $node->children() as $child )
                $this->_fillColour($child, $color);
        }
    }

    /**
     * 
     * @param  ElementType $element
     * @return string
     */
    protected function _getElementId(JerseyElement $element)
    {
        $slugger = new Slugger('-', '-');
        $elementEntity = $element->getElementType();
        $name = $slugger->getSlug($elementEntity->getName());

        //$name = trim(strtolower(preg_replace('/ */i', '', $elementEntity->getName() )));

        return $name;
    }

    /**
     * 
     * @return array
     */
    protected function _getElements()
    {
        return $this->_jersey->getElements();
    }

    /**
     * 
     */
    protected function _loadJerseyPatterns()
    {
        foreach ( $this->_getElements() as $element ) {
            $patterns = $element->getJerseyPatterns();
            if ( $patterns && count($patterns) > 0 )
                $this->_loadPatterns($patterns, $element);
        }
    }

    /**
     * 
     */
    protected function _loadPatterns($patterns, $element)
    {
        $elementPattern = $this->_getElementId($element);
        $elementNode = $this->_baseSvg->getElementById($elementPattern);

        if ( !$elementNode ) return false;

        foreach ($patterns as $jerseyPattern) {

            $pattern = $jerseyPattern->getPattern();

            $file = $this->_getPatternPath($pattern);
            if ( !file_exists($file) ) {
                
                //$this->_addErrorPatternNotFound($pattern);
                continue;
            }

            $patternNode = SVGDocument::getInstance( $file );
            
            // Remove colours and shading
            $this->_fillColour($patternNode, 'none');

            // Remove base and shading element
            $this->_cleanPatternNode($patternNode, $elementPattern);

            $patternIdentifier = $this->_getPatternIdentifier($pattern);
                    
            $this->_setPatternColour($jerseyPattern, $patternNode);

            $patternNode->setId($patternIdentifier);
            //$elementNode->append($patternNode);
            $this->_insertAfter($patternNode, $elementNode);
        }

        return true;
    }

    /**
     * 
     * @param Pattern $pattern
     */
    protected function _addErrorPatternNotFound($pattern)
    {
        $messageError = sprintf("Pattern file <b>%s</b> not found with the slug: <b>%s</b>", 
                            $pattern->getDescription(),
                            $pattern->getSlug()
                        );

        $error = new Error();
        $error->setType(Error::GENERATE_SVG)
              ->setMessage($messageError)
              ->setJersey($this->_jersey);

        $this->_jersey->getErrors()->add($error);

        $this->em->persist($error);
        $this->em->persist($this->_jersey);
        $this->em->flush();
    }

    protected function _cleanPatternNode($patternNode, $elementId)
    {
        if ( 'side' == $this->_view )
            return;
        
        $base = $patternNode->getElementById('base');
        if ( $base )
            $patternNode->removeElementById('base');

        $baseShading = $patternNode->getElementById('shading');
        if ( $baseShading )
            $patternNode->removeElementById('shading');
    }

    /**
     * 
     * @param  SVGDocument $node
     * @param  string $pattern
     * @return SVGDocument
     */
    protected function _findFirstByIdPattern($node, $pattern)
    {
        $id = $node->getId();

        if ( preg_match($pattern, $id) )
            return $node;
 
        foreach ( $node->children() as $child ) {
            $found = $this->_findFirstByIdPattern($child, $pattern);
            if ( $found )
                return $found;
        }

        return false;
    }

    /**
     * 
     * @param  array $colours
     * @return
     */
    protected function _getColoursInfinite($dataColours)
    {
        $arrayColours = new \ArrayIterator($dataColours);

        return new \InfiniteIterator($arrayColours);
    }

    /**
     * 
     * @param  $colours
     * @return array
     */
    protected function _getColoursArray($colours)
    {
        $dataColours = array();
        foreach ( $colours as $colour ) {
            $dataColours[] = $colour->getColour();
        }

        return $dataColours;
    }


    /**
     * 
     * @param Pattern     $pattern
     * @param SVGDocument $patternNode
     */
    protected function _setPatternColour($pattern, $patternNode)
    {
        $colours = $pattern->getPatternColours();

        if ( !$colours || count($colours) <= 0 )
            return true;

        $arrayColours = $this->_getColoursArray($colours);

        $patternInteger = '/^pattern-[0-9]+/';
        $interpolateNodes = $this->_findFirstByIdPattern($patternNode, $patternInteger);

        $patternColour = '/^pattern/';
        if ( !empty($interpolateNodes) ) {
            $patternColour = $patternInteger;

            /*
            if ( count($arrayColours) < 2 ) {

                $baseColour = $this->_findBaseColour();
                if ( $baseColour )
                    array_unshift($arrayColours, $baseColour);
            }
            */
        }

        $colourIterator = $this->_getColoursInfinite($arrayColours);
        $colourIterator->rewind();

        $this->_matchByRegex($patternNode, $patternColour,
            function($child) use ($colourIterator)
            {
                $nextColour = $colourIterator->current();
                $colourIterator->next();

                $this->_fillColour($child, $nextColour);
            }
        );
    }

    /**
     * 
     * @return null
     */
    protected function _findBaseColour()
    {
        $baseColour = null;
        foreach ( $this->_getElements() as $element ) {
            if ( $element->getElementType()->getId() == ElementType::BASE && $element->getColour() )
                $baseColour = $element->getColour()->getHexadecimal();
        }

        return $baseColour;
    }

    /**
     * 
     * @param  SVGDocument $node
     * @param  string $regex
     * @param  function $callback
     */
    protected function _matchByRegex($node, $regex, $callback, $matchSelf = true)
    {
        $id = $node->getId();
        if ( $matchSelf && preg_match($regex, $id) )
            call_user_func($callback, $node);

        foreach ( $node->children() as $child )
            $this->_matchByRegex($child, $regex, $callback);
    }

    /**
     * 
     * @return string
     */
    protected function _getJerseyPath()
    {
        $fs = new Filesystem();
        $jerseysDir = $this->container->getParameter('jerseys_dir');
        
        if ( !$fs->exists( $jerseysDir ) )
            $fs->mkdir( $jerseysDir );

        $jerseyFolder = realpath($jerseysDir) . '/' . $this->_jersey->getFolderPath();

        if ( !$fs->exists( $jerseyFolder ) )
            $fs->mkdir( $jerseyFolder );

        $viewFolder = realpath($jerseyFolder) . ('front' == $this->_view ? '/front.svg' : '/side.svg');

        return $viewFolder;
    }

    /**
     * 
     */
    protected function _performGeneration()
    {
        $this->_baseSvg = $this->_getBaseSvg();
        $this->_orderElements();
        $this->_setElementsColours();
        $this->_loadJerseyPatterns();
    }

    /**
     * 
     */
    public function generateFrontView()
    {
        $this->_view = 'front';
        $this->_performGeneration();

        $this->_baseSvg->asXML( $this->_getJerseyPath(), false );
    }

    /**
     * 
     */
    public function generateSideView()
    {
        $this->_view = 'side';
        $this->_performGeneration();

        $this->_baseSvg->asXML( $this->_getJerseyPath(), false );
    }

    /**
     * 
     * @param  Jersey|null $jersey
     * @return boolean
     */
    public function generate(Jersey $jersey = null)
    {
        try {

            if ( $jersey )
                $this->setJersey($jersey);
            
            $this->generateFrontView();
            $this->generateSideView();
            return true;

        } catch ( \Exception $e ) {
            return false;
        }
    }

    /**
     * 
     * @param  Jersey $jersey
     * @return
     */
    public function output(Jersey $jersey, $view = 'front')
    {
        $this->setJersey($jersey);

        $this->_view = $view;
        $this->_performGeneration();

        $this->_baseSvg->output();
    }

}