<?php

namespace DBLS\Bundle\JerseyColoursBundle\EventListener;

use Symfony\Component\HttpKernel\Event\GetResponseEvent;
use Symfony\Component\DependencyInjection\ContainerAware;

/**
* 
*/
class SvgServerListener extends ContainerAware
{
    /**
     * Starts the SVG phantomJS if it's not responding
     * @param  GetResponseEvent $event
     */
    public function onKernelRequest(GetResponseEvent $event)
    {
        $rootDir = $this->container->get('kernel')->getRootDir();
        $webPath = realpath($rootDir . '/../web/');
        $phantom = realpath($rootDir . '/../bin/phantomjs');
        $svgServer = $webPath . '/generator/server.js';
        $logFile = $webPath . '/generator/server.log';

        $command = sprintf("%s %s >> %s &", $phantom, $svgServer, $logFile );

        $startPhantomJs = function() use ($command)
        {
            exec($command);
        };

        try {

            $client = new \Guzzle\Service\Client();
            $url = $this->container->getParameter('svg_server');
            $promise = $client->getAsync($url, array('connect_timeout' => 2))
                        ->then(
                            function(){},
                            $startPhantomJs
                        );

        } catch ( \Exception $e ) {
            var_dump($e);exit;
            call_user_func($startPhantomJs);
        }
    }
}