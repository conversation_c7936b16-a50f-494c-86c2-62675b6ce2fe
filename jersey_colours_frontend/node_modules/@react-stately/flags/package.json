{"name": "@react-stately/flags", "version": "3.1.2", "description": "Spectrum UI components in React", "license": "Apache-2.0", "main": "dist/main.js", "module": "dist/module.js", "types": "dist/types.d.ts", "exports": {"source": "./src/index.ts", "types": ["./dist/types.d.ts", "./src/index.ts"], "import": "./dist/import.mjs", "require": "./dist/main.js"}, "source": "src/index.ts", "files": ["dist", "src"], "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/adobe/react-spectrum"}, "dependencies": {"@swc/helpers": "^0.5.0"}, "publishConfig": {"access": "public"}, "gitHead": "265b4d7f107905ee1c6e87a8af1613ab440a6849"}