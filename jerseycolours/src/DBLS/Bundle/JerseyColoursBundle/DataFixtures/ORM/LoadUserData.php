<?php

namespace DBLS\Bundle\JerseyColoursBundle\DataFixtures\ORM;

use Doctrine\Common\DataFixtures\AbstractFixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Common\Persistence\ObjectManager;
use DBLS\Bundle\JerseyColoursBundle\Entity\User;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

class LoadUserData extends AbstractFixture implements OrderedFixtureInterface, ContainerAwareInterface
{
    
    /**
     * @var ContainerInterface
     */
    private $container;

    /**
     * {@inheritDoc}
     */
    public function setContainer(ContainerInterface $container = null)
    {
        $this->container = $container;
    }
    
    /**
     * {@inheritDoc}
     */
    public function load(ObjectManager $manager)
    {
		
        $user = new User();
        $user->setUsername('admin');
        $user->setEmail('<EMAIL>');
        //$user->setSalt(md5(uniqid()));
        $encoder = $this->container
           ->get('security.encoder_factory')
           ->getEncoder($user)
        ;
        $user->setPassword($encoder->encodePassword('123456', $user->getSalt()));

        $manager->persist($user);
        $manager->flush();
		
    }
        
    /**
     * {@inheritDoc}
     */
    public function getOrder()
    {
        return 1;
    }
}