# frozen_string_literal: true

module Api
  module V1
    class AuthController < ApplicationController
      skip_before_action :authenticate_user!, only: [:login, :register]

      def login
        user = User.find_by(email: params[:email]&.downcase)
        
        if user&.authenticate(params[:password])
          token = generate_token(user)
          render json: {
            success: true,
            message: 'Login successful',
            data: {
              user: user_data(user),
              token: token
            }
          }
        else
          render json: {
            success: false,
            error: 'Invalid email or password'
          }, status: :unauthorized
        end
      end

      def register
        user = User.new(user_params)
        user.role = 'user' # Default role
        
        if user.save
          token = generate_token(user)
          render json: {
            success: true,
            message: 'Registration successful',
            data: {
              user: user_data(user),
              token: token
            }
          }, status: :created
        else
          render json: {
            success: false,
            error: 'Registration failed',
            details: user.errors.full_messages
          }, status: :unprocessable_entity
        end
      end

      def me
        render json: {
          success: true,
          data: {
            user: user_data(current_user)
          }
        }
      end

      private

      def user_params
        params.require(:user).permit(:email, :password, :first_name, :last_name)
      end

      def generate_token(user)
        payload = {
          user_id: user.id,
          email: user.email,
          exp: 24.hours.from_now.to_i
        }
        JWT.encode(payload, Rails.application.secret_key_base, 'HS256')
      end

      def user_data(user)
        {
          id: user.id,
          email: user.email,
          first_name: user.first_name,
          last_name: user.last_name,
          role: user.role
        }
      end

      def authenticate_user!
        super
      rescue
        unauthorized
      end
    end
  end
end
