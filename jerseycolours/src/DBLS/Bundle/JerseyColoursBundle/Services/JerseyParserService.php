<?php

namespace DBLS\Bundle\JerseyColoursBundle\Services;

use DBLS\Bundle\JerseyColoursBundle\StringParser\StringParser;
use DBLS\Bundle\JerseyColoursBundle\Entity\Jersey;
use Doctrine\ORM\EntityManager;
use Symfony\Component\DependencyInjection\ContainerAware;

/**
 * Description of JerseyParserService
 *
 * <AUTHOR>
 */
class JerseyParserService extends ContainerAware
{

    /**
     * Holds an instance of database connection
     * @var EntityManager
     */
    private $em = null;

    /**
     * @var Jersey
     */
    private $jersey = null;

    public function __construct(EntityManager $em)
    {
        $this->em = $em;
    }

    /**
     * @param Jersey $jersey
     */
    public function setJersey( Jersey $jersey )
    {
        $this->jersey = $jersey;
    }

    /**
     * @return Jersey
     */
    public function getJersey()
    {
        $this->jersey;
    }

    public function createJerseyFromString($string)
    {

        if ( !empty($this->container->getParameter('levenshtein_ratio')) ) {
            $leven_ratio = $this->container->getParameter('levenshtein_ratio');
        } else {
            $leven_ratio = 75; //default
        }

        $serviceDictionary = $this->container->get('services.dictionary');
        
        // 1. Parse incoming string
        $stringParser = new StringParser($this->em, $serviceDictionary);
        $stringParser->setLevenshteinRatio($leven_ratio);

        $arrColours = array();
        $colours = $this->em->getRepository('JerseyColoursBundle:Colour')->findAll();
        foreach ($colours as $color) {
            $arrColours[] = trim(strtolower($color->getName()));
        }
        $stringParser->setKnownColours($arrColours);

        $arrAllPatterns = array();
        $arrPatternsAndElements = array();
        $patterns = $this->em->getRepository('JerseyColoursBundle:Pattern')->findBy(array('status' => 1));
        foreach ($patterns as $pattern) {
            $arrAllPatterns[] = trim(strtolower($pattern->getDescription()));
            $arrPatternsAndElements[strtolower(trim($pattern->getElementType()->getName()))][] = trim(strtolower($pattern->getDescription()));
        }
        $stringParser->setKnownPatterns($arrAllPatterns, $arrPatternsAndElements);

        $arrElements = array();
        $elements = $this->em->getRepository('JerseyColoursBundle:ElementType')->findAll();
        foreach ($elements as $element) {
            $arrElements[] = trim(strtolower($element->getName()));
        }
        $stringParser->setKnownElements($arrElements);

        $arrSynonyms = array();
        $synonyms = $this->em->getRepository('JerseyColoursBundle:Synonym')->findAll();
        
        foreach ($synonyms as $synonym) {
            $arrSynonyms[] = array(
                'term' => strtolower(trim($synonym->getTerm())),
                'original_term' => strtolower(trim($synonym->getOriginalTerm()))
            );
        }
        $stringParser->setSynonyms($arrSynonyms);
        
        if ( $this->jersey ) {
            $stringParser->setJersey($this->jersey);
        }

        $jersey = $stringParser->createJerseyFromString($string);

        return $jersey;
    }

}
