# frozen_string_literal: true

class JerseySerializer
  include JSONAPI::Serializer

  attributes :name, :description, :string_value, :status, :parsed, :parsing, 
             :parsing_time, :external_jersey_id, :number, :created_at, :updated_at

  belongs_to :race, serializer: RaceSerializer
  belongs_to :trainer, serializer: TrainerSerializer
  has_many :jersey_elements, serializer: JerseyElementSerializer
  has_many :errors, serializer: ErrorSerializer

  attribute :generated_string do |jersey|
    jersey.generate_string
  end

  attribute :folder_path do |jersey|
    jersey.folder_path
  end

  attribute :element_count do |jersey|
    jersey.jersey_elements.count
  end

  attribute :error_count do |jersey|
    jersey.errors.count
  end

  attribute :has_base_color do |jersey|
    jersey.base_element&.colour.present?
  end
end
