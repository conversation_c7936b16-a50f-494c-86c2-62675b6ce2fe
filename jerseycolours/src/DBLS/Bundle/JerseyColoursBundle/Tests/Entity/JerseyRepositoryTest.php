<?php

namespace DBLS\Bundle\JerseyColoursBundle\Tests\Entity;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use DBLS\Bundle\JerseyColoursBundle\Entity\Meeting;
use DBLS\Bundle\JerseyColoursBundle\Entity\Race;
use DBLS\Bundle\JerseyColoursBundle\Entity\Jersey;

class JerseyRepositoryTest extends WebTestCase
{
    /**
     * @var \Doctrine\ORM\EntityManager
     */
    private $em;

    /**
     * {@inheritDoc}
     */
    public function setUp()
    {
        self::bootKernel();
        $this->em = static::$kernel->getContainer()
            ->get('doctrine')
            ->getManager();

        $this->_createJerseys();
    }

    /**
     * {@inheritDoc}
     */
    protected function tearDown()
    {
        parent::tearDown();
        $this->em->close();
    }

    /**
     * {@inheritDoc}
     */
    public static function setUpBeforeClass()
    {
         //start the symfony kernel
         $kernel = static::createKernel();
         $kernel->boot();
    }

    protected function _createJerseys()
    {
        $meeting = new Meeting();
        $meeting->setExternalId('test')
                ->setDate('2015-08-01')
                ->setLocation('test');

        $this->em->persist($meeting);

        $race = new Race();
        $race->setName('test')
             ->setExternalRaceId('test')
             ->setExternalMeetId('test')
             ->setNumber(1)
             ->setRacetime('2015-08-01 14:00:00')
             ->setMeeting($meeting);

        $this->em->persist($race);

        $this->jersey1 = new Jersey();
        $this->jersey1->setName('Silk 1')
                ->setRace($race)
                ->setParsed(true)
                ->setExternalJerseyId(1)
                ->setStringValue('test');

        $this->jersey2 = new Jersey();
        $this->jersey2->setName('Silk 2')
                ->setRace($race)
                ->setParsing(true)
                ->setExternalJerseyId(2)
                ->setStringValue('test');

        $this->jersey3 = new Jersey();
        $this->jersey3->setName('Silk 3')
                ->setRace($race)
                ->setStatus(true)
                ->setExternalJerseyId(3)
                ->setStringValue('test');

        $this->jersey4 = new Jersey();
        $this->jersey4->setName('Silk 4')
                ->setRace($race)
                ->setExternalJerseyId(4)
                ->setStringValue('test');

        $now = new \DateTime('now');
        $tenMinutesAgo = $now->sub(new \DateInterval('P3M'));

        $this->jersey5 = new Jersey();
        $this->jersey5->setName('Silk 5')
                ->setRace($race)
                ->setParsing(true)
                ->setParsingTime($tenMinutesAgo)
                ->setExternalJerseyId(5)
                ->setStringValue('test');

        $this->em->persist($this->jersey1);
        $this->em->persist($this->jersey2);
        $this->em->persist($this->jersey3);
        $this->em->persist($this->jersey4);
        $this->em->persist($this->jersey5);

        $this->em->flush();
    }

    public function testFetchParsedJersey()
    {
        $jerseyRepository = $this->em->getRepository('JerseyColoursBundle:Jersey');

        $jerseys = $jerseyRepository->findPendingToParse();

        //$this->assertCount(1, $jerseys);
    }
}
