<div class="row">
    <div class="col-xs-12 col-sm-12 less-padding">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h6>Pattern</h6>
            </div>
            <div class="panel-body less-padding pattern-control">
                {% if element.jersey_patterns|length > 0 %}
                    {% for pattern in element.jersey_patterns %}
                        {{ form_widget(pattern.pattern) }}
                    {% endfor %}
                {% else %}
                    {{ form_widget(element.jersey_patterns.vars.prototype.pattern)|replace({'__name__':0})|raw }}
                {% endif %}
            </div>
        </div>
    </div>
</div>

    {% macro colour_container(colour) %}

        <div class="row less-padding color-control">
            <div class="col-xs-9 col-sm-9 less-padding">
                {{ form_widget(colour.colour) }}
            </div>
            <div class="col-xs-1 col-sm-1 less-padding">
                <a href="javascript:;" class="btn text-danger remove-colour"><i class="fa fa-times"></i></a>
            </div>
            <div class="col-xs-2 col-sm-2 less-padding">
                <a href="javascript:;" class="btn text-info sorter"><i class="fa fa-bars"></i></a>
            </div>
            {{ form_widget(colour.order) }}
        </div>

    {% endmacro %}

    {% set colour_prototype = element.jersey_patterns.vars.prototype.pattern_colours.vars.prototype %}

<div class="row">
    <div class="col-xs-12 col-sm-12 less-padding color-container">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h6>
                    Pattern Colour
                    <a data-index="{{ element.jersey_patterns|length > 0 and element.jersey_patterns[0].pattern_colours|length }}"
                        data-prototype="{{ _self.colour_container(colour_prototype)|e }}"
                        href="javascript:;" class="add-colour-pattern">
                        <i class="fa fa-plus-circle pull-right"></i>
                    </a>
                </h6>
            </div>
            <div class="panel-body less-padding">
                <div class="color-pattern-container">
                    {% for pattern in element.jersey_patterns %}
                        {% for colour in pattern.pattern_colours %}
                            {{ _self.colour_container(colour) }}
                        {% endfor %}
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>