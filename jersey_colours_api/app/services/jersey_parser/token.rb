# frozen_string_literal: true

module JerseyParser
  class Token
    # Token types matching PHP application
    T_COLOUR = 'colour'
    T_PATTERN = 'pattern'
    T_ELEMENT = 'element'
    T_COMMA = 'comma'
    T_AND = 'and'
    T_UNKNOWN = 'unknown'

    attr_accessor :type, :value, :match, :original_term

    def initialize(type:, value:, match: nil, original_term: nil)
      @type = type
      @value = value
      @match = match || value
      @original_term = original_term || value
    end

    def to_h
      {
        token: type,
        value: value,
        match: match,
        original_term: original_term
      }
    end

    def self.comma_token
      new(type: T_COMMA, value: ',', match: ',')
    end

    def self.and_token
      new(type: T_AND, value: 'and', match: 'and')
    end

    def self.unknown_token(value)
      new(type: T_UNKNOWN, value: value, match: value)
    end

    def colour?
      type == T_COLOUR
    end

    def pattern?
      type == T_PATTERN
    end

    def element?
      type == T_ELEMENT
    end

    def comma?
      type == T_COMMA
    end

    def and?
      type == T_AND
    end

    def unknown?
      type == T_UNKNOWN
    end
  end
end
