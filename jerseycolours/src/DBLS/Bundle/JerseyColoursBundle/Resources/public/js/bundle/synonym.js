JockerColour = window.JockerColour || {};

JockerColour.Synonym = 
{
    init: function() 
    {
        this.configDelete();
        this.configNewSynonym();
        this.configEditSynonym();
    },

    configDelete: function() 
    {
        $(document).on('click', 'a.delete-item', function() {
            var id = $(this).data('id');
            bootbox.confirm("Are you sure you want to delete it?", function(result) {
                if (!result) return true;
                Form.showLoading();
                $.post(Routing.generate('synonym_delete', {
                    id: id
                })).done(function() {
                    Message.msgSuccess('Item was deleted.');
                    General.reload(3);
                }).fail(function() {
                    Message.msgError('Item could not be deleted.');
                }).always(function() {
                    Form.hideLoading();
                });
            });
        });
    },

    configNewSynonym: function()
    {
        $('.new-synonym').on(
            'click',
            function()
            {
                var settings = {
                    url: Routing.generate('synonym_new'), 
                    callback: JockerColour.Synonym.configSynonymForm
                };

                JockerColour.Synonym.modalSynonym(settings);
            }
        );
    },

    configEditSynonym: function()
    {
        $('.edit-synonym').on(
            'click',
            function()
            {
                var id = $(this).data('id');
                var settings = {
                    url: Routing.generate('synonym_edit', {id: id}), 
                    callback: JockerColour.Synonym.configSynonymForm
                };

                JockerColour.Synonym.modalSynonym(settings);
            }
        );
    },

    configSynonymForm: function(modal)
    {
        var form  = $( 'form', modal );

        $('.btn.reset', form).on(
            'click',
            function()
            {
                modal.modal('hide');
            }
        );

        submit = function()
        {
            form.find('[type="submit"]').attr('disabled', true);

            var obj = {
                callback: JockerColour.Synonym.afterSaveSynonym,
                callbackError: function()
                {
                    form.find('[type="submit"]').removeAttr('disabled');
                }
            };

            Form.submitAjax( form, obj );
            return false;
        };

        Form.addValidate(form, submit);
    },

    afterSaveSynonym: function(response)
    {
        var form = $(response.form);
        form.find('[type="submit"]').removeAttr('disabled');
        
        if ( !response.status ){
            Message.msgError(response.description,form);
            return false;
        }

        General.reload(3);
    },

    modalSynonym: function(settings)
    {
        var modal = $('#model-modal').clone();
        var body = modal.find('.modal-body');
        
        modal.find('.modal-title').html('Synonym');

        body.empty();

        Message.setFadeOut(false).msgInfo('Loading...', body);

        modal.find('.modal-dialog').removeClass('modal-lg');
        modal.modal('show');
        body.load(settings.url,
            function()
            {
                if (settings.callback && typeof(settings.callback) == 'function')
                    settings.callback.call(this, modal, settings);

                JockerColour.Synonym.configSelectOriginalTerm(modal);
            }
        );
    },

    configSelectOriginalTerm: function(modal)
    {
        General.waitTo(
            function()
            {
                var format = function(o)
                {
                    switch(o.type) {
                        case 1:
                            if ( !o.color ) {
                                return o.text;
                            } else {
                                return "<span class='colour-square square-select' style='background-color: " + o.color + "'></span> " + o.text;
                            }
                        case 2:
                            return '<i class="fa fa-puzzle-piece"></i> ' + o.text;
                        case 3:
                            return '<i class="fa fa-tags"></i> ' + o.text;
                        default:
                            return o.text;
                    }
                };

                $("select.original-term-select").select2(
                    {
                        ajax: {
                            url: Routing.generate('get_original_terms'),
                            dataType: 'json',
                            delay: 0,
                            data: function (params)
                            {
                                return {
                                    term: params.term, // search term
                                    page: params.page
                                };
                            },
                            processResults: function (data, page)
                            {
                                return {
                                    results: data
                                };
                            },
                            cache: true
                        },
                        allowClear: false,
                        minimumInputLength: 2,
                        required: true,
                        placeholder: '-- Select an original term --',
                        templateResult: format,
                        templateSelection: format,
                        escapeMarkup: function(m) { return m; }
                    }
                );
            },
            1
        );
    }
};

$(document).ready(
    function() 
    {
        JockerColour.Synonym.init();
    }
);