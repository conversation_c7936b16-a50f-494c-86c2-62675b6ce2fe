<?php

namespace DBLS\Bundle\JerseyColoursBundle\Validator\Constraints;

use Symfony\Component\Validator\Constraint;

/**
 * Annotation constraint to prevent entity duplicating
 *
 * @Annotation
 * <AUTHOR>
 */
class Unique extends Constraint {

    public $fields = array();
    public $repositoryClass = "";

    public function validatedBy() {
        return 'unique.validator';
    }

    public function getTargets() {
        return self::CLASS_CONSTRAINT;
    }

}
