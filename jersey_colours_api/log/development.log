  [1m[35m (129.5ms)[0m  [1m[35mCREATE DATABASE "jersey_colours_api_development" ENCODING = 'unicode' /*application='JerseyColoursApi'*/[0m
  [1m[35m (202.0ms)[0m  [1m[35mCREATE DATABASE "jersey_colours_api_development" ENCODING = 'unicode' /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.3ms)[0m  [1m[35mCREATE TABLE "schema_migrations" ("version" varchar NOT NULL PRIMARY KEY) /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE TABLE "ar_internal_metadata" ("key" varchar NOT NULL PRIMARY KEY, "value" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL) /*application='JerseyColoursApi'*/[0m
  [1m[36mActiveRecord::InternalMetadata Load (0.0ms)[0m  [1m[34mSELECT * FROM "ar_internal_metadata" WHERE "ar_internal_metadata"."key" = 'environment' ORDER BY "ar_internal_metadata"."key" ASC LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mActiveRecord::InternalMetadata Create (0.1ms)[0m  [1m[32mINSERT INTO "ar_internal_metadata" ("key", "value", "created_at", "updated_at") VALUES ('environment', 'development', '2025-07-08 10:07:01.680709', '2025-07-08 10:07:01.680710') RETURNING "key" /*application='JerseyColoursApi'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='JerseyColoursApi'*/[0m
Migrating to CreateUsers (20250708095641)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.6ms)[0m  [1m[35mCREATE TABLE "users" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "email" varchar, "password_digest" varchar, "first_name" varchar, "last_name" varchar, "role" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL) /*application='JerseyColoursApi'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.0ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250708095641') RETURNING "version" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
Migrating to CreateColours (20250708095649)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.5ms)[0m  [1m[35mCREATE TABLE "colours" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "name" varchar, "cmyk" varchar, "hexadecimal" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL) /*application='JerseyColoursApi'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.0ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250708095649') RETURNING "version" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
Migrating to CreateElementTypes (20250708095654)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.5ms)[0m  [1m[35mCREATE TABLE "element_types" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "name" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL) /*application='JerseyColoursApi'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.0ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250708095654') RETURNING "version" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
Migrating to CreatePatterns (20250708095700)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE TABLE "patterns" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "description" varchar, "path_svg" varchar, "side_path_svg" varchar, "status" boolean, "element_type_id" integer NOT NULL, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_3d778f06ca"
FOREIGN KEY ("element_type_id")
  REFERENCES "element_types" ("id")
) /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_patterns_on_element_type_id" ON "patterns" ("element_type_id") /*application='JerseyColoursApi'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.0ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250708095700') RETURNING "version" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
Migrating to CreateMeetings (20250708095707)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE TABLE "meetings" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "external_id" varchar, "date" date, "location" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL) /*application='JerseyColoursApi'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.0ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250708095707') RETURNING "version" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
Migrating to CreateRaces (20250708095712)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE TABLE "races" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "name" varchar, "external_race_id" varchar, "external_meet_id" varchar, "number" integer, "racetime" datetime(6), "meeting_id" integer NOT NULL, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_15e127971e"
FOREIGN KEY ("meeting_id")
  REFERENCES "meetings" ("id")
) /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_races_on_meeting_id" ON "races" ("meeting_id") /*application='JerseyColoursApi'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.0ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250708095712') RETURNING "version" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
Migrating to CreateTrainers (20250708095718)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE TABLE "trainers" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "name" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL) /*application='JerseyColoursApi'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.0ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250708095718') RETURNING "version" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
Migrating to CreateJerseys (20250708095725)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.6ms)[0m  [1m[35mCREATE TABLE "jerseys" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "name" varchar, "description" varchar, "string_value" text, "status" boolean, "parsed" boolean, "parsing" boolean, "parsing_time" datetime(6), "external_jersey_id" varchar, "number" integer, "race_id" integer NOT NULL, "trainer_id" integer NOT NULL, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_fd427e406d"
FOREIGN KEY ("race_id")
  REFERENCES "races" ("id")
, CONSTRAINT "fk_rails_5afe03b12d"
FOREIGN KEY ("trainer_id")
  REFERENCES "trainers" ("id")
) /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_jerseys_on_race_id" ON "jerseys" ("race_id") /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_jerseys_on_trainer_id" ON "jerseys" ("trainer_id") /*application='JerseyColoursApi'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.0ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250708095725') RETURNING "version" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
Migrating to CreateJerseyElements (20250708095734)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE TABLE "jersey_elements" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "order" integer, "jersey_id" integer NOT NULL, "element_type_id" integer NOT NULL, "colour_id" integer NOT NULL, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_e1037fe411"
FOREIGN KEY ("jersey_id")
  REFERENCES "jerseys" ("id")
, CONSTRAINT "fk_rails_86f7bef1d4"
FOREIGN KEY ("element_type_id")
  REFERENCES "element_types" ("id")
, CONSTRAINT "fk_rails_e49bc668e8"
FOREIGN KEY ("colour_id")
  REFERENCES "colours" ("id")
) /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_jersey_elements_on_jersey_id" ON "jersey_elements" ("jersey_id") /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_jersey_elements_on_element_type_id" ON "jersey_elements" ("element_type_id") /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_jersey_elements_on_colour_id" ON "jersey_elements" ("colour_id") /*application='JerseyColoursApi'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.0ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250708095734') RETURNING "version" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
Migrating to CreateJerseyElementPatterns (20250708095740)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE TABLE "jersey_element_patterns" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "jersey_element_id" integer NOT NULL, "pattern_id" integer NOT NULL, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_3c2b9497c6"
FOREIGN KEY ("jersey_element_id")
  REFERENCES "jersey_elements" ("id")
, CONSTRAINT "fk_rails_1c24ab39aa"
FOREIGN KEY ("pattern_id")
  REFERENCES "patterns" ("id")
) /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_jersey_element_patterns_on_jersey_element_id" ON "jersey_element_patterns" ("jersey_element_id") /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_jersey_element_patterns_on_pattern_id" ON "jersey_element_patterns" ("pattern_id") /*application='JerseyColoursApi'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.0ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250708095740') RETURNING "version" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
Migrating to CreateJerseyPatternColours (20250708095745)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.4ms)[0m  [1m[35mCREATE TABLE "jersey_pattern_colours" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "order" integer, "jersey_element_pattern_id" integer NOT NULL, "colour_id" integer NOT NULL, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_5e076b03b3"
FOREIGN KEY ("jersey_element_pattern_id")
  REFERENCES "jersey_element_patterns" ("id")
, CONSTRAINT "fk_rails_efeb7268ed"
FOREIGN KEY ("colour_id")
  REFERENCES "colours" ("id")
) /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_jersey_pattern_colours_on_jersey_element_pattern_id" ON "jersey_pattern_colours" ("jersey_element_pattern_id") /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_jersey_pattern_colours_on_colour_id" ON "jersey_pattern_colours" ("colour_id") /*application='JerseyColoursApi'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.0ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250708095745') RETURNING "version" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
Migrating to CreateSynonyms (20250708095751)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.6ms)[0m  [1m[35mCREATE TABLE "synonyms" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "term" varchar, "original_term" varchar, "colour_id" integer NOT NULL, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_52ba4b3d79"
FOREIGN KEY ("colour_id")
  REFERENCES "colours" ("id")
) /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.0ms)[0m  [1m[35mCREATE INDEX "index_synonyms_on_colour_id" ON "synonyms" ("colour_id") /*application='JerseyColoursApi'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.0ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250708095751') RETURNING "version" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
Migrating to CreateErrors (20250708095757)
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.6ms)[0m  [1m[35mCREATE TABLE "errors" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "type" varchar, "message" text, "expression" varchar, "details" text, "jersey_id" integer NOT NULL, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_c592e34154"
FOREIGN KEY ("jersey_id")
  REFERENCES "jerseys" ("id")
) /*application='JerseyColoursApi'*/[0m
  [1m[35m (0.1ms)[0m  [1m[35mCREATE INDEX "index_errors_on_jersey_id" ON "errors" ("jersey_id") /*application='JerseyColoursApi'*/[0m
  [1m[36mActiveRecord::SchemaMigration Create (0.1ms)[0m  [1m[32mINSERT INTO "schema_migrations" ("version") VALUES ('20250708095757') RETURNING "version" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.0ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='JerseyColoursApi'*/[0m
  [1m[36mActiveRecord::SchemaMigration Load (0.1ms)[0m  [1m[34mSELECT "schema_migrations"."version" FROM "schema_migrations" ORDER BY "schema_migrations"."version" ASC /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Load (0.0ms)[0m  [1m[34mSELECT "element_types".* FROM "element_types" WHERE "element_types"."id" = 1 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Exists? (0.0ms)[0m  [1m[34mSELECT 1 AS one FROM "element_types" WHERE LOWER("element_types"."name") = LOWER('base') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Create (0.1ms)[0m  [1m[32mINSERT INTO "element_types" ("id", "name", "created_at", "updated_at") VALUES (1, 'base', '2025-07-08 10:07:41.091426', '2025-07-08 10:07:41.091426') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.3ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Load (0.0ms)[0m  [1m[34mSELECT "element_types".* FROM "element_types" WHERE "element_types"."id" = 2 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Exists? (0.6ms)[0m  [1m[34mSELECT 1 AS one FROM "element_types" WHERE LOWER("element_types"."name") = LOWER('shirt') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Create (0.1ms)[0m  [1m[32mINSERT INTO "element_types" ("id", "name", "created_at", "updated_at") VALUES (2, 'shirt', '2025-07-08 10:07:41.095239', '2025-07-08 10:07:41.095239') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Load (0.0ms)[0m  [1m[34mSELECT "element_types".* FROM "element_types" WHERE "element_types"."id" = 3 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Exists? (0.6ms)[0m  [1m[34mSELECT 1 AS one FROM "element_types" WHERE LOWER("element_types"."name") = LOWER('cap') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Create (0.0ms)[0m  [1m[32mINSERT INTO "element_types" ("id", "name", "created_at", "updated_at") VALUES (3, 'cap', '2025-07-08 10:07:41.098038', '2025-07-08 10:07:41.098038') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Load (0.0ms)[0m  [1m[34mSELECT "element_types".* FROM "element_types" WHERE "element_types"."id" = 4 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Exists? (0.7ms)[0m  [1m[34mSELECT 1 AS one FROM "element_types" WHERE LOWER("element_types"."name") = LOWER('peak') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Create (0.1ms)[0m  [1m[32mINSERT INTO "element_types" ("id", "name", "created_at", "updated_at") VALUES (4, 'peak', '2025-07-08 10:07:41.100778', '2025-07-08 10:07:41.100778') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Load (0.0ms)[0m  [1m[34mSELECT "element_types".* FROM "element_types" WHERE "element_types"."id" = 5 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Exists? (0.6ms)[0m  [1m[34mSELECT 1 AS one FROM "element_types" WHERE LOWER("element_types"."name") = LOWER('sleeves') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Create (0.0ms)[0m  [1m[32mINSERT INTO "element_types" ("id", "name", "created_at", "updated_at") VALUES (5, 'sleeves', '2025-07-08 10:07:41.103569', '2025-07-08 10:07:41.103569') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Load (0.0ms)[0m  [1m[34mSELECT "element_types".* FROM "element_types" WHERE "element_types"."id" = 6 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Exists? (0.5ms)[0m  [1m[34mSELECT 1 AS one FROM "element_types" WHERE LOWER("element_types"."name") = LOWER('collar') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Create (0.0ms)[0m  [1m[32mINSERT INTO "element_types" ("id", "name", "created_at", "updated_at") VALUES (6, 'collar', '2025-07-08 10:07:41.106044', '2025-07-08 10:07:41.106044') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Load (0.0ms)[0m  [1m[34mSELECT "element_types".* FROM "element_types" WHERE "element_types"."id" = 7 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Exists? (0.6ms)[0m  [1m[34mSELECT 1 AS one FROM "element_types" WHERE LOWER("element_types"."name") = LOWER('cuffs') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Create (0.0ms)[0m  [1m[32mINSERT INTO "element_types" ("id", "name", "created_at", "updated_at") VALUES (7, 'cuffs', '2025-07-08 10:07:41.108772', '2025-07-08 10:07:41.108772') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Load (0.0ms)[0m  [1m[34mSELECT "element_types".* FROM "element_types" WHERE "element_types"."id" = 8 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Exists? (0.6ms)[0m  [1m[34mSELECT 1 AS one FROM "element_types" WHERE LOWER("element_types"."name") = LOWER('tassel') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Create (0.0ms)[0m  [1m[32mINSERT INTO "element_types" ("id", "name", "created_at", "updated_at") VALUES (8, 'tassel', '2025-07-08 10:07:41.111887', '2025-07-08 10:07:41.111887') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Load (0.0ms)[0m  [1m[34mSELECT "element_types".* FROM "element_types" WHERE "element_types"."id" = 9 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Exists? (0.7ms)[0m  [1m[34mSELECT 1 AS one FROM "element_types" WHERE LOWER("element_types"."name") = LOWER('pompom') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Create (0.1ms)[0m  [1m[32mINSERT INTO "element_types" ("id", "name", "created_at", "updated_at") VALUES (9, 'pompom', '2025-07-08 10:07:41.118477', '2025-07-08 10:07:41.118477') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Load (0.0ms)[0m  [1m[34mSELECT "element_types".* FROM "element_types" WHERE "element_types"."id" = 10 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Exists? (0.6ms)[0m  [1m[34mSELECT 1 AS one FROM "element_types" WHERE LOWER("element_types"."name") = LOWER('armbands') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Create (0.0ms)[0m  [1m[32mINSERT INTO "element_types" ("id", "name", "created_at", "updated_at") VALUES (10, 'armbands', '2025-07-08 10:07:41.122458', '2025-07-08 10:07:41.122458') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Load (0.0ms)[0m  [1m[34mSELECT "element_types".* FROM "element_types" WHERE "element_types"."id" = 11 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Exists? (1.1ms)[0m  [1m[34mSELECT 1 AS one FROM "element_types" WHERE LOWER("element_types"."name") = LOWER('seams') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Create (0.1ms)[0m  [1m[32mINSERT INTO "element_types" ("id", "name", "created_at", "updated_at") VALUES (11, 'seams', '2025-07-08 10:07:41.125723', '2025-07-08 10:07:41.125723') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Load (0.0ms)[0m  [1m[34mSELECT "element_types".* FROM "element_types" WHERE "element_types"."id" = 12 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Exists? (0.6ms)[0m  [1m[34mSELECT 1 AS one FROM "element_types" WHERE LOWER("element_types"."name") = LOWER('sash') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Create (0.0ms)[0m  [1m[32mINSERT INTO "element_types" ("id", "name", "created_at", "updated_at") VALUES (12, 'sash', '2025-07-08 10:07:41.128818', '2025-07-08 10:07:41.128818') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Load (0.0ms)[0m  [1m[34mSELECT "element_types".* FROM "element_types" WHERE "element_types"."id" = 13 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Exists? (0.6ms)[0m  [1m[34mSELECT 1 AS one FROM "element_types" WHERE LOWER("element_types"."name") = LOWER('epaulettes') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Create (0.4ms)[0m  [1m[32mINSERT INTO "element_types" ("id", "name", "created_at", "updated_at") VALUES (13, 'epaulettes', '2025-07-08 10:07:41.131481', '2025-07-08 10:07:41.131481') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Load (0.0ms)[0m  [1m[34mSELECT "element_types".* FROM "element_types" WHERE "element_types"."id" = 14 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Exists? (0.6ms)[0m  [1m[34mSELECT 1 AS one FROM "element_types" WHERE LOWER("element_types"."name") = LOWER('crossed sashes') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Create (0.1ms)[0m  [1m[32mINSERT INTO "element_types" ("id", "name", "created_at", "updated_at") VALUES (14, 'crossed sashes', '2025-07-08 10:07:41.135023', '2025-07-08 10:07:41.135023') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Count (0.0ms)[0m  [1m[34mSELECT COUNT(*) FROM "element_types" /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Load (0.0ms)[0m  [1m[34mSELECT "colours".* FROM "colours" WHERE "colours"."name" = 'red' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.0ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE LOWER("colours"."name") = LOWER('red') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.0ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE "colours"."hexadecimal" = '#FF0000' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Create (0.1ms)[0m  [1m[32mINSERT INTO "colours" ("name", "cmyk", "hexadecimal", "created_at", "updated_at") VALUES ('red', '0,100,100,0', '#FF0000', '2025-07-08 10:07:41.143166', '2025-07-08 10:07:41.143166') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Load (0.0ms)[0m  [1m[34mSELECT "colours".* FROM "colours" WHERE "colours"."name" = 'blue' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.6ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE LOWER("colours"."name") = LOWER('blue') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.0ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE "colours"."hexadecimal" = '#0000FF' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Create (0.1ms)[0m  [1m[32mINSERT INTO "colours" ("name", "cmyk", "hexadecimal", "created_at", "updated_at") VALUES ('blue', '100,100,0,0', '#0000FF', '2025-07-08 10:07:41.148223', '2025-07-08 10:07:41.148223') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Load (0.0ms)[0m  [1m[34mSELECT "colours".* FROM "colours" WHERE "colours"."name" = 'green' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.6ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE LOWER("colours"."name") = LOWER('green') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.0ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE "colours"."hexadecimal" = '#00FF00' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Create (0.1ms)[0m  [1m[32mINSERT INTO "colours" ("name", "cmyk", "hexadecimal", "created_at", "updated_at") VALUES ('green', '100,0,100,0', '#00FF00', '2025-07-08 10:07:41.152067', '2025-07-08 10:07:41.152067') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Load (0.1ms)[0m  [1m[34mSELECT "colours".* FROM "colours" WHERE "colours"."name" = 'yellow' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (2.1ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE LOWER("colours"."name") = LOWER('yellow') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.0ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE "colours"."hexadecimal" = '#FFFF00' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Create (0.0ms)[0m  [1m[32mINSERT INTO "colours" ("name", "cmyk", "hexadecimal", "created_at", "updated_at") VALUES ('yellow', '0,0,100,0', '#FFFF00', '2025-07-08 10:07:41.157417', '2025-07-08 10:07:41.157417') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Load (0.1ms)[0m  [1m[34mSELECT "colours".* FROM "colours" WHERE "colours"."name" = 'black' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.7ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE LOWER("colours"."name") = LOWER('black') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.0ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE "colours"."hexadecimal" = '#000000' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Create (0.1ms)[0m  [1m[32mINSERT INTO "colours" ("name", "cmyk", "hexadecimal", "created_at", "updated_at") VALUES ('black', '0,0,0,100', '#000000', '2025-07-08 10:07:41.162975', '2025-07-08 10:07:41.162975') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Load (0.0ms)[0m  [1m[34mSELECT "colours".* FROM "colours" WHERE "colours"."name" = 'white' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.6ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE LOWER("colours"."name") = LOWER('white') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.0ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE "colours"."hexadecimal" = '#FFFFFF' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Create (0.1ms)[0m  [1m[32mINSERT INTO "colours" ("name", "cmyk", "hexadecimal", "created_at", "updated_at") VALUES ('white', '0,0,0,0', '#FFFFFF', '2025-07-08 10:07:41.166955', '2025-07-08 10:07:41.166955') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Load (0.0ms)[0m  [1m[34mSELECT "colours".* FROM "colours" WHERE "colours"."name" = 'orange' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.7ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE LOWER("colours"."name") = LOWER('orange') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.0ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE "colours"."hexadecimal" = '#FF8000' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Create (0.0ms)[0m  [1m[32mINSERT INTO "colours" ("name", "cmyk", "hexadecimal", "created_at", "updated_at") VALUES ('orange', '0,50,100,0', '#FF8000', '2025-07-08 10:07:41.170682', '2025-07-08 10:07:41.170682') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Load (0.0ms)[0m  [1m[34mSELECT "colours".* FROM "colours" WHERE "colours"."name" = 'purple' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.5ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE LOWER("colours"."name") = LOWER('purple') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.0ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE "colours"."hexadecimal" = '#8000FF' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Create (0.0ms)[0m  [1m[32mINSERT INTO "colours" ("name", "cmyk", "hexadecimal", "created_at", "updated_at") VALUES ('purple', '50,100,0,0', '#8000FF', '2025-07-08 10:07:41.174286', '2025-07-08 10:07:41.174286') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Load (0.0ms)[0m  [1m[34mSELECT "colours".* FROM "colours" WHERE "colours"."name" = 'pink' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.9ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE LOWER("colours"."name") = LOWER('pink') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.0ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE "colours"."hexadecimal" = '#FF80FF' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Create (0.1ms)[0m  [1m[32mINSERT INTO "colours" ("name", "cmyk", "hexadecimal", "created_at", "updated_at") VALUES ('pink', '0,50,0,0', '#FF80FF', '2025-07-08 10:07:41.178067', '2025-07-08 10:07:41.178067') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Load (0.0ms)[0m  [1m[34mSELECT "colours".* FROM "colours" WHERE "colours"."name" = 'brown' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.5ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE LOWER("colours"."name") = LOWER('brown') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.0ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE "colours"."hexadecimal" = '#8B4513' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Create (0.0ms)[0m  [1m[32mINSERT INTO "colours" ("name", "cmyk", "hexadecimal", "created_at", "updated_at") VALUES ('brown', '30,70,100,15', '#8B4513', '2025-07-08 10:07:41.181617', '2025-07-08 10:07:41.181617') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Load (0.1ms)[0m  [1m[34mSELECT "colours".* FROM "colours" WHERE "colours"."name" = 'grey' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.7ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE LOWER("colours"."name") = LOWER('grey') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.0ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE "colours"."hexadecimal" = '#808080' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Create (0.1ms)[0m  [1m[32mINSERT INTO "colours" ("name", "cmyk", "hexadecimal", "created_at", "updated_at") VALUES ('grey', '0,0,0,50', '#808080', '2025-07-08 10:07:41.186353', '2025-07-08 10:07:41.186353') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Load (0.0ms)[0m  [1m[34mSELECT "colours".* FROM "colours" WHERE "colours"."name" = 'maroon' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.5ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE LOWER("colours"."name") = LOWER('maroon') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.0ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE "colours"."hexadecimal" = '#800000' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Create (0.0ms)[0m  [1m[32mINSERT INTO "colours" ("name", "cmyk", "hexadecimal", "created_at", "updated_at") VALUES ('maroon', '0,100,100,50', '#800000', '2025-07-08 10:07:41.190756', '2025-07-08 10:07:41.190756') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Load (0.0ms)[0m  [1m[34mSELECT "colours".* FROM "colours" WHERE "colours"."name" = 'navy' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (1.3ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE LOWER("colours"."name") = LOWER('navy') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.0ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE "colours"."hexadecimal" = '#000080' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Create (0.0ms)[0m  [1m[32mINSERT INTO "colours" ("name", "cmyk", "hexadecimal", "created_at", "updated_at") VALUES ('navy', '100,100,0,50', '#000080', '2025-07-08 10:07:41.195201', '2025-07-08 10:07:41.195201') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Load (0.0ms)[0m  [1m[34mSELECT "colours".* FROM "colours" WHERE "colours"."name" = 'royal blue' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.6ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE LOWER("colours"."name") = LOWER('royal blue') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.0ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE "colours"."hexadecimal" = '#4169E1' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Create (0.0ms)[0m  [1m[32mINSERT INTO "colours" ("name", "cmyk", "hexadecimal", "created_at", "updated_at") VALUES ('royal blue', '100,75,0,0', '#4169E1', '2025-07-08 10:07:41.199683', '2025-07-08 10:07:41.199683') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Load (0.0ms)[0m  [1m[34mSELECT "colours".* FROM "colours" WHERE "colours"."name" = 'emerald green' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.7ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE LOWER("colours"."name") = LOWER('emerald green') LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Exists? (0.0ms)[0m  [1m[34mSELECT 1 AS one FROM "colours" WHERE "colours"."hexadecimal" = '#50C878' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Create (0.0ms)[0m  [1m[32mINSERT INTO "colours" ("name", "cmyk", "hexadecimal", "created_at", "updated_at") VALUES ('emerald green', '85,0,100,0', '#50C878', '2025-07-08 10:07:41.203566', '2025-07-08 10:07:41.203566') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mColour Count (0.0ms)[0m  [1m[34mSELECT COUNT(*) FROM "colours" /*application='JerseyColoursApi'*/[0m
  [1m[36mPattern Load (0.0ms)[0m  [1m[34mSELECT "patterns".* FROM "patterns" WHERE "patterns"."description" = 'stripes' AND "patterns"."element_type_id" = 2 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Load (0.1ms)[0m  [1m[34mSELECT "element_types".* FROM "element_types" WHERE "element_types"."id" = 2 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mPattern Create (0.1ms)[0m  [1m[32mINSERT INTO "patterns" ("description", "path_svg", "side_path_svg", "status", "element_type_id", "created_at", "updated_at") VALUES ('stripes', 'stripes.svg', 'stripes_side.svg', 1, 2, '2025-07-08 10:07:41.220676', '2025-07-08 10:07:41.220676') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mPattern Load (0.0ms)[0m  [1m[34mSELECT "patterns".* FROM "patterns" WHERE "patterns"."description" = 'spots' AND "patterns"."element_type_id" = 2 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Load (0.8ms)[0m  [1m[34mSELECT "element_types".* FROM "element_types" WHERE "element_types"."id" = 2 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mPattern Create (0.0ms)[0m  [1m[32mINSERT INTO "patterns" ("description", "path_svg", "side_path_svg", "status", "element_type_id", "created_at", "updated_at") VALUES ('spots', 'spots.svg', 'spots_side.svg', 1, 2, '2025-07-08 10:07:41.224430', '2025-07-08 10:07:41.224430') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mPattern Load (0.0ms)[0m  [1m[34mSELECT "patterns".* FROM "patterns" WHERE "patterns"."description" = 'diamonds' AND "patterns"."element_type_id" = 2 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Load (0.7ms)[0m  [1m[34mSELECT "element_types".* FROM "element_types" WHERE "element_types"."id" = 2 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mPattern Create (0.1ms)[0m  [1m[32mINSERT INTO "patterns" ("description", "path_svg", "side_path_svg", "status", "element_type_id", "created_at", "updated_at") VALUES ('diamonds', 'diamonds.svg', 'diamonds_side.svg', 1, 2, '2025-07-08 10:07:41.227889', '2025-07-08 10:07:41.227889') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mPattern Load (0.0ms)[0m  [1m[34mSELECT "patterns".* FROM "patterns" WHERE "patterns"."description" = 'hoops' AND "patterns"."element_type_id" = 2 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Load (0.6ms)[0m  [1m[34mSELECT "element_types".* FROM "element_types" WHERE "element_types"."id" = 2 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mPattern Create (0.0ms)[0m  [1m[32mINSERT INTO "patterns" ("description", "path_svg", "side_path_svg", "status", "element_type_id", "created_at", "updated_at") VALUES ('hoops', 'hoops.svg', 'hoops_side.svg', 1, 2, '2025-07-08 10:07:41.230761', '2025-07-08 10:07:41.230761') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mPattern Load (0.0ms)[0m  [1m[34mSELECT "patterns".* FROM "patterns" WHERE "patterns"."description" = 'chevrons' AND "patterns"."element_type_id" = 2 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Load (0.6ms)[0m  [1m[34mSELECT "element_types".* FROM "element_types" WHERE "element_types"."id" = 2 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mPattern Create (0.0ms)[0m  [1m[32mINSERT INTO "patterns" ("description", "path_svg", "side_path_svg", "status", "element_type_id", "created_at", "updated_at") VALUES ('chevrons', 'chevrons.svg', 'chevrons_side.svg', 1, 2, '2025-07-08 10:07:41.233466', '2025-07-08 10:07:41.233466') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mPattern Load (0.0ms)[0m  [1m[34mSELECT "patterns".* FROM "patterns" WHERE "patterns"."description" = 'stars' AND "patterns"."element_type_id" = 2 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mElementType Load (0.6ms)[0m  [1m[34mSELECT "element_types".* FROM "element_types" WHERE "element_types"."id" = 2 LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mPattern Create (0.0ms)[0m  [1m[32mINSERT INTO "patterns" ("description", "path_svg", "side_path_svg", "status", "element_type_id", "created_at", "updated_at") VALUES ('stars', 'stars.svg', 'stars_side.svg', 1, 2, '2025-07-08 10:07:41.237825', '2025-07-08 10:07:41.237825') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mPattern Count (0.0ms)[0m  [1m[34mSELECT COUNT(*) FROM "patterns" /*application='JerseyColoursApi'*/[0m
  [1m[36mUser Load (0.0ms)[0m  [1m[34mSELECT "users".* FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.0ms)[0m  [1m[35mBEGIN immediate TRANSACTION /*application='JerseyColoursApi'*/[0m
  [1m[36mUser Exists? (0.1ms)[0m  [1m[34mSELECT 1 AS one FROM "users" WHERE "users"."email" = '<EMAIL>' LIMIT 1 /*application='JerseyColoursApi'*/[0m
  [1m[36mUser Create (0.1ms)[0m  [1m[32mINSERT INTO "users" ("email", "password_digest", "first_name", "last_name", "role", "created_at", "updated_at") VALUES ('<EMAIL>', '$2a$12$yFuhKhcM2tjSIntotcLU4.7g77oINThBow0NGYeCFm9IdUXNEGwHG', 'Admin', 'User', 'admin', '2025-07-08 10:07:41.486006', '2025-07-08 10:07:41.486006') RETURNING "id" /*application='JerseyColoursApi'*/[0m
  [1m[36mTRANSACTION (0.1ms)[0m  [1m[35mCOMMIT TRANSACTION /*application='JerseyColoursApi'*/[0m
