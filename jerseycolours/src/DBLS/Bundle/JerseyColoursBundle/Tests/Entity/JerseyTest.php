<?php

namespace DBLS\Bundle\JerseyColoursBundle\Tests\Entity;

use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

use DBLS\Bundle\JerseyColoursBundle\Entity\ElementType;
use DBLS\Bundle\JerseyColoursBundle\Entity\Jersey;
use DBLS\Bundle\JerseyColoursBundle\Entity\Error;
use DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElement;
use DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElementPattern;
use DBLS\Bundle\JerseyColoursBundle\Entity\JerseyPatternColour;

/**
 *
 * <AUTHOR>
 */
class JerseyTest extends WebTestCase
{
    /**
     *
     * DI Container
     */
    static $container;

    /**
     * @var \Doctrine\ORM\EntityManager
     */
    private $em;

    /**
     *
     * @var DBLS\Bundle\JerseyColoursBundle\Services\JerseyParserService
     */
    static $service;

    /**
     * {@inheritDoc}
     */
    public function setUp()
    {
        self::bootKernel();
        $this->em = static::$kernel->getContainer()
            ->get('doctrine')
            ->getManager()
        ;
    }

    /**
     * {@inheritDoc}
     */
    protected function tearDown()
    {
        parent::tearDown();
        $this->em->close();
    }

    /**
     * {@inheritDoc}
     */
    public static function setUpBeforeClass()
    {
         //start the symfony kernel
         $kernel = static::createKernel();
         $kernel->boot();

         //get the DI container
         self::$container = $kernel->getContainer();

         self::$service = self::$container->get('services.jersey_parser');
    }

    public function testGenerateString1() {
        $jersey = new Jersey();
        $jersey->setName('Alberdini');

        $element = new JerseyElement();
        $element->setElementType($this->em->getRepository('JerseyColoursBundle:ElementType')->find(ElementType::BASE));
        $element->setColour($this->em->getRepository('JerseyColoursBundle:Colour')->findOneBy(array('name' => 'black')));
        $jersey->getElements()->add($element);

        $element = new JerseyElement();
        $element->setElementType($this->em->getRepository('JerseyColoursBundle:ElementType')->find(ElementType::CUFFS));
        $element->setColour($this->em->getRepository('JerseyColoursBundle:Colour')->findOneBy(array('name' => 'white')));
        $jersey->getElements()->add($element);

        $element = new JerseyElement();
        $element->setElementType($this->em->getRepository('JerseyColoursBundle:ElementType')->find(ElementType::CAP));
        $element->setColour($this->em->getRepository('JerseyColoursBundle:Colour')->findOneBy(array('name' => 'red')));
        $jersey->getElements()->add($element);

        $element = new JerseyElement();
        $element->setElementType($this->em->getRepository('JerseyColoursBundle:ElementType')->find(ElementType::SLEEVES));
        $elementPattern = new JerseyElementPattern();
        $elementPattern->setPattern($this->em->getRepository('JerseyColoursBundle:Pattern')->findByDescriptionAndElement('hooped', 'sleeves'));
        $patternColour = new JerseyPatternColour();
        $patternColour->setOrder(1);
        $patternColour->setColour($this->em->getRepository('JerseyColoursBundle:Colour')->findOneBy(array('name' => 'purple')));
        $elementPattern->getPatternColours()->add($patternColour);
        $patternColour = new JerseyPatternColour();
        $patternColour->setOrder(1);
        $patternColour->setColour($this->em->getRepository('JerseyColoursBundle:Colour')->findOneBy(array('name' => 'aqua')));
        $elementPattern->getPatternColours()->add($patternColour);
        $patternColour = new JerseyPatternColour();
        $patternColour->setOrder(2);
        $patternColour->setColour($this->em->getRepository('JerseyColoursBundle:Colour')->findOneBy(array('name' => 'royal blue')));
        $elementPattern->getPatternColours()->add($patternColour);
        $element->getJerseyPatterns()->add($elementPattern);
        $jersey->getElements()->add($element);

        // add all other elements
        $this->addRemainingElements($jersey);

        $generatedString = $jersey->generateString();
        //var_dump($generatedString);die();

        $newJersey = self::$service->createJerseyFromString($generatedString);
        //$this->assertEquals(true, $jersey->isSameAs($newJersey), "Fail when trying to generate string for jersey 'Alberdini'.");
    }

    private function addRemainingElements($jersey) {
        $elements = $this->em->getRepository('JerseyColoursBundle:ElementType')->findAll();
        foreach ($elements as $element) {
            $idElemento = $element->getId();

            $foundElement = $jersey->getElements()->filter(
                function($entity) use ($idElemento) {
                    return $entity->getElementType()->getId() == $idElemento;
                }
            );

            if ( $foundElement->isEmpty() ) {
                $newElement = new JerseyElement();
                $newElement->setOrder($jersey->getElements()->count()+1);
                $newElement->setElementType($element);
                $jersey->getElements()->add($newElement);
            }
        }
    }

}
