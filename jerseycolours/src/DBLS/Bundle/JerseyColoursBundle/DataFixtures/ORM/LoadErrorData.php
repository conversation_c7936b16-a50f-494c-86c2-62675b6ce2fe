<?php

namespace DBLS\Bundle\JerseyColoursBundle\DataFixtures\ORM;

use Doctrine\Common\DataFixtures\AbstractFixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Common\Persistence\ObjectManager;
use DBLS\Bundle\JerseyColoursBundle\Entity\Error;

class LoadErrorData extends AbstractFixture implements OrderedFixtureInterface
{

    /**
     * {@inheritDoc}
     */
    public function load(ObjectManager $manager)
    {

        $error = new Error();
        $error->setJersey($this->getReference('jersey-thubiaan'));
        $error->setIdAPI('Adfd76677fsa87dfa');
        $error->setMessage('Ops... Error!!! And now????');
        $error->setType('T_PATTERN');
        
        $manager->persist($error);
        $manager->flush();
    }

    /**
     * {@inheritDoc}
     */
    public function getOrder()
    {
        return 11;
    }
    
}
