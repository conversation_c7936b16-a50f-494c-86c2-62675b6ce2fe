{% extends '::base.html.twig' %}

{% block stylesheets %}
    {% stylesheets 
        'bundles/jerseycolours/css/bootstrap.min.css'
        'bundles/jerseycolours/css/font-awesome.css'
        'bundles/jerseycolours/css/unicorn-login.css'
        filter='cssrewrite' %}
        <link rel="stylesheet" href="{{ asset_url }}" />
    {% endstylesheets %}
{% endblock stylesheets %}

{% block body %}
    
    <div id="container">
        <div id="logo">
            <img src="{{ asset('bundles/jerseycolours/img/logo.png') }}" alt="" />
        </div>
        <div id="loginbox">            
            <form id="loginform" action="{{ path("fos_user_security_check") }}" method="post">
                <input type="hidden" name="_csrf_token" value="{{ csrf_token }}" />
                <p>Enter username and password to continue.</p>                                
                <div class="input-group input-sm">
                    <span class="input-group-addon"><i class="fa fa-user"></i></span>
                    <input type="text" id="username" name="_username" class="form-control" value="{{ last_username }}" placeholder="{{ 'security.login.username'|trans({}, 'FOSUserBundle') }}" />
                </div>
                <div class="input-group">
                    <span class="input-group-addon"><i class="fa fa-lock"></i></span>
                    <input type="password" id="password" class="form-control" name="_password" required="required" placeholder="{{ 'security.login.password'|trans({}, 'FOSUserBundle') }}" />
                </div>
                <div class="form-actions clearfix">               
                    <input type="submit" id="_submit" name="_submit" class="btn btn-block btn-primary btn-default" value="{{ 'security.login.submit'|trans({}, 'FOSUserBundle') }}" />
                </div>   
            </form>                
        </div>
    </div>
                
    {% if error %}
        <div class="alert alert-danger">
                <strong>Error!</strong> <div>{{ error|trans({}, 'FOSUserBundle') }}</div>
        </div>                
    {% endif %}

{% endblock body %}
