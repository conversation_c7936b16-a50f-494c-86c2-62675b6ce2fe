<?php

namespace DBLS\Bundle\JerseyColoursBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class PdfGeneratorCommand extends ContainerAwareCommand
{
    protected $container;

    /**
     * 
     */
    protected function configure()
    {
        $this->setName('jersey:pdf')
             ->setDescription('Generate PDF');
    }

    /**
     * 
     * @param  InputInterface  $input
     * @param  OutputInterface $output
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->container = $this->getContainer();

        $pdfGenerator = $this->container->get('knp_snappy.image');
        $templating = $this->container->get('templating');

        $this->em = $this->container->get('doctrine.orm.entity_manager');
        $raceRepository = $this->em->getRepository('JerseyColoursBundle:Race');

        $race = $raceRepository->find(419);

        $htmlOutput = $templating->render('JerseyColoursBundle:Pdf:silks.html.twig', 
                            array('silks' => $race->getApprovedJerseys())
                        );

        $cleanRaceSlug = $race->getCleanFolder();
        $pdfFile = $race->getPdfName();

        $options = array(
            'width'         => '2000',
            'quality'       => '100'
        );

        $pdfGenerator->generateFromHtml( $htmlOutput, $pdfFile, $options, true );

        $i = new \Imagick($pdfFile);
        $i->transformimagecolorspace(\Imagick::COLORSPACE_CMYK);
        $i->setFormat('JPG');
        $i->writeImage($pdfFile);
    }
}