<?php

namespace DBLS\Bundle\JerseyColoursBundle\DataFixtures\ORM;

use Doctrine\Common\DataFixtures\AbstractFixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Common\Persistence\ObjectManager;
use DBLS\Bundle\JerseyColoursBundle\Entity\Pattern;

class LoadPatternData extends AbstractFixture implements OrderedFixtureInterface
{

    /**
     * {@inheritDoc}
     */
    public function load(ObjectManager $manager)
    {

        $Pattern1 = new Pattern();
        $Pattern1->setDescription('Checked');
        $Pattern1->setPathSVG('');
        $Pattern1->setElementType($this->getReference('elementType-cap'));
        $manager->persist($Pattern1);
        $manager->flush();
        $this->addReference('pattern-cap-checked', $Pattern1);

        $Pattern2 = new Pattern();
        $Pattern2->setDescription('Quartered');
        $Pattern2->setPathSVG('');
        $Pattern2->setElementType($this->getReference('elementType-cap'));
        $manager->persist($Pattern2);
        $manager->flush();
        $this->addReference('pattern-cap-quartered', $Pattern2);

        $Pattern3 = new Pattern();
        $Pattern3->setDescription('Core Circle');
        $Pattern3->setPathSVG('');
        $Pattern3->setElementType($this->getReference('elementType-shirt'));
        $manager->persist($Pattern3);
        $manager->flush();
        $this->addReference('pattern-core-circle', $Pattern3);

        $Pattern4 = new Pattern();
        $Pattern4->setDescription('Core Star');
        $Pattern4->setPathSVG('');
        $Pattern4->setElementType($this->getReference('elementType-shirt'));
        $manager->persist($Pattern4);
        $manager->flush();
        $this->addReference('pattern-core-star', $Pattern4);

        $Pattern5 = new Pattern();
        $Pattern5->setDescription('Checked');
        $Pattern5->setPathSVG('');
        $Pattern5->setElementType($this->getReference('elementType-sleeves'));
        $manager->persist($Pattern5);
        $manager->flush();
        $this->addReference('pattern-sleeve-checked', $Pattern5);

        $Pattern6 = new Pattern();
        $Pattern6->setDescription('Quartered');
        $Pattern6->setPathSVG('');
        $Pattern6->setElementType($this->getReference('elementType-sleeves'));
        $manager->persist($Pattern6);
        $manager->flush();
        $this->addReference('pattern-sleeve-quartered', $Pattern6);

        $Pattern7 = new Pattern();
        $Pattern7->setDescription('Hooped');
        $Pattern7->setPathSVG('');
        $Pattern7->setElementType($this->getReference('elementType-sleeves'));
        $manager->persist($Pattern7);
        $manager->flush();
        $this->addReference('pattern-sleeves-hooped', $Pattern7);

        $Pattern8 = new Pattern();
        $Pattern8->setDescription('Striped');
        $Pattern8->setPathSVG('');
        $Pattern8->setElementType($this->getReference('elementType-sleeves'));
        $manager->persist($Pattern8);
        $manager->flush();
        $this->addReference('pattern-sleeves-striped', $Pattern8);

        $Pattern9 = new Pattern();
        $Pattern9->setDescription('Star');
        $Pattern9->setPathSVG('');
        $Pattern9->setElementType($this->getReference('elementType-shirt'));
        $manager->persist($Pattern9);
        $manager->flush();
        $this->addReference('pattern-start-shirt', $Pattern9);

        $Pattern10 = new Pattern();
        $Pattern10->setDescription('Diagonal Stripes');
        $Pattern10->setPathSVG('');
        $Pattern10->setElementType($this->getReference('elementType-shirt'));
        $manager->persist($Pattern10);
        $manager->flush();
        $this->addReference('pattern-diagonal-stripes-shirt', $Pattern10);

        $Pattern11 = new Pattern();
        $Pattern11->setDescription('Maltese Cross');
        $Pattern11->setPathSVG('');
        $Pattern11->setElementType($this->getReference('elementType-shirt'));
        $manager->persist($Pattern11);
        $manager->flush();
        $this->addReference('pattern-maltese-cross-shirt', $Pattern11);

        $Pattern12 = new Pattern();
        $Pattern12->setDescription('Spots');
        $Pattern12->setPathSVG('');
        $Pattern12->setElementType($this->getReference('elementType-shirt'));
        $manager->persist($Pattern12);
        $manager->flush();
        $this->addReference('pattern-spots-shirt', $Pattern12);

        $Pattern13 = new Pattern();
        $Pattern13->setDescription('Fleur de Lys');
        $Pattern13->setPathSVG('');
        $Pattern13->setElementType($this->getReference('elementType-shirt'));
        $manager->persist($Pattern13);
        $manager->flush();
        $this->addReference('pattern-fleur-de-lys-shirt', $Pattern13);

        $Pattern14 = new Pattern();
        $Pattern14->setDescription('Diamond');
        $Pattern14->setPathSVG('');
        $Pattern14->setElementType($this->getReference('elementType-shirt'));
        $manager->persist($Pattern14);
        $manager->flush();
        $this->addReference('pattern-diamond-shirt', $Pattern14);

        $Pattern15 = new Pattern();
        $Pattern15->setDescription('Shield');
        $Pattern15->setPathSVG('');
        $Pattern15->setElementType($this->getReference('elementType-shirt'));
        $manager->persist($Pattern15);
        $manager->flush();
        $this->addReference('pattern-shield-shirt', $Pattern15);

        $Pattern16 = new Pattern();
        $Pattern16->setDescription('Lion');
        $Pattern16->setPathSVG('');
        $Pattern16->setElementType($this->getReference('elementType-shirt'));
        $manager->persist($Pattern16);
        $manager->flush();
        $this->addReference('pattern-lion-shirt', $Pattern16);

        $Pattern17 = new Pattern();
        $Pattern17->setDescription('Shamrock');
        $Pattern17->setPathSVG('');
        $Pattern17->setElementType($this->getReference('elementType-shirt'));
        $manager->persist($Pattern17);
        $manager->flush();
        $this->addReference('pattern-shamrock-shirt', $Pattern17);

        $Pattern18 = new Pattern();
        $Pattern18->setDescription('Spots');
        $Pattern18->setPathSVG('');
        $Pattern18->setElementType($this->getReference('elementType-cap'));
        $manager->persist($Pattern18);
        $manager->flush();
        $this->addReference('pattern-spots-cap', $Pattern18);

        $Pattern19 = new Pattern();
        $Pattern19->setDescription('Hooped');
        $Pattern19->setPathSVG('');
        $Pattern19->setElementType($this->getReference('elementType-cap'));
        $manager->persist($Pattern19);
        $manager->flush();
        $this->addReference('pattern-hooped-cap', $Pattern19);

        $Pattern20 = new Pattern();
        $Pattern20->setDescription('Swirl');
        $Pattern20->setPathSVG('');
        $Pattern20->setElementType($this->getReference('elementType-sleeves'));
        $manager->persist($Pattern20);
        $manager->flush();
        $this->addReference('pattern-swirl-sleeves', $Pattern20);

        $Pattern21 = new Pattern();
        $Pattern21->setDescription('S');
        $Pattern21->setPathSVG('');
        $Pattern21->setElementType($this->getReference('elementType-shirt'));
        $manager->persist($Pattern21);
        $manager->flush();
        $this->addReference('pattern-s-shirt', $Pattern21);

        $Pattern22 = new Pattern();
        $Pattern22->setDescription('Sides');
        $Pattern22->setPathSVG('');
        $Pattern22->setElementType($this->getReference('elementType-shirt'));
        $manager->persist($Pattern22);
        $manager->flush();
        $this->addReference('pattern-sides-shirt', $Pattern22);

        $Pattern23 = new Pattern();
        $Pattern23->setDescription('Seams');
        $Pattern23->setPathSVG('');
        $Pattern23->setElementType($this->getReference('elementType-shirt'));
        $manager->persist($Pattern23);
        $manager->flush();
        $this->addReference('pattern-seams-shirt', $Pattern23);

        $Pattern24 = new Pattern();
        $Pattern24->setDescription('Disc');
        $Pattern24->setPathSVG('');
        $Pattern24->setElementType($this->getReference('elementType-shirt'));
        $manager->persist($Pattern24);
        $manager->flush();
        $this->addReference('pattern-disc-shirt', $Pattern24);

        $Pattern25 = new Pattern();
        $Pattern25->setDescription('Stars');
        $Pattern25->setPathSVG('');
        $Pattern25->setElementType($this->getReference('elementType-shirt'));
        $manager->persist($Pattern25);
        $manager->flush();
        $this->addReference('pattern-stars-shirt', $Pattern25);

        $Pattern26 = new Pattern();
        $Pattern26->setDescription('Quarters');
        $Pattern26->setPathSVG('');
        $Pattern26->setElementType($this->getReference('elementType-shirt'));
        $manager->persist($Pattern26);
        $manager->flush();
        $this->addReference('pattern-quarters-shirt', $Pattern26);

        $Pattern27 = new Pattern();
        $Pattern27->setDescription('Thirds');
        $Pattern27->setPathSVG('');
        $Pattern27->setElementType($this->getReference('elementType-shirt'));
        $manager->persist($Pattern27);
        $manager->flush();
        $this->addReference('pattern-thirds-shirt', $Pattern27);

        $Pattern28 = new Pattern();
        $Pattern28->setDescription('Crossed sashes');
        $Pattern28->setPathSVG('');
        $Pattern28->setElementType($this->getReference('elementType-shirt'));
        $manager->persist($Pattern28);
        $manager->flush();
        $this->addReference('pattern-crossed-sashes-shirt', $Pattern28);

        $Pattern29 = new Pattern();
        $Pattern29->setDescription('Braces');
        $Pattern29->setPathSVG('');
        $Pattern29->setElementType($this->getReference('elementType-shirt'));
        $manager->persist($Pattern29);
        $manager->flush();
        $this->addReference('pattern-braces-shirt', $Pattern29);

        $Pattern30 = new Pattern();
        $Pattern30->setDescription('Hoops');
        $Pattern30->setPathSVG('');
        $Pattern30->setElementType($this->getReference('elementType-sleeves'));
        $manager->persist($Pattern30);
        $manager->flush();
        $this->addReference('pattern-hoops-sleeves', $Pattern30);

        $Pattern31 = new Pattern();
        $Pattern31->setDescription('Hoops');
        $Pattern31->setPathSVG('');
        $Pattern31->setElementType($this->getReference('elementType-shirt'));
        $manager->persist($Pattern31);
        $manager->flush();
        $this->addReference('pattern-hoops-shirt', $Pattern31);

        $Pattern32 = new Pattern();
        $Pattern32->setDescription('Striped');
        $Pattern32->setPathSVG('');
        $Pattern32->setElementType($this->getReference('elementType-cap'));
        $manager->persist($Pattern32);
        $manager->flush();
        $this->addReference('pattern-cap-striped', $Pattern32);


    }

    /**
     * {@inheritDoc}
     */
    public function getOrder()
    {
        return 7;
    }

}
