FOS\UserBundle\Model\User:
    properties:
        username:
        - Length:
            min: 3
            max: 255
            minMessage: "Your username must have at least {{ limit }} characters."
            maxMessage: "The username is too long."
        - NotBlank: { message: "Please enter a username"}
        plainPassword:
            - NotBlank: { message: "Please enter a password", groups:[Registration, ChangePassword] }
            - Length:
                min: 8
                max: 255
                minMessage: "Your password must have at least {{ limit }} characters."
                maxMessage: "The password is too long."