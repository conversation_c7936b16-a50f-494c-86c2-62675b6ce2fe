<?php

namespace DBLS\Bundle\JerseyColoursBundle\Validator\Constraints;

use Symfony\Component\Validator\Constraint;

/**
 * Annotation constraint to validate files extension
 *
 * @Annotation
 * <AUTHOR>
 */
class FileExtension extends Constraint {

    public $errorMessage = "File extension '{{user_extension}}' is invalid. Allowed file extension(s) is(are): '{{allowed_extensions}}'";
    public $allowExtensions = array();

    public function validatedBy() {
        return 'fileextension.validator';
    }

}
