{"name": "jquery.inputmask", "version": "3.1.63", "description": "jquery.inputmask is a jquery plugin which create an input mask.", "main": "./dist/inputmask/jquery.inputmask.js", "files": ["dist/inputmask/jquery.inputmask.js", "dist/inputmask/jquery.inputmask.extensions.js", "dist/inputmask/jquery.inputmask.date.extensions.js", "dist/inputmask/jquery.inputmask.numeric.extensions.js", "dist/inputmask/jquery.inputmask.phone.extensions.js", "dist/inputmask/jquery.inputmask.regex.extensions.js"], "scripts": {"test": "grunt qunit"}, "repository": {"type": "git", "url": "https://github.com/RobinHerbots/jquery.inputmask.git"}, "keywords": ["j<PERSON>y", "plugins", "input", "form", "inputmask", "mask"], "author": {"name": "<PERSON>", "url": "http://github.com/RobinHerbots/jquery.inputmask"}, "license": "MIT", "bugs": {"url": "https://github.com/RobinHerbots/jquery.inputmask/issues"}, "homepage": "https://github.com/RobinHerbots/jquery.inputmask", "dependencies": {"jquery": ">=1.7"}, "devDependencies": {"grunt": "^0.4.5", "grunt-bump": "^0.3.0", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-qunit": "^0.7.0", "grunt-contrib-uglify": "^0.9.1", "grunt-nuget": "^0.1.4", "grunt-release": "^0.12.0", "grunt-shell": "^1.1.2"}}