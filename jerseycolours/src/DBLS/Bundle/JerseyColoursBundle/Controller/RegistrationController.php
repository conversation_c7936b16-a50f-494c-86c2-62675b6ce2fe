<?php

namespace DBLS\Bundle\JerseyColoursBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Template;

use DBLS\Bundle\JerseyColoursBundle\Form\RegistrationFormType;
use DBLS\Bundle\JerseyColoursBundle\Entity\User;
use FOS\UserBundle\Controller\RegistrationController as BaseController;

use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

/**
 * Registration controller.
 *
 * @Route("/user")
 */
class RegistrationController extends BaseController
{

    /**
     * Lists all User entities.
     *
     * @Route("/", name="user")
     * @Method("GET")
     * @Template("JerseyColoursBundle:User:index.html.twig")
     */
    public function indexAction()
    {
        $em = $this->container->get('doctrine')->getEntityManager();

        $entities = $em->getRepository('JerseyColoursBundle:User')->findAll();

        return array(
            'entities' => $entities,
        );
    }
    
    /**
     * Displays a form to create a new User entity.
     *
     * @Route("/new", name="user_new")
     * @Method("GET")
     * @Template("JerseyColoursBundle:Registration:register.html.twig")
     */
    public function newAction()
    {        
        $entity = new User();
        $form   = $this->createUserForm($entity);

        return array(
            'entity' => $entity,
            'form'  => $form->createView(),
        );
    }
        
    /**
     * Displays a form to edit an existing User entity.
     *
     * @Route("/edit/{id}", name="user_edit")
     * @Method("GET")
     * @Template("JerseyColoursBundle:Registration:register.html.twig")
     */
    public function editAction($id)
    {
        
        $em = $this->container->get('doctrine')->getEntityManager();

        $entity = $em->getRepository('JerseyColoursBundle:User')->find($id);
        
        if (!$entity) {
            throw new NotFoundHttpException('Unable to find User entity.');
        }
        
        $editForm = $this->createUserForm($entity, $id, true);
                
        return array(
            'entity' => $entity,
            'form'  => $editForm->createView(),
        );               
        
    }
    
    /**
     * Creates a new User entity.
     *
     * @Route("/save/{id}", name="user_save", defaults={"id": null})
     * @Method("POST")
     * @Template("JerseyColoursBundle:Registration:register.html.twig")
     */
    public function saveAction(Request $request, $id)
    {
        $isUpdate = false;
	if ( !$id ) {
	    $entity = new User();
	} else {
	    
            $isUpdate = true;
	    $em = $this->container->get('doctrine')->getEntityManager();
            $entity = $em->getRepository('JerseyColoursBundle:User')->find($id);

	    if (!$entity) {
		throw $this->createNotFoundException('Unable to find User entity.');
	    }
	}
	
        $form = $this->createUserForm($entity, $id, $isUpdate);
        $form->handleRequest($request);
        
        $messageBroker = $this->container->get('message.broker');
	$messageDefaults = $this->container->getParameter('messages');

        if ( $form->isValid() ) {
	    
            $em = $this->container->get('doctrine')->getEntityManager();
            $em->persist($entity);
            $em->flush();
            
            $form = $this->createUserForm($entity, $entity->getId(), true );
	     
	    $messageBroker->addSuccess($messageDefaults['success']);
            
        } else {
	    
	    $messageBroker->addError($messageDefaults['error']);
	}
	
	return array( 'form' => $form->createView() );
        
    }
            
    /**
     * Creates a form to save a User entity.
     *
     * @param User $entity The entity
     *
     * @return \Symfony\Component\Form\Form The form
     */
    private function createUserForm(User $entity, $id = null, $isUpdate = false)
    {
        
        $action = $this->container->get('router')->generate('user_save', array( 'id' => $id ));
        
        $formType = new RegistrationFormType('FOS\UserBundle\Entity\User');
        $formType->setUpdate($isUpdate);
        
        $form = $this->container->get('form.factory')->create($formType, $entity, array(
            'action' => $action,
            'method' => 'POST',
        ));

        return $form;
    }
    
}
