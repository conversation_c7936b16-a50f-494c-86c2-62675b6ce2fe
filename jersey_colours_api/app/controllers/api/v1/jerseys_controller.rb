# frozen_string_literal: true

module Api
  module V1
    class JerseysController < BaseController
      before_action :set_jersey, only: [:show, :update, :destroy, :parse, :generate_svg]

      def index
        jerseys = Jersey.includes(:race, :trainer, :jersey_elements, :errors)
        jerseys = jerseys.where('name ILIKE ?', "%#{params[:search]}%") if params[:search].present?
        jerseys = jerseys.where(parsed: params[:parsed]) if params[:parsed].present?
        jerseys = paginate(jerseys.order(created_at: :desc))

        render_json_api(jerseys, JerseySerializer)
      end

      def show
        render_json_api(@jersey, JerseySerializer, include: [:jersey_elements, :errors])
      end

      def create
        jersey = Jersey.new(jersey_params)
        
        if jersey.save
          # Parse the jersey string if provided
          if jersey.string_value.present?
            parse_jersey_string(jersey)
          end
          
          render_json_api(jersey, JerseySerializer, status: :created)
        else
          render_error('Failed to create jersey', :unprocessable_entity, jersey.errors.full_messages)
        end
      end

      def update
        if @jersey.update(jersey_params)
          # Re-parse if string_value changed
          if @jersey.saved_change_to_string_value? && @jersey.string_value.present?
            parse_jersey_string(@jersey)
          end
          
          render_json_api(@jersey, JerseySerializer)
        else
          render_error('Failed to update jersey', :unprocessable_entity, @jersey.errors.full_messages)
        end
      end

      def destroy
        @jersey.destroy
        render_success(nil, 'Jersey deleted successfully')
      end

      def parse
        if @jersey.string_value.present?
          parse_jersey_string(@jersey)
          render_json_api(@jersey.reload, JerseySerializer, include: [:jersey_elements, :errors])
        else
          render_error('No string value to parse')
        end
      end

      def generate_svg
        # This would integrate with the SVG generation service
        # For now, return a placeholder
        render json: {
          success: true,
          data: {
            front_svg_url: "/api/v1/jerseys/#{@jersey.id}/svg/front",
            side_svg_url: "/api/v1/jerseys/#{@jersey.id}/svg/side"
          }
        }
      end

      def bulk_parse
        jersey_ids = params[:jersey_ids] || []
        results = []

        jersey_ids.each do |jersey_id|
          jersey = Jersey.find_by(id: jersey_id)
          next unless jersey&.string_value.present?

          begin
            parse_jersey_string(jersey)
            results << { id: jersey.id, status: 'success', parsed: jersey.parsed }
          rescue => e
            results << { id: jersey.id, status: 'error', error: e.message }
          end
        end

        render json: {
          success: true,
          message: "Processed #{results.length} jerseys",
          data: { results: results }
        }
      end

      private

      def set_jersey
        @jersey = Jersey.find(params[:id])
      end

      def jersey_params
        params.require(:jersey).permit(:name, :description, :string_value, :status, :number, :race_id, :trainer_id)
      end

      def parse_jersey_string(jersey)
        parser_service = JerseyParserService.new
        parser_service.set_jersey(jersey)
        
        # Clear existing elements and errors
        jersey.jersey_elements.destroy_all
        jersey.errors.destroy_all
        
        # Parse the string
        parsed_jersey = parser_service.create_jersey_from_string(jersey.string_value)
        
        # Update jersey with parsed data
        jersey.parsed = parsed_jersey.parsed
        jersey.parsing_time = Time.current
        jersey.save!
        
        parsed_jersey
      end
    end
  end
end
