<?php

namespace DBLS\Bundle\JerseyColoursBundle\Services;

use Vich\UploaderBundle\Naming\NamerInterface;
use Vich\UploaderBundle\Mapping\PropertyMapping;

use DBLS\Bundle\JerseyColoursBundle\Entity\Pattern;


/**
 * This class is intended to customize how file names are created. 
 * 
 * <AUTHOR>
 */
class PatternFileNamerService implements NamerInterface {
    
    /**
     * 
     */
    public function __construct() {
    }
    
    /**
     * Creates a name for the file being uploaded.
     *
     * @param object          $object  The object the upload is attached to.
     * @param PropertyMapping $mapping The mapping to use to manipulate the given object.
     *
     * @return string The file name.
     */
    public function name($object, PropertyMapping $mapping)
    {
        if ( $object instanceof Pattern) {
            if ( $mapping->getFilePropertyName() == 'imageFile' ) {
                $filename = 'front.svg';
            }

            if ( $mapping->getFilePropertyName() == 'sideImageFile' ) {
                $filename = 'side.svg';
            }
        }
        
        if ( $filename == '' ) {
            $file = $mapping->getFile($object);
            $filename = uniqid() . '_' . $file->getClientOriginalName();
        }
        
        return $filename;
    }
    
}
