<?php

namespace DBLS\Bundle\JerseyColoursBundle\DataFixtures\ORM;

use Doctrine\Common\DataFixtures\AbstractFixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Common\Persistence\ObjectManager;
use DBLS\Bundle\JerseyColoursBundle\Entity\Meeting;

class LoadMeetingData extends AbstractFixture implements OrderedFixtureInterface
{

    /**
     * {@inheritDoc}
     */
    public function load(ObjectManager $manager)
    {

        $meetingOne = new Meeting();
        $meetingOne->setLocation("Morphettville");
        $meetingOne->setExternalId("6e33a793-d3d6-45e8-a523-2997c86ba860");
        $meetingOne->setDate("2015-01-01 00:00:00");

        $manager->persist($meetingOne);
        $manager->flush();
        $this->addReference('meeting-one', $meetingOne);
    }

    /**
     * {@inheritDoc}
     */
    public function getOrder()
    {
        return 2;
    }
    
}
