import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { jerseyService, Jersey, JerseySearchParams } from '../services/jerseyService';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PlayIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

export const Jerseys: React.FC = () => {
  const [jerseys, setJerseys] = useState<Jersey[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [searchParams, setSearchParams] = useState<JerseySearchParams>({
    page: 1,
    per_page: 25,
    search: '',
    parsed: undefined,
  });
  const [selectedJerseys, setSelectedJerseys] = useState<number[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    loadJerseys();
  }, [searchParams]);

  const loadJerseys = async () => {
    try {
      setLoading(true);
      const response = await jerseyService.getJerseys(searchParams);
      if (response.success) {
        setJerseys(response.data || []);
      } else {
        setError(response.error || 'Failed to load jerseys');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load jerseys');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (searchTerm: string) => {
    setSearchParams(prev => ({ ...prev, search: searchTerm, page: 1 }));
  };

  const handleFilterChange = (parsed?: boolean) => {
    setSearchParams(prev => ({ ...prev, parsed, page: 1 }));
  };

  const handleParseJersey = async (jerseyId: number) => {
    try {
      const response = await jerseyService.parseJersey(jerseyId);
      if (response.success) {
        loadJerseys(); // Reload to show updated status
      } else {
        setError(response.error || 'Failed to parse jersey');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to parse jersey');
    }
  };

  const handleBulkParse = async () => {
    if (selectedJerseys.length === 0) return;

    try {
      const response = await jerseyService.bulkParseJerseys(selectedJerseys);
      if (response.success) {
        setSelectedJerseys([]);
        loadJerseys();
      } else {
        setError(response.error || 'Failed to parse jerseys');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to parse jerseys');
    }
  };

  const handleSelectJersey = (jerseyId: number) => {
    setSelectedJerseys(prev =>
      prev.includes(jerseyId)
        ? prev.filter(id => id !== jerseyId)
        : [...prev, jerseyId]
    );
  };

  const handleSelectAll = () => {
    if (selectedJerseys.length === jerseys.length) {
      setSelectedJerseys([]);
    } else {
      setSelectedJerseys(jerseys.map(j => j.id));
    }
  };

  const getStatusIcon = (jersey: Jersey) => {
    if (jersey.parsing) return <ClockIcon className="h-4 w-4 text-yellow-500" />;
    if (jersey.parsed && jersey.error_count === 0) return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
    if (jersey.error_count && jersey.error_count > 0) return <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />;
    return <ClockIcon className="h-4 w-4 text-gray-400" />;
  };

  return (
    <div>
      {/* Header */}
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-xl font-semibold text-gray-900">Jerseys</h1>
          <p className="mt-2 text-sm text-gray-700">
            Manage jockey racing silks with natural language parsing and visual generation.
          </p>
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Jersey
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="mt-6 bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="sm:flex sm:items-center sm:justify-between">
            <div className="flex-1 min-w-0">
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  className="focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md"
                  placeholder="Search jerseys..."
                  value={searchParams.search}
                  onChange={(e) => handleSearch(e.target.value)}
                />
              </div>
            </div>

            <div className="mt-4 sm:mt-0 sm:ml-4 flex space-x-2">
              <select
                className="rounded-md border-gray-300 text-sm focus:ring-primary-500 focus:border-primary-500"
                value={searchParams.parsed === undefined ? 'all' : searchParams.parsed.toString()}
                onChange={(e) => {
                  const value = e.target.value;
                  handleFilterChange(value === 'all' ? undefined : value === 'true');
                }}
              >
                <option value="all">All Status</option>
                <option value="true">Parsed Successfully</option>
                <option value="false">Parse Errors</option>
              </select>

              {selectedJerseys.length > 0 && (
                <button
                  onClick={handleBulkParse}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  <PlayIcon className="h-4 w-4 mr-1" />
                  Parse Selected ({selectedJerseys.length})
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mt-4 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Jersey Table */}
      <div className="mt-6 bg-white shadow overflow-hidden sm:rounded-md">
        <div className="px-4 py-5 sm:p-6">
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
              <p className="mt-2 text-sm text-gray-500">Loading jerseys...</p>
            </div>
          ) : jerseys.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">No jerseys found.</p>
              <button
                onClick={() => setShowCreateModal(true)}
                className="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-600 bg-primary-100 hover:bg-primary-200"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Create your first jersey
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <input
                        type="checkbox"
                        checked={selectedJerseys.length === jerseys.length}
                        onChange={handleSelectAll}
                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Jersey
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Description
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Elements
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {jerseys.map((jersey) => (
                    <tr key={jersey.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedJerseys.includes(jersey.id)}
                          onChange={() => handleSelectJersey(jersey.id)}
                          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center">
                              <span className="text-white font-medium text-sm">
                                {jersey.name.charAt(0).toUpperCase()}
                              </span>
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {jersey.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              ID: {jersey.id} • #{jersey.number || 'N/A'}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900 max-w-xs truncate">
                          {jerseyService.formatJerseyString(jersey)}
                        </div>
                        <div className="text-sm text-gray-500">
                          {jersey.string_value}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getStatusIcon(jersey)}
                          <span className={`ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${jerseyService.getStatusBadgeColor(jersey)}`}>
                            {jerseyService.getStatusText(jersey)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {jerseyService.getElementSummary(jersey)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <Link
                            to={`/jerseys/${jersey.id}`}
                            className="text-primary-600 hover:text-primary-900"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </Link>
                          <button
                            onClick={() => handleParseJersey(jersey.id)}
                            className="text-green-600 hover:text-green-900"
                            disabled={jersey.parsing}
                          >
                            <PlayIcon className="h-4 w-4" />
                          </button>
                          <button className="text-gray-600 hover:text-gray-900">
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button className="text-red-600 hover:text-red-900">
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Pagination would go here */}

      {/* Create Modal would go here */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Create New Jersey</h3>
              <p className="text-sm text-gray-500 mb-4">
                Jersey creation form will be implemented here.
              </p>
              <div className="flex justify-end space-x-2">
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                >
                  Cancel
                </button>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700"
                >
                  Create
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
