<?php

namespace DBLS\Bundle\JerseyColoursBundle\DataFixtures\ORM;

use Doctrine\Common\DataFixtures\AbstractFixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Common\Persistence\ObjectManager;
use DBLS\Bundle\JerseyColoursBundle\Entity\Jersey;

class LoadJerseyData extends AbstractFixture implements OrderedFixtureInterface
{
    /**
     * {@inheritDoc}
     */
    public function load(ObjectManager $manager)
    {

        $JerseyThubiaan = new Jersey();
        $JerseyThubiaan->setName("Thubiaan");
        $JerseyThubiaan->setDescription("");
        $JerseyThubiaan->setStringValue("Thubiaan: white, red c with bakers hat, red collar, royal blue sleeves, red cap");
        $JerseyThubiaan->setStatus(false);
        $JerseyThubiaan->setRace($this->getReference('race-one'));
        $JerseyThubiaan->setExternalJerseyId("teste");
        $manager->persist($JerseyThubiaan);
        $manager->flush();
        $this->addReference('jersey-thubiaan', $JerseyThubiaan);

        $JerseyWaltzing = new Jersey();
        $Jersey<PERSON>altzing->setName("Waltzing To Win");
        $JerseyWaltzing->setDescription("");
        $JerseyWaltzing->setStringValue("Waltzing To Win: royal blue, lime hooped sleeves, quartered cap");
        $JerseyWaltzing->setStatus(false);
        $JerseyWaltzing->setRace($this->getReference('race-one'));
        $JerseyWaltzing->setExternalJerseyId("teste");
        $manager->persist($JerseyWaltzing);
        $manager->flush();
        $this->addReference('jersey-waltzing', $JerseyWaltzing);

        $JerseyGossip = new Jersey();
        $JerseyGossip->setName("Gossip Guru");
        $JerseyGossip->setDescription("");
        $JerseyGossip->setStringValue("Gossip Guru: navy blue, white stars, navy blue, red and white striped sleeves, red cap");
        $JerseyGossip->setStatus(true);
        $JerseyGossip->setRace($this->getReference('race-one'));
        $JerseyGossip->setExternalJerseyId("teste");
        $manager->persist($JerseyGossip);
        $manager->flush();
        $this->addReference('jersey-gossip', $JerseyGossip);

        $JerseyToo = new Jersey();
        $JerseyToo->setName("Too Cool To Fool");
        $JerseyToo->setDescription("");
        $JerseyToo->setStringValue("Too Cool To Fool: navy blue, white stars, navy blue, red and white striped sleeves, white cap");
        $JerseyToo->setStatus(false);
        $JerseyToo->setRace($this->getReference('race-two'));
        $JerseyToo->setExternalJerseyId("teste");
        $manager->persist($JerseyToo);
        $manager->flush();
        $this->addReference('jersey-toocool', $JerseyToo);

        $JerseyVatican = new Jersey();
        $JerseyVatican->setName("Vatican");
        $JerseyVatican->setDescription("");
        $JerseyVatican->setStringValue("Vatican: black, orange spots and cap");
        $JerseyVatican->setStatus(false);
        $JerseyVatican->setRace($this->getReference('race-two'));
        $JerseyVatican->setExternalJerseyId("teste");
        $manager->persist($JerseyVatican);
        $manager->flush();
        $this->addReference('jersey-vatican', $JerseyVatican);

    }

    /**
     * {@inheritDoc}
     */
    public function getOrder()
    {
        return 4;
    }
}
