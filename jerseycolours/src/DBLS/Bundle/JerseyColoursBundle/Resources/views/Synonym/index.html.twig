{% extends 'site.html.twig' %}

{% block page_header %}
    <div id="content-header">
	<h1>Synonym list</h1>
	<div class="btn-group">
	    <a class="btn btn-large new-synonym" href="javascript:;" title="New Synonym"><i class="fa fa-plus-circle"></i> New Synonym</a>
	</div>
    </div>
{% endblock page_header %}

{% block breadcrumb %}
    <div id="breadcrumb">
	<a href="{{ path('homepage') }}" class="tip-bottom" data-original-title="Go to Home"><i class="fa fa-home"></i> Home</a>
	<a href="javascript:;" class="current">Synonym list</a>
    </div>
{% endblock breadcrumb %}

{% block content %}
    <div class="row">
	<div class="col-xs-12">
	    <div class="widget-box">
		<div class="widget-title">
		    <span class="icon">
			<i class="fa fa-th"></i>
		    </span>
		    <h5>Synonym list</h5>
		</div>
		<div class="widget-content nopadding">
		    <table class="table table-bordered table-striped table-hover data-table auto-init">
			<thead>
			    <tr>
				<th style="width: 5%">#</th>
				<th style="width: 25%">Term</th>
				<th style="width: 25%">Original Term</th>
				<th style="width: 20%">Type</th>
				<th style="width: 20%" class="no-sort">Actions</th>
			    </tr>
			</thead>
			<tbody>
			    {% for entity in synonyms %}
				<tr>
				    <td>{{ entity.id }}</td>
				    <td>{{ entity.term|capitalize }}</td>
				    <td>
				    	{{ entity.originalTerm|capitalize }}
				    </td>
				    <td>{{ entity.getTypeName }}</td>
					<td>
						<a href="javascript:;" data-id="{{ entity.id }}" class="btn btn-primary btn-xs edit-synonym"><i class="fa fa-pencil"></i> Edit</a>
						<a href="javascript:;" data-id="{{ entity.id }}" class="btn btn-danger btn-xs delete-item"><i class="fa fa-trash-o"></i> Delete</a>
					</td>
				</tr>
			    {% endfor %}
			</tbody>
		    </table>
		</div>
	    </div>
	</div>
    </div>
{% endblock content %}


{% block extrascripts %}
    {% javascripts 
	'@JerseyColoursBundle/Resources/public/js/bundle/synonym.js' %}
		<script type="text/javascript" src="{{ asset_url }}"></script>
    {% endjavascripts %}
{% endblock extrascripts %}