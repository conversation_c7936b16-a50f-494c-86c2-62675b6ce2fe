Jersey Colours
=============

A Symfony project created on July 1, 2015, 2:17 pm.

# Getting Started from scratch

The following process assumes that the devopment envirmonment alread has the following items installed:

    PHP >= 5.4.9
    MySQL >= 5.5

# The Steps

## 1. Create Database

Use either MySQL (recommended) or PostreSQL/PostGIS and create a new database named `jerseycolours`

## 2. Create jerseycolours project directory

    mkdir ~/projects/
    mkdir ~/projects/jerseycolours

## 3. Clone jerseycolours repository

    git clone https://bitbucket.org/dblsolutions/jockeycolours.git


## 4. Install jerseycolours requirements

    cd ~/projects/jerseycolours/jerseycolours/
    composer update


## 5. Configure Database settings

Use your favorite text editor to edit `DATABASE` in `parameters.yml`. 
Add your database settings and save.

    nano ~/projects/jerseycolours/jerseycolours/app/config/parameters.yml


## 6. Initialize the database

    cd ~/projects/jerseycolours/jerseycolours
    php app/console doctrine:schema:create
    mysqlimport -u [uname] -p[pass] jockeycolour ~/projects/jerseycolours/jerseycolours/documentation/dump_jockeycolours.sql
    
## 7. Dump assets

    cd ~/projects/jerseycolours/jerseycolours
    php app/console assets:install web

## 8. Run server and Open up a browser and See if it worked!

    php app/console server:run 
    firefox --new-tab http://127.0.0.1:8000

