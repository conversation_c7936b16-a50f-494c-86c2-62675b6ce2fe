{% extends 'site.html.twig' %}

{% block page_header %}
<div id="content-header">
    <h1>Silk listing</h1>
</div>
{% endblock page_header %}

{% block breadcrumb %}
<div id="breadcrumb">
    <a href="{{ path('homepage') }}" class="tip-bottom" data-original-title="Go to Home"><i class="fa fa-home"></i> Home</a>
    <a href="{{ path('jersey') }}" class="current">Silk list</a>
</div>
{% endblock breadcrumb %}

{% block content %}
    <div class="row">
        <div class="col-xs-12">
        	<div class="widget-box">
        	    <div class="widget-title">
        		<span class="icon">
        		    <i class="fa fa-cogs"></i>
        		</span>
        		<h5>Silk list</h5>
        	    </div>
        	    <div class="widget-content jersey-container">
                    {% set hide_message = 'show' %}
                                        
                    {% if race is defined and race|length > 0 %}            		                                
                        {{ include('JerseyColoursBundle:Jersey:race.html.twig', { 'race' : race }) }}
                        {% set hide_message = 'hide' %}
                    {% endif %}
                        
                    <div id="message-no-race" class="alert alert-warning {{ hide_message }}">
                         There is no <b>pending</b> Silk.
                    </div>
        	    </div>
        	</div>
        </div>
    </div>

{% endblock content %}


    {% block extrascripts %}
    {% javascripts 
    '@JerseyColoursBundle/Resources/public/js/bundle/synonym.js'
	'@JerseyColoursBundle/Resources/public/js/bundle/jersey.js' %}
        <script type="text/javascript" src="{{ asset_url }}"></script>
    {% endjavascripts %}

    {% autoescape false %}
        <script type="text/javascript">
            JockerColour.Jersey.colours = {{ colours }};
            JockerColour.Jersey.jerseyPath = '{{ asset("images/jerseys/") }}';

            {% if jersey %}
                $(document).ready(
                    function()
                    {
                        JockerColour.Jersey.editJersey({{ jersey.id }})
                    }
                );
            {% endif %}
        </script>
    {% endautoescape %}

{% endblock extrascripts %}