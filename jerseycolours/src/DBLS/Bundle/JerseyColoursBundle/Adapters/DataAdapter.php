<?php

namespace DBLS\Bundle\JerseyColoursBundle\Adapters;

/**
 * Description of DataAdapter
 *
 * <AUTHOR>
 */
abstract class DataAdapter{
    
    protected $dataArray = array();

    /*
     * Return data processed.
     */
    public function getDataProcessed($data) {
        
        $itemMeetings = array();
        
        foreach($this->_getValue($data, 'data', array()) as $meeting){
            
            $itemMeetings = array(
                'externalId'    => $this->_getValue($meeting, 'id', null),
                'location'      => $this->_getValue($meeting, 'venue', null),
                'date'          => $this->_getValue($meeting, 'date', null),
                'code'          => $this->_getValue($meeting, 'code', null),
                'races'         => array(),
            );            
            
            $itemRaces = array();
            
            foreach($this->_getValue($meeting, 'races', array()) as $race){
                               
                $itemRaces = array(
                    'name'              => $this->_getValue($race, 'name', null),
                    'externalRaceId'    => $this->_getValue($race, 'id', null),
                    'externalMeetId'    => $this->_getValue($race, 'meetid', null),
                    'number'            => $this->_getValue($race, 'number', null),
                    'racetime'          => $this->_getValue($race, 'racetime', null),
                    'jerseys'           => array(),
                );
                
                $itemJersey = array();
                
                foreach($this->_getValue($race, 'runners', array()) as $jersey){
                    
                    $itemJersey[] = array(
                        'stringValue'       => $this->_getValue($jersey, 'livery', null),
                        'name'              => $this->_getValue($jersey, 'name', null),
                        'externalJerseyId'  => $this->_getValue($jersey, 'id', null),
                        'number'            => $this->_getValue($jersey, 'number', null),
                        'trainer'           => $this->_getValue($jersey, 'trainer', null),
                    );
                    
                }
                
                $itemRaces['jerseys'] = $itemJersey;
                $itemMeetings['races'][] = $itemRaces;                
            }            
            
            $this->dataArray['meetings'][] = $itemMeetings;            
                       
        }  
        
        return array(
            'data'      => json_decode( json_encode($this->dataArray) ),
            'importer'  => $data
        );
        
    }
    
    /*
     * Get Value
     */
    private function _getValue($data,$key,$return)
    {
        
        if( !empty($data->$key) )
            return $data->$key;
        else
            return $return;
    }

}
