# Jersey Colours Setup Instructions

## Prerequisites
- Ruby 3.2.0 (via rbenv)
- Node.js 18+ (for React frontend)
- SQLite3 (for development database)

## Backend Setup (Ruby on Rails API)

### 1. Ensure you're using the correct Ruby version
```bash
cd jersey_colours_api

# Initialize rbenv in your shell
eval "$(rbenv init -)"

# Verify Ruby version
ruby --version
# Should show: ruby 3.2.0

# If not, set the local Ruby version
rbenv local 3.2.0
```

### 2. Install dependencies and setup database
```bash
# Install bundler if needed
gem install bundler

# Install gems
bundle install

# Create and setup database
rails db:create
rails db:migrate
rails db:seed

# Start the Rails server
rails server -p 3001
```

The API will be available at: http://localhost:3001

### 3. Test the API
```bash
# Health check
curl http://localhost:3001/up

# Test authentication endpoint
curl -X POST http://localhost:3001/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'
```

## Frontend Setup (React)

### 1. Install Node.js dependencies
```bash
cd ../jersey_colours_frontend

# Install dependencies
npm install
```

### 2. Handle Node.js version compatibility
If you get Vite errors due to Node.js version, you have two options:

**Option A: Update Node.js (Recommended)**
```bash
# Using nvm (if installed)
nvm install 18
nvm use 18

# Or using Homebrew
brew install node@18
```

**Option B: Downgrade Vite**
```bash
npm install vite@4.5.0 --save-dev
```

### 3. Start the development server
```bash
npm run dev
```

The frontend will be available at: http://localhost:5173

## Demo Credentials
- **Email**: <EMAIL>
- **Password**: password123

## Troubleshooting

### Ruby/Rails Issues
```bash
# If you get bundler version errors
gem install bundler:2.4.1

# If Rails is not found
gem install rails

# Reset rbenv if needed
rbenv rehash
```

### Database Issues
```bash
# Reset database if needed
rails db:drop db:create db:migrate db:seed
```

### Frontend Issues
```bash
# Clear npm cache
npm cache clean --force

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

## Project Structure

### Backend (jersey_colours_api/)
- **Models**: User, Jersey, Colour, Pattern, ElementType, etc.
- **Controllers**: API endpoints under `/api/v1/`
- **Services**: JerseyParserService, SvgGeneratorService
- **Database**: SQLite3 with seeded data

### Frontend (jersey_colours_frontend/)
- **Pages**: Dashboard, Jerseys, Colours, Patterns
- **Components**: Layout, Authentication, Forms
- **Services**: API integration with axios
- **Styling**: Tailwind CSS with custom components

## Key Features

1. **Jersey String Parsing**: Natural language processing for jersey descriptions
2. **SVG Generation**: Visual jersey representation
3. **Color Management**: CMYK and Hex color system
4. **Pattern System**: Stripes, spots, diamonds, hoops
5. **User Authentication**: JWT-based auth system
6. **Responsive UI**: Mobile-friendly interface

## API Endpoints

- `POST /api/v1/auth/login` - User authentication
- `GET /api/v1/jerseys` - List jerseys
- `POST /api/v1/jerseys` - Create jersey
- `POST /api/v1/jerseys/:id/parse` - Parse jersey string
- `GET /api/v1/colours` - List colours
- `GET /api/v1/patterns` - List patterns

## Next Steps

1. Start both servers (Rails on :3001, React on :5173)
2. Login with demo credentials
3. Explore the dashboard and jersey management
4. Test jersey string parsing functionality
5. Customize colors and patterns as needed

The application maintains full feature parity with the original PHP version while providing a modern, scalable architecture!
