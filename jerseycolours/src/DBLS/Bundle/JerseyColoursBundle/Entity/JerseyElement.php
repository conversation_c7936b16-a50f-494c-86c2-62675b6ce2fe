<?php

namespace DBLS\Bundle\JerseyColoursBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Doctrine\Common\Collections\ArrayCollection;

/**
 * JerseyElement
 *
 * @ORM\Table()
 * @ORM\Entity(repositoryClass="DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElementRepository")
 */
class JerseyElement
{

    /**
     * @var integer
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="Jersey", inversedBy="elements")
     * @ORM\JoinColumn(name="jersey_id", referencedColumnName="id", onDelete="CASCADE")
     */
    private $jersey;

    /**
     * @ORM\ManyToOne(targetEntity="ElementType", inversedBy="elements_jersey")
     * @ORM\JoinColumn(name="element_type_id", referencedColumnName="id")
     */
    private $element_type;

    /**
     * @ORM\ManyToOne(targetEntity="Colour",cascade={"persist"})
     * @ORM\JoinColumn(name="colour_id", referencedColumnName="id")
     */
    protected $colour;
    
    /**
     * @var integer
     *
     * @ORM\Column(name="`order`", type="integer")
     */
    private $order;

    /**
     * @ORM\OneToMany(targetEntity="JerseyElementPattern", mappedBy="jersey_element")
    */
    protected $jersey_patterns;

    public function __construct()
    {
        $this->jersey_patterns = new ArrayCollection();
    }
    
    /**
     * Get id
     *
     * @return integer 
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set jersey
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $jersey
     * @return JerseyElement
     */
    public function setJersey(\DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $jersey = null)
    {
        $this->jersey = $jersey;

        return $this;
    }

    /**
     * Get jersey
     *
     * @return \DBLS\Bundle\JerseyColoursBundle\Entity\Jersey 
     */
    public function getJersey()
    {
        return $this->jersey;
    }

    /**
     * Set element_type
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\ElementType $elementType
     * @return JerseyElement
     */
    public function setElementType(\DBLS\Bundle\JerseyColoursBundle\Entity\ElementType $elementType = null)
    {
        $this->element_type = $elementType;

        return $this;
    }

    /**
     * Get element_type
     *
     * @return \DBLS\Bundle\JerseyColoursBundle\Entity\ElementType 
     */
    public function getElementType()
    {
        return $this->element_type;
    }

    /**
     * Set colour
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Colour $colour
     * @return JerseyElement
     */
    public function setColour(\DBLS\Bundle\JerseyColoursBundle\Entity\Colour $colour = null)
    {
        $this->colour = $colour;

        return $this;
    }

    /**
     * Get colour
     *
     * @return \DBLS\Bundle\JerseyColoursBundle\Entity\Colour 
     */
    public function getColour()
    {
        return $this->colour;
    }

    /**
     * Set order
     *
     * @param integer $order
     * @return JerseyElement
     */
    public function setOrder($order)
    {
        $this->order = $order;

        return $this;
    }

    /**
     * Get order
     *
     * @return integer 
     */
    public function getOrder()
    {
        return $this->order;
    }

    /**
     * Add jersey_patterns
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElementPattern $jerseyPatterns
     * @return JerseyElement
     */
    public function addJerseyPattern(\DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElementPattern $jerseyPatterns)
    {
        $this->jersey_patterns[] = $jerseyPatterns;

        return $this;
    }

    /**
     * Remove jersey_patterns
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElementPattern $jerseyPatterns
     */
    public function removeJerseyPattern(\DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElementPattern $jerseyPatterns)
    {
        $this->jersey_patterns->removeElement($jerseyPatterns);
    }

    /**
     * Get jersey_patterns
     *
     * @return \Doctrine\Common\Collections\Collection 
     */
    public function getJerseyPatterns()
    {
        return $this->jersey_patterns;
    }
}
