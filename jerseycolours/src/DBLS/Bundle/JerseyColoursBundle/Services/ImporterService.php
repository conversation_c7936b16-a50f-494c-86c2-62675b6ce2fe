<?php

namespace DBLS\Bundle\JerseyColoursBundle\Services;

use DBLS\Bundle\JerseyColoursBundle\Adapters\DataSourceInterface;
use DBLS\Bundle\JerseyColoursBundle\Entity\Import;
use DBLS\Bundle\JerseyColoursBundle\Entity\Race;
use DBLS\Bundle\JerseyColoursBundle\Entity\Trainer;
use DBLS\Bundle\JerseyColoursBundle\Entity\Jersey;
use DBLS\Bundle\JerseyColoursBundle\Services\JerseyParserService;
use DBLS\Bundle\JerseyColoursBundle\Services\JerseyGeneratorService;
use DBLS\Bundle\JerseyColoursBundle\Services\MeetingGeneratorService;
use Doctrine\ORM\EntityManager;
use Symfony\Component\Filesystem\Filesystem;

use \Symfony\Component\DependencyInjection\ContainerAware;

/**
 * Description of ImportService
 *
 * <AUTHOR>
 */
class ImporterService extends ContainerAware
{
    
    private $adapter;
    private $em;
    private $serviceMeetingGenerator;
    private $serviceJerseyParser;
    private $serviceJerseyGenerator;
    
    
    /*
     * 
     */
    public function __construct(EntityManager $em) {
        
        $this->em  = $em;        
        
    }
    
    /*
     * 
     */
    public function setAdapter(DataSourceInterface $adapter )
    {
        $this->adapter = $adapter;
        return $this;
    }
    
    /*
     * 
     */
    public function setJerseyParserService(JerseyParserService $jersyParserService)
    {
        $this->serviceJerseyParser = $jersyParserService;
    }
    
    /*
     * 
     */
    public function setJerseyGeneratorService(JerseyGeneratorService $jerseyGeneratorService)
    {
        $this->serviceJerseyGenerator = $jerseyGeneratorService;
    }
    
    /*
     * 
     */
    public function setMeetingGeneratorService(MeetingGeneratorService $meetingGeneratorService)
    {
        $this->serviceMeetingGenerator = $meetingGeneratorService;
    }

    /*
     * 
     */
    public function processCreate()
    {
        // suspend auto-commit
        $this->em->getConnection()->beginTransaction();

        try {

                $data = $this->adapter->getData();
                
                // Create file data json
                $file =  $this->_createFile($data['importer']);
                
                if ( !$file )
                    return false;
                
                // Insert Import
                $import = $this->_saveImporter($file);
                
                //count Meetigns
                $numMeet = 0;
                
                //count Races
                $numRace = 0;

                //count Trainers
                $numTrainers = 0;
                
                //count Jerseys
                $numJersey = 0;
                
                // Loop Meetings
                foreach ($data['data']->meetings as $meeting) {
                    // Create Meeting
                    $entityMeeting = $this->serviceMeetingGenerator->createMeeting($import,$meeting);
                    
                    // count num row new Meeting
                    if( $entityMeeting['new'] === true)
                        $numMeet++;
                    
                    // loop Races
                    foreach ($meeting->races as $race) {
                        // Create Race
                        $entityRace = $this->_createRace($entityMeeting['entity'], $race);
                        
                        // count num row new Meeting
                        if ( $entityRace['new'] === true )
                            $numRace++;
                                    
                        if ( !empty( $entityRace['entity'] ) ) {
                        
                            // loop Jerseys
                            foreach ($race->jerseys as $jersey) {

                                $entityTrainer = $this->_createTrainer($jersey);

                                // Create Jersey
                                $data = array(
                                    'jersey'    => $jersey,
                                    'meeting'   => $entityMeeting['entity']->getId(),
                                    'race'      => $entityRace['entity'],
                                    'trainer'   => $entityTrainer['entity']
                                );
                                
                                // count num row new Jersey
                                if ( $this->_createJersey( $data ) === true )
                                    $numJersey++;   
                            }   
                        }
                    }
                }
                
                /*
                echo "========= METINGES =========" . "<br/>";
                echo "Total: " . $numMeet . "<br/>";
                echo "============================" . "<br/><br/>";
                echo "=========== RACES ==========" . "<br/>";
                echo "Total: " . $numRace . "<br/>";
                echo "============================" . "<br/><br/>";
                echo "=========== SILKS ==========" . "<br/>";
                echo "Total: " . $numJersey . "<br/>";
                echo "============================" . "<br/>";
                * 
                */
               
            $this->em->getConnection()->commit();
               
            return array(
                'meetings'  => $numMeet,
                'races'     => $numRace,
                'trainers'  => $numTrainers,
                'jerseys'   => $numJersey
            );

        } catch( \Exception $e ) {

            $this->em->getConnection()->rollback();

            throw $e;
        }
        
    }
    
    /*
     * 
     */
    private function _createJersey( $data )
    {
        // Verified Jersey created in BD.
        $bdJersey = $this->_validateJersey( $data );
        
        /*
        if( !empty($bdJersey)  ) {// && $bdJersey->getStatus() === false ){
            
            //Remove Jersey
            $this->em->remove($bdJersey);
            $this->em->flush();    
        }
        */
        
        //Create new Jersey
        return $this->_saveJersey($data, $bdJersey);
    }

    /*
     * 
     */
    private function _saveJersey( array $data, $jersey = null )
    {
        try {

            if ( empty($jersey) ) {
                $jersey = new Jersey();
            }

            $jersey->setStringValue($data['jersey']->stringValue)
                    ->setDescription($data['jersey']->stringValue)
                    ->setName($data['jersey']->name)
                    ->setParsed(false)
                    ->setParsing(false)
                    ->setStatus(false)
                    ->setRace($data['race'])
                    ->setTrainer($data['trainer'])
                    ->setNumber($data['jersey']->number)
                    ->setExternalJerseyId($data['jersey']->externalJerseyId);

            $this->em->persist($jersey);
            $this->em->flush();
            
            return true;
        
        } catch (\Exception $ex) {
            return false;
        }
    }
    
    /*
     * 
     */
    private function _validateJersey( array $data )
    {
        // Verified Jersey created.
        $entityJersey = $this->em->getRepository('JerseyColoursBundle:Jersey')->findJerseyByTrainer( 
                array(
                    'name'      => $data['jersey']->name,
                    'trainer'   => $data['trainer']->getId(),
                    'race'      => $data['race']->getId()
                    /*
                    // Before the validation was against description and race
                    'race' => $data['race']->getId(), 
                    'meeting' => $data['meeting']
                    */
                )
        );
        return $entityJersey;
    }

    /*
     * 
     */
    private function _saveImporter( $data )
    {
        
        $import = new Import();
        $import->setContent($data);
        $this->em->persist($import);
        $this->em->flush();
                
        return $import;
    }
    
    /*
     * 
     */
    private function _getDirJson()
    {
        $fs = new Filesystem();

        $rootDir = $this->container->get('kernel')->getRootDir();
        $webPath = realpath($rootDir . '/../web/');
        $jsonDir =  $webPath . '/json';

        if ( !$fs->exists( $jsonDir ) )
            $fs->mkdir( $jsonDir );

        return $jsonDir;
    }
    
    /*
     * 
     */
    private function _createFile( $data )
    {        
        
        try
        {
        
            $jsonDir = $this->_getDirJson();

            $fs = new Filesystem();

            $dateTime = sprintf('%s-%s',date('Y_m_d'),date('his'));
            $file = sprintf('%s.%s',$dateTime,"json");
            
            $fileFolder = sprintf('%s/%s',$jsonDir,$file);
            
            if ( !$fs->exists( $jsonDir ) )
                $fs->mkdir( $jsonDir );

            file_put_contents($fileFolder, json_encode($data));

            return $file;
        
        }  catch ( Exception $e ){
            return false;
        }
    }

    /*
     * 
     */
    private function _createRace( $meeting, $data )
    {
        // Verified Race created.
        $race = $this->em->getRepository('JerseyColoursBundle:Race')->findOneBy( 
                            array( 
                                'externalRaceId'    => $data->externalRaceId, 
                                'meeting'           => $meeting, 
                            ) 
                );
                        
        if ( $race ) {
            
            $return = array(
                        'entity'  => $race,
                        'new'     => false,
                      );
            
            return $return;
        }
        
        $race = new Race();
        $race->setApproved(0);
        $race->setExternalMeetId($data->externalMeetId);
        $race->setExternalRaceId($data->externalRaceId);
        $race->setName($data->name);
        $race->setNumber($data->number);
        $race->setRacetime($data->racetime);
        $race->setMeeting($meeting);
        $this->em->persist($race);
        $this->em->flush();
                
        $return = array(
                    'entity'  => $race,
                    'new'     => true,
                  );
            
        return $return;        
        
    }

    /*
     * 
     */
    private function _createTrainer( $data )
    {
        $trainerName = trim($data->trainer);

        // Verified Trainer created.
        $trainer = $this->em->getRepository('JerseyColoursBundle:Trainer')->findOneBy( array( 'name' => $trainerName ) );
                        
        if( $trainer ){
            
            $return = array(
                        'entity'  => $trainer,
                        'new'     => false,
                      );
            
            return $return;
            
        }
        
        $trainer = new Trainer();
        $trainer->setName($trainerName);
        $this->em->persist($trainer);
        $this->em->flush();
                
        $return = array(
                    'entity'  => $trainer,
                    'new'     => true,
                  );
            
        return $return;        
        
    }
    
}
