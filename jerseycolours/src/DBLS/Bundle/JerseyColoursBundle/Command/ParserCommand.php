<?php

namespace DBLS\Bundle\JerseyColoursBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class ParserCommand extends ContainerAwareCommand
{
    protected $container;

    protected $em;

    protected function configure()
    {
        $this->setName('jersey:parse')
             ->setDescription('Parse the pending Silks from the database');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->container = $this->getContainer();
        $jerseyParser = $this->container->get('services.jersey_parser');
        $jerseyGenerator = $this->container->get('services.jersey_generator');

        $this->em = $this->container->get('doctrine.orm.entity_manager');
        $jerseyRepository = $this->em->getRepository('JerseyColoursBundle:Jersey');
        
        $jerseys = $jerseyRepository->findPendingToParse();

        $parsingTime = new \DateTime('now');

        foreach ( $jerseys as $jersey ) {
            $jersey->setParsing(true)->setParsingTime($parsingTime);
            $this->em->persist($jersey);
        }

        $this->em->flush();

        foreach ( $jerseys as $jersey ) {

            $stringDescription = $jersey->getStringValue();

            // Try to find a matching silk with the same trainer and original description
            $similarJersey = $jerseyRepository->findSimilarJersey($jersey);

            if ( !empty($similarJersey) ) {
                $newDescription = $similarJersey->getDescription();

                if ( !empty($newDescription) ) {

                    $stringDescription = $newDescription;
                    $jersey->setCopyFrom($similarJersey)->setDescription($stringDescription);
                }
            }
            
            $jerseyParser->setJersey($jersey);
            $jersey = $jerseyParser->createJerseyFromString($stringDescription);
            $jersey->setParsed(true)->setParsing(false);
            $jerseyGenerator->createJersey($jersey, false, false);
        }

        if ( count($jerseys) > 0 ) {
            
            $now = new \DateTime('now');
            $timeFormated = $now->format('Y-m-d H:i:s');
            $message = sprintf('%s - %d silks parsed', $timeFormated, count($jerseys));
            $output->writeln($message);

        } else {
            $output->writeln('No silks parsed');
        }
    }
}