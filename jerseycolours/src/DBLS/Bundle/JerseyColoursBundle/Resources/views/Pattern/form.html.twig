{% extends 'site.html.twig' %}

{% form_theme form _self %}

{% block file_widget %}
    {% spaceless %}
        <input type="{{ type }}" {{ block('widget_attributes') }} onchange="javascript:this.nextSibling.style.display = 'none';"/>
        
        <div style="margin-top: 10px; padding: 5px;">
            {% if image_url is not null %}                
                <div style="width: 100px" class="thumbnail">
                    <img style="width: 100px; height: 130px" alt="Pattern" src="{{ asset('images/patterns/' ~ form.parent.vars.data.slug ~ '/' ~ image_url) }}">
                </div>                
            {% endif %}        
        </div>
    {% endspaceless %}
{% endblock file_widget %}

{% block page_header %}
    <div id="content-header">
	<h1>Pattern form</h1>
	<div class="btn-group">
	    <a class="btn btn-large" href="{{ path('pattern') }}" title="Pattern List"><i class="fa fa-th"></i> Pattern List</a>
	</div>
    </div>
{% endblock page_header %}

{% block breadcrumb %}
    <div id="breadcrumb">
	<a href="{{ path('homepage') }}" class="tip-bottom" data-original-title="Go to Home"><i class="fa fa-home"></i> Home</a>
	<a href="{{ path('pattern') }}">Pattern list</a>
	<a href="{{ path('pattern_new') }}" class="current">Pattern form</a>
    </div>
{% endblock breadcrumb %}

{% block content %}
    <div class="row">
	<div class="col-xs-12">
	    <div class="widget-box">
		<div class="widget-title">
		    <span class="icon">
			<i class="fa fa-cogs"></i>
		    </span>
		    <h5>Pattern form</h5>
		</div>
		<div class="widget-content nopadding">
		    {{ form(form) }}
		</div>
	    </div>
	</div>
    </div>
{% endblock %}

{% block extrascripts %}
    {% javascripts 
	'@JerseyColoursBundle/Resources/public/js/bundle/pattern.js' %}
	<script src="{{ asset_url }}"></script>
    {% endjavascripts %}
{% endblock extrascripts %}