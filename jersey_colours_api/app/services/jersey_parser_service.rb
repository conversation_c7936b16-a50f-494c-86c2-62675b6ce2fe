# frozen_string_literal: true

class JerseyParserService
  attr_accessor :jersey

  def initialize
    @jersey = nil
  end

  def set_jersey(jersey)
    @jersey = jersey
  end

  def get_jersey
    @jersey
  end

  def create_jersey_from_string(string, levenshtein_ratio: 75)
    # Initialize lexer and parser
    lexer = JerseyParser::Lexer.new
    lexer.levenshtein_ratio = levenshtein_ratio

    # Set up known terms
    setup_known_terms(lexer)

    # Create parser
    parser = JerseyParser::Parser.new(lexer)

    # Set jersey if provided
    parser.jersey = @jersey if @jersey

    # Parse the string
    jersey = parse_string(string, lexer, parser)

    jersey
  end

  private

  def setup_known_terms(lexer)
    # Set known colours
    colours = Colour.all.map { |color| color.name.strip.downcase }
    lexer.set_known_colours(colours)

    # Set known patterns
    patterns = Pattern.where(status: true).map { |pattern| pattern.description.strip.downcase }
    lexer.set_known_patterns(patterns)

    # Set known elements
    elements = ElementType.all.map { |element| element.name.strip.downcase }
    lexer.set_known_elements(elements)

    # Set synonyms
    synonyms = Synonym.all.map do |synonym|
      {
        term: synonym.term.strip.downcase,
        original_term: synonym.original_term.strip.downcase
      }
    end
    lexer.set_synonyms(synonyms)
  end

  def parse_string(string, lexer, parser)
    # Clean up the string
    cleaned_string = cleanup_string(string)

    # Tokenize
    lexer.tokenize(cleaned_string)
    tokens = lexer.tokens

    # Parse base color (sequence up to first comma)
    first_chunk = get_chunk_until_comma(tokens)
    is_color = parser.parse_base(first_chunk)

    unless is_color
      # If first chunk wasn't a color, prepend it back to tokens
      count = first_chunk.length
      if count > 0
        tokens.unshift(JerseyParser::Token.comma_token)
        first_chunk.reverse.each { |token| tokens.unshift(token) }
      end
    end

    # Parse remaining tokens
    parser.parse(tokens)

    # Create jersey entity
    create_jersey_entity(parser, string)
  end

  def cleanup_string(string)
    string = string.strip

    # Handle colons
    colon_pos = string.index(':')
    if colon_pos
      string = string[0...colon_pos] + ' ' + string[colon_pos..-1]
    end

    # Replace multiple spaces with single space
    string.gsub(/\s{2,}/, ' ')
  end

  def get_chunk_until_comma(tokens)
    chunk = []
    tokens.each do |token|
      break if token.comma?
      chunk << token
    end
    chunk
  end

  def create_jersey_entity(parser, original_string)
    jersey = @jersey || Jersey.new

    # Set basic properties
    jersey.string_value = original_string
    jersey.name = parser.get_jersey_name.presence || "Jersey #{Time.current.to_i}"
    jersey.parsed = parser.get_errors.empty?
    jersey.parsing_time = Time.current

    # Add elements
    if parser.get_elements.present?
      parser.get_elements.each_with_index do |element_data, index|
        jersey_element = JerseyElement.new
        jersey_element.order = index + 1

        # Find element type
        element_type = ElementType.find_by(name: element_data[JerseyParser::Parser::S_ELEMENT_NAME])
        jersey_element.element_type = element_type

        # Set color if present
        if element_data[JerseyParser::Parser::S_COLOUR].present?
          colour = Colour.find_by(name: element_data[JerseyParser::Parser::S_COLOUR])
          jersey_element.colour = colour
        end

        jersey.jersey_elements << jersey_element

        # Add patterns if present
        if element_data[JerseyParser::Parser::S_PATTERNS].present?
          element_data[JerseyParser::Parser::S_PATTERNS].each do |pattern_data|
            pattern = Pattern.joins(:element_type)
                           .find_by(description: pattern_data['pattern'], 
                                   element_types: { name: element_data[JerseyParser::Parser::S_ELEMENT_NAME] })
            
            if pattern
              jersey_element_pattern = JerseyElementPattern.new
              jersey_element_pattern.pattern = pattern
              jersey_element.jersey_element_patterns << jersey_element_pattern

              # Add pattern colors
              if pattern_data['colours'].present?
                pattern_data['colours'].each_with_index do |color_name, color_index|
                  colour = Colour.find_by(name: color_name)
                  if colour
                    jersey_pattern_colour = JerseyPatternColour.new
                    jersey_pattern_colour.order = color_index + 1
                    jersey_pattern_colour.colour = colour
                    jersey_element_pattern.jersey_pattern_colours << jersey_pattern_colour
                  end
                end
              end
            end
          end
        end
      end
    end

    # Add errors
    if parser.get_errors.present?
      parser.get_errors.each do |error_data|
        error = Error.new
        error.type = map_parser_error_to_jersey_error(error_data['type'])
        error.message = error_data['message']
        error.expression = error_data['expression']
        jersey.errors << error
      end
    end

    jersey
  end

  def map_parser_error_to_jersey_error(parser_error)
    case parser_error
    when JerseyParser::Parser::E_INVALID_STRING_START
      Error::INVALID_STRING_START
    when JerseyParser::Parser::E_TOKEN_UNKNOWN
      Error::WORD_UNKNOWN
    when JerseyParser::Parser::E_NO_SEMANTICAL_MEANING
      Error::NO_SEMANTICAL_MEANING
    when JerseyParser::Parser::E_PATTERN_NOT_RELATED_TO_ELEMENT
      Error::PATTERN_NOT_RELATED_TO_ELEMENT
    else
      parser_error
    end
  end
end
