# frozen_string_literal: true

class PatternSerializer
  include JSONAPI::Serializer

  attributes :description, :path_svg, :side_path_svg, :status, :created_at, :updated_at

  belongs_to :element_type, serializer: ElementTypeSerializer

  attribute :usage_count do |pattern|
    pattern.jersey_element_patterns.count
  end

  attribute :front_view_path do |pattern|
    pattern.front_view_path
  end

  attribute :side_view_path do |pattern|
    pattern.side_view_path
  end

  attribute :slug do |pattern|
    pattern.description.parameterize
  end
end
