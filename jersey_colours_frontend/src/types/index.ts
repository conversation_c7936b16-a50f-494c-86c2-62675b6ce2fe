// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
  details?: string[];
}

// User Types
export interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  role: 'admin' | 'user';
}

export interface AuthResponse {
  user: User;
  token: string;
}

// Color Types
export interface Colour {
  id: number;
  name: string;
  cmyk: string;
  hexadecimal: string;
  created_at: string;
  updated_at: string;
  usage_count?: number;
  rgb?: {
    r: number;
    g: number;
    b: number;
  };
}

// Element Type
export interface ElementType {
  id: number;
  name: string;
  created_at: string;
  updated_at: string;
  pattern_count?: number;
  usage_count?: number;
}

// Pattern Types
export interface Pattern {
  id: number;
  description: string;
  path_svg: string;
  side_path_svg: string;
  status: boolean;
  created_at: string;
  updated_at: string;
  element_type: ElementType;
  usage_count?: number;
  front_view_path?: string;
  side_view_path?: string;
  slug?: string;
}

// Jersey Types
export interface JerseyPatternColour {
  id: number;
  order: number;
  colour: Colour;
  created_at: string;
  updated_at: string;
}

export interface JerseyElementPattern {
  id: number;
  pattern: Pattern;
  jersey_pattern_colours: JerseyPatternColour[];
  created_at: string;
  updated_at: string;
}

export interface JerseyElement {
  id: number;
  order: number;
  element_type: ElementType;
  colour?: Colour;
  jersey_element_patterns: JerseyElementPattern[];
  created_at: string;
  updated_at: string;
  string_representation?: string;
}

export interface Error {
  id: number;
  type: string;
  message: string;
  expression?: string;
  details?: string;
  created_at: string;
  updated_at: string;
}

export interface Meeting {
  id: number;
  external_id: string;
  date: string;
  location: string;
  created_at: string;
  updated_at: string;
  race_count?: number;
  jersey_count?: number;
}

export interface Race {
  id: number;
  name: string;
  external_race_id: string;
  external_meet_id: string;
  number: number;
  racetime: string;
  meeting: Meeting;
  created_at: string;
  updated_at: string;
  jersey_count?: number;
}

export interface Trainer {
  id: number;
  name: string;
  created_at: string;
  updated_at: string;
  jersey_count?: number;
}

export interface Jersey {
  id: number;
  name: string;
  description?: string;
  string_value: string;
  status: boolean;
  parsed: boolean;
  parsing: boolean;
  parsing_time?: string;
  external_jersey_id: string;
  number?: number;
  race?: Race;
  trainer?: Trainer;
  jersey_elements: JerseyElement[];
  errors: Error[];
  created_at: string;
  updated_at: string;
  generated_string?: string;
  folder_path?: string;
  element_count?: number;
  error_count?: number;
  has_base_color?: boolean;
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
}

export interface JerseyForm {
  name: string;
  description?: string;
  string_value: string;
  status: boolean;
  number?: number;
  race_id?: number;
  trainer_id?: number;
}

export interface ColourForm {
  name: string;
  cmyk: string;
  hexadecimal: string;
}

export interface PatternForm {
  description: string;
  path_svg: string;
  side_path_svg: string;
  status: boolean;
  element_type_id: number;
}

// Query Parameters
export interface PaginationParams {
  page?: number;
  per_page?: number;
}

export interface SearchParams extends PaginationParams {
  search?: string;
}

export interface JerseySearchParams extends SearchParams {
  parsed?: boolean;
}

export interface PatternSearchParams extends SearchParams {
  status?: boolean;
  element_type_id?: number;
}

// API Endpoints
export interface ApiEndpoints {
  auth: {
    login: string;
    register: string;
    me: string;
  };
  jerseys: string;
  colours: string;
  patterns: string;
  elementTypes: string;
  races: string;
  meetings: string;
  trainers: string;
  synonyms: string;
  errors: string;
}

// Component Props
export interface JerseyCardProps {
  jersey: Jersey;
  onEdit?: (jersey: Jersey) => void;
  onDelete?: (jersey: Jersey) => void;
  onParse?: (jersey: Jersey) => void;
}

export interface ColourPickerProps {
  value?: string;
  onChange: (color: string) => void;
  colours: Colour[];
}

export interface JerseyVisualizerProps {
  jersey: Jersey;
  view?: 'front' | 'side';
}

export interface LoadingState {
  isLoading: boolean;
  error?: string;
}

export interface TableColumn<T> {
  key: keyof T;
  label: string;
  render?: (value: any, item: T) => React.ReactNode;
  sortable?: boolean;
}

export interface TableProps<T> {
  data: T[];
  columns: TableColumn<T>[];
  loading?: boolean;
  onRowClick?: (item: T) => void;
}

// Navigation
export interface NavItem {
  name: string;
  href: string;
  icon?: React.ComponentType<any>;
  current?: boolean;
}
