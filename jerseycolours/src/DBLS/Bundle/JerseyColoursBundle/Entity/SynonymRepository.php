<?php

namespace DBLS\Bundle\JerseyColoursBundle\Entity;

use Doctrine\ORM\EntityRepository;

/**
 * SynonymRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class SynonymRepository extends EntityRepository
{
    /**
     * Find synonym by term
     * 
     * @param string $term
     * @return Synonym
     */
    public function findByTerm($term) 
    {
        $qb = $this->_em->createQueryBuilder();
        $qb
            ->select('s')
            ->from('JerseyColoursBundle:Synonym', 's')
            ->where($qb->expr()->eq(
                $qb->expr()->lower($qb->expr()->trim('s.term')), ":term"
            ))
            ->setParameter('term', trim(strtolower($term)));

        $q = $qb->getQuery();
        
        try {
            return $q->getOneOrNullResult();
        } catch (NoResultException $ex) {
            return null;
        }
    }
}
