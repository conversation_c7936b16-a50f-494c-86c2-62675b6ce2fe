<?php

namespace DBLS\Bundle\JerseyColoursBundle\Services;

use Doctrine\ORM\EntityManager;
use DBLS\Bundle\JerseyColoursBundle\Entity\Meeting;
use DBLS\Bundle\JerseyColoursBundle\Entity\Import;
use \Symfony\Component\DependencyInjection\ContainerAware;

/**
 * The purpose of this class is to generate Meetings.
 * 
 * <AUTHOR>
 */
class MeetingGeneratorService extends ContainerAware
{

    /**
     * Holds an instance of database connection
     * @var EntityManager
     */
    private $em = null;

    public function __construct(EntityManager $em) 
    {
        $this->em = $em;
    }

    /**
     * This method creates a new Meeting.
     */
    public function createMeeting(Import $import,$data) 
    {
        
        // Verified Meeting created.
        $meeting = $this->em->getRepository('JerseyColoursBundle:Meeting')->findOneBy( 
                            array( 'externalId' => $data->externalId )
                    );
                        
        if( $meeting ){
            $return = array(
                'entity'  => $meeting,
                'new'     => false,
              );
            return $return;
        }
        
        // Create Meeting
        $entityMeeting = new Meeting();
        $entityMeeting->setLocation($data->location);
        $entityMeeting->setExternalId($data->externalId);
        $entityMeeting->setDate($data->date);
        $entityMeeting->setCode($data->code);
        $entityMeeting->setImport($import);

        $this->em->persist($entityMeeting);
        $this->em->flush();
        
        $return = array(
          'entity'  => $entityMeeting,
          'new'     => true,
        );
        
        return $return;

    }            
             
}
