import{createContext as r,useContext as o,use<PERSON>emo as u}from"react";import{useOnUnmount as i}from'../../hooks/use-on-unmount.js';import{MenuMachine as c}from'./menu-machine.js';const a=r(null);function p(t){let n=o(a);if(n===null){let e=new Error(`<${t} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,s),e}return n}function s({id:t,__demoMode:n=!1}){let e=u(()=>c.new({id:t,__demoMode:n}),[]);return i(()=>e.dispose()),e}export{a as MenuContext,s as useMenuMachine,p as useMenuMachineContext};
