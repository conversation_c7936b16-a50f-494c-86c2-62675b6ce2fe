<?php

namespace DBLS\Bundle\JerseyColoursBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use DBLS\Bundle\JerseyColoursBundle\Adapters\FileAdapter;

class GenerateJpegCommand extends ContainerAwareCommand
{
    protected function configure()
    {
        $this->setName('jersey:jpeg')
             ->setDescription('Generate jpeg');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $container = $this->getContainer();
        
        $jpegGenerator = $container->get('services.jpeg.generator');

        $em = $container->get('doctrine.orm.entity_manager');
        $raceRepository = $em->getRepository('JerseyColoursBundle:Race');

        $raceEntity = $raceRepository->find(24);
        $silks = $raceEntity->getApprovedJerseys();

        $jpegGenerator->outputFile($raceEntity, $silks);
    }
}