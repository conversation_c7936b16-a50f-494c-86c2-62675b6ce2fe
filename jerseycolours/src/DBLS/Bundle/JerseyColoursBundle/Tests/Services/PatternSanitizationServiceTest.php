<?php

namespace DBLS\Bundle\JerseyColoursBundle\Tests\Services;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

use DBLS\Bundle\JerseyColoursBundle\Entity\Pattern;

class PatternSanitizationTest extends WebTestCase 
{
    /**
     * @Pattern
     */
    protected $pattern;

    /**
     * {@inheritDoc}
     */
    public function setUp()
    {
        self::bootKernel();

        $this->em = static::$kernel->getContainer()
                                    ->get('doctrine')
                                    ->getManager();

        $this->pattern = new Pattern();
    }

    /**
     * {@inheritDoc}
     */
    protected function tearDown()
    {
        parent::tearDown();
        $this->em->close();
    } 

    public function testPatternWithElement()
    {
        $this->pattern->setDescription('Cap Checked');

        $this->em->persist($this->pattern);
        
        $this->assertEquals('Checked', $this->pattern->getDescription());
    }

    public function testPatternWithElementUpperCase()
    {
        $this->pattern->setDescription('CAP Checked');

        $this->em->persist($this->pattern);
        
        $this->assertEquals('Checked', $this->pattern->getDescription());
    }

    public function testPatternWithElementLowerCase()
    {
        $this->pattern->setDescription('cap Checked');

        $this->em->persist($this->pattern);
        
        $this->assertEquals('Checked', $this->pattern->getDescription());
    }

    public function testPatternWithElementInMiddle()
    {
        $this->pattern->setDescription('Striped cap Checked');

        $this->em->persist($this->pattern);
        
        $this->assertEquals('Striped Checked', $this->pattern->getDescription());
    }

    public function testPatternWithElementStart()
    {
        $this->pattern->setDescription(' Sleeves Checked');

        $this->em->persist($this->pattern);
        
        $this->assertEquals('Checked', $this->pattern->getDescription());
    }

    public function testPatternWithElementEnd()
    {
        $this->pattern->setDescription(' Checked Shirt ');

        $this->em->persist($this->pattern);
        
        $this->assertEquals('Checked', $this->pattern->getDescription());
    }
}