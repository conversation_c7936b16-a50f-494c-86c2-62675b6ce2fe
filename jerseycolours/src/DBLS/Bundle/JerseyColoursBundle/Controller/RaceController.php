<?php

namespace DBLS\Bundle\JerseyColoursBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;

/**
 * Race controller.
 *
 * @Route("/race")
 */
class RaceController extends Controller
{

    /**
     * Approve the race
     *
     * @Route("/approve/{id}", name="race_approval", defaults={"id": null}, options={"expose"=true}))
     * @Method("POST")
     */
    public function approveAction(Request $request, $id)
    {

        if ( !$id ) {
            throw $this->createNotFoundException('Unable to find Race Entity');
        } else {
            
            $em = $this->getDoctrine()->getManager();
            $entity = $em->getRepository('JerseyColoursBundle:Race')->find($id);

            if (!$entity) {
              throw $this->createNotFoundException('Unable to find Race entity.');
            }
        }

        $raceApproval = $this->get('services.race.approval');
        $status = $raceApproval->approveRace($entity);

        $return = array( 'status' => true );
        if ( !$status ) {
            $return = array(
                'status' => false,
                'msg'    => 'The race could not be approved. There are silks with errors.'
            );
        }
    
        return new JsonResponse($return);
    }
}
