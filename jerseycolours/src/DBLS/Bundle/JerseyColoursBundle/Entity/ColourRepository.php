<?php

namespace DBLS\Bundle\JerseyColoursBundle\Entity;

use Doctrine\ORM\EntityRepository;

/**
 * ColourRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class ColourRepository extends EntityRepository
{
    /**
     * Find colour by name insensitive
     * 
     * @param string $name
     * @return ElementType
     */
    public function findByName($name) 
    {
        $qb = $this->_em->createQueryBuilder();
        $qb
            ->select('t')
            ->from('JerseyColoursBundle:Colour', 't')
            ->where($qb->expr()->eq(
                $qb->expr()->lower($qb->expr()->trim('t.name')), ":name"
            ))
            ->setParameter('name', trim(strtolower($name)));

        $q = $qb->getQuery();
        return $q->getSingleResult();
    }

    /**
     * 
     * @param  string $name
     * @return array
     */
    public function listOptionsCombo($name)
    {
        $qb = $this->createQueryBuilder('c');

        $colours = $qb->where($qb->expr()->like(
                        $qb->expr()->lower($qb->expr()->trim('c.name')), ":name"
                    ))
                    ->setParameter('name', '%' . strtolower(trim($name)) . '%')
                    ->orderBy('c.name','ASC')
                    ->getQuery()
                    ->getResult();

        $dataColours = array();
        foreach ( $colours as $colour ) {
            $dataColours[] = array(
                'id' => $colour->getId(),
                'color' => $colour->getHexadecimal(),
                'text' => $colour->getName()
            );
        }

        return $dataColours;
    }
    
}
