<?php

namespace DBLS\Bundle\JerseyColoursBundle\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;

use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Template;


/**
 * Pattern controller.
 *
 * @Route("/error")
 */
class ErrorController extends Controller
{

    /**
     * Lists all Pattern entities.
     *
     * @Route("/", name="error", options={"expose"=true})
     * @Method("GET")
     * @Template("JerseyColoursBundle:Error:index.html.twig")
     */
    public function indexAction()
    {
        $em = $this->getDoctrine()->getManager();

        $erros = $em->getRepository('JerseyColoursBundle:Error')->findCommonErros();

        return array(
            'errors' => $erros,
        );
    }
}
