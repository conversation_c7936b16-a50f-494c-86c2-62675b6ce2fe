{"mappings": ";;AAAA;;;;;;;;;;CAUC;AAWM,SAAS,0CAAc,OAAgC,EAAE,GAAyB;IACvF,CAAA,GAAA,yCAAc,EAAE;QACd,IAAI,WAAW,QAAQ,GAAG,IAAI,KAAK;YACjC,QAAQ,GAAG,CAAC,OAAO,GAAG,IAAI,OAAO;YACjC,OAAO;gBACL,IAAI,QAAQ,GAAG,EACb,QAAQ,GAAG,CAAC,OAAO,GAAG;YAE1B;QACF;IACF;AACF", "sources": ["packages/@react-aria/utils/src/useSyncRef.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {MutableRefObject} from 'react';\nimport {RefObject} from '@react-types/shared';\nimport {useLayoutEffect} from './';\n\ninterface ContextValue<T> {\n  ref?: MutableRefObject<T | null>\n}\n\n// Syncs ref from context with ref passed to hook\nexport function useSyncRef<T>(context?: ContextValue<T> | null, ref?: RefObject<T | null>): void {\n  useLayoutEffect(() => {\n    if (context && context.ref && ref) {\n      context.ref.current = ref.current;\n      return () => {\n        if (context.ref) {\n          context.ref.current = null;\n        }\n      };\n    }\n  });\n}\n"], "names": [], "version": 3, "file": "useSyncRef.module.js.map"}