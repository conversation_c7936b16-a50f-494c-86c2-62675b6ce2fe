<div class="well">
    <h3><i class="fa fa-calendar"></i> {{ date|date('d M, Y') }}</h3>

    <div class="row">

        <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">

            {% for meeting in meetings %}
                {% if meeting is defined and meeting|length > 0 %}            		                                
                    <div class="panel panel-default">
                        <div class="panel-heading" role="tab" id="heading{{meeting.id}}">
                            <h4 class="panel-title">
                                <a class="collapsed" data-toggle="collapse" data-parent="#accordion" href="#collapse{{meeting.id}}" aria-expanded="false" aria-controls="collapse{{meeting.id}}">
                                    {{meeting.name}}
                                </a>
                            </h4>

                        </div>
                        <div id="collapse{{meeting.id}}" class="panel-collapse collapse" role="tabpanel" aria-labelledby="heading{{meeting.id}}">
                            <div class="panel-body">
                                <ul class="site-stats" style="margin-top: 8px;">
                                {% for race in meeting.races %}
                                    {% if race.unapprovedJerseys is defined and race.unapprovedJerseys|length > 0 %}      
                                        <li>
                                            <div>
                                                <a href="{{ path('jersey_race', {'id': race.id}) }}">{{race.name}} <small> Silk(s) total {{race.unapprovedJerseys|length}}</small></a>
                                            </div>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                {% endif %}
            {% endfor %}

        </div>

    </div>
</div>