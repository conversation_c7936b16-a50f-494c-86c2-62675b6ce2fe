<?php

namespace DBLS\Bundle\JerseyColoursBundle\Tests\Services;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

use DBLS\Bundle\JerseyColoursBundle\Entity\Pattern;

class RemoveEmptyFolderServiceTest extends WebTestCase
{
    /**
     * @Pattern
     */
    protected $containear;

    protected $dir = 'empty_folder';

    /**
     * {@inheritDoc}
     */
    public function setUp()
    {
        self::bootKernel();

        $this->container = static::$kernel->getContainer();

        if ( !is_dir($this->dir) ) {
            mkdir($this->dir);
        }

        $another = $this->dir . '/another';
        $oneMore = $another . '/more';

        if ( !is_dir($another) ) {
            mkdir($another);

        }

        if ( !is_dir($oneMore) ) {
            mkdir($oneMore);
        }

    }

    /**
     * {@inheritDoc}
     */
    protected function tearDown()
    {
        parent::tearDown();
        if ( is_dir($this->dir) ) {
            rmdir($this->dir);
        }
    }

    public function testRemoveEmptyFolder()
    {
        $remover = $this->container->get('services.utils.remove_folders');

        // here, directory must exist
        $this->assertTrue(is_dir($this->dir));

        // then, we remove it
        $remover->clean($this->dir);

        // finaly, it disapear
        $this->assertTrue(!is_dir($this->dir));
    }
}
