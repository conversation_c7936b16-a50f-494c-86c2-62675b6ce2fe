imports:
    - { resource: parameters.yml }
    - { resource: security.yml }
    - { resource: services.yml }

# Put parameters here that don't need to change on each machine where the app is deployed
# http://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
parameters:
    locale: en
    sluggable.slugger.class: "DBLS\Bundle\JerseyColoursBundle\Strings\Slugger"

framework:
    #esi:             ~
    translator:      { fallbacks: ["%locale%"] }
    secret:          "%secret%"
    router:
        resource: "%kernel.root_dir%/config/routing.yml"
        strict_requirements: ~
    form:            ~
    csrf_protection: ~
    validation:      { enable_annotations: true }
    serializer:      { enable_annotations: true }
    templating:
        engines: ['twig']
        #assets_version: SomeVersionScheme
    default_locale:  "%locale%"
    trusted_hosts:   ~
    trusted_proxies: ~
    session:
        # handler_id set to null will use default session handler from php.ini
        handler_id:  ~
    fragments:       ~
    http_method_override: true

# Twig Configuration
twig:
    form:
        resources: 
            - "bootstrap_3_layout.html.twig"
            - ":Form:toolbar.html.twig"
            - "VichUploaderBundle:Form:fields.html.twig"
    debug:            "%kernel.debug%"
    strict_variables: "%kernel.debug%"
    exception_controller: 'FOS\RestBundle\Controller\ExceptionController::showAction'


# Assetic Configuration
assetic:
    debug:          "%kernel.debug%"
    use_controller: false
    bundles:        ['JerseyColoursBundle', 'FOSUserBundle']
    #java: /usr/bin/java
    filters:
        cssrewrite: ~
        #closure:
        #    jar: "%kernel.root_dir%/Resources/java/compiler.jar"
        #yui_css:
        #    jar: "%kernel.root_dir%/Resources/java/yuicompressor-2.4.7.jar"

# Doctrine Configuration
doctrine:
    dbal:
        driver:   pdo_mysql
        host:     "%database_host%"
        port:     "%database_port%"
        dbname:   "%database_name%"
        user:     "%database_user%"
        password: "%database_password%"
        charset:  UTF8
        # if using pdo_sqlite as your database driver:
        #   1. add the path in parameters.yml
        #     e.g. database_path: "%kernel.root_dir%/data/data.db3"
        #   2. Uncomment database_path in parameters.yml.dist
        #   3. Uncomment next line:
        #     path:     "%database_path%"

    orm:
        auto_generate_proxy_classes: "%kernel.debug%"
        entity_managers:
            default:
                naming_strategy: doctrine.orm.naming_strategy.underscore
                auto_mapping: true
                dql:
                    numeric_functions:
                        LEVENSHTEIN: WOK\Doctrine\Query\LevenshteinFunction
                        LEVENSHTEIN_RATIO: WOK\Doctrine\Query\LevenshteinRatioFunction
                    string_functions:
                        MATCH: WOK\Doctrine\Query\MatchAgainstFunction
                        SOUNDEX: WOK\Doctrine\Query\SoundexFunction

# Swiftmailer Configuration
swiftmailer:
    disable_delivery: true
    transport: "%mailer_transport%"
    host:      "%mailer_host%"
    username:  "%mailer_user%"
    password:  "%mailer_password%"
    spool:     { type: memory }

# KNP Menu
knp_menu:
    # use "twig: false" to disable the Twig extension and the TwigRenderer
    twig:
        template: knp_menu.html.twig
    #  if true, enables the helper for PHP templates
    templating: false
    # the renderer to use, list is also available by default
    default_renderer: twig

# FOSUser
fos_user:
    db_driver: orm # other valid values are 'mongodb', 'couchdb' and 'propel'
    firewall_name: main
    user_class: DBLS\Bundle\JerseyColoursBundle\Entity\User

# Upload component
vich_uploader:
    db_driver: orm
    mappings:
        svg_pattern:
            uri_prefix:         /images/patterns
            upload_destination: %kernel.root_dir%/../web/images/patterns
            inject_on_load:     true
            delete_on_update:   true
            delete_on_remove:   true
            namer: services.pattern.file_namer
            directory_namer: services.pattern.directory_namer
            
knp_paginator:
    page_range: 5                      # default page range used in pagination control
    default_options:
        page_name: page                # page query parameter name
        sort_field_name: sort          # sort field query parameter name
        sort_direction_name: direction # sort direction query parameter name
        distinct: true                 # ensure distinct results, useful when ORM queries are using GROUP BY statements
    template:
        pagination: KnpPaginatorBundle:Pagination:twitter_bootstrap_v3_pagination.html.twig     # sliding pagination controls template
        sortable: KnpPaginatorBundle:Pagination:sortable_link.html.twig # sort link template

fos_rest:
    body_listener: true
    view:
        view_response_listener: force
    format_listener:
        rules:
            - { path: '^/api', fallback_format: json }
            - { path: '^/', stop: true }