<!DOCTYPE html>
<html lang="en">
	<head>
		<title>Unicorn Admin</title>
		<meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<link rel="stylesheet" href="css/bootstrap.min.css" />
		<link rel="stylesheet" href="css/font-awesome.css" />
		<link rel="stylesheet" href="css/jquery.gritter.css" />
		<link rel="stylesheet" href="css/jquery-ui.css" />
		<link rel="stylesheet" href="css/unicorn.css" />
		<!--[if lt IE 9]>
		<script type="text/javascript" src="js/respond.min.js"></script>
		<![endif]-->
			
	</head>	<body data-color="grey" class="flat"><div id="wrapper">
		<div id="header">
			<h1><a href="./index.html">Unicorn Admin</a></h1>		
			<a id="menu-trigger" href="#"><i class="fa fa-align-justify"></i></a>	
		</div>
		
		<div id="user-nav">
            <ul class="btn-group">
                <li class="btn" ><a title="" href="#"><i class="fa fa-user"></i> <span class="text">Profile</span></a></li>
                <li class="btn dropdown" id="menu-messages"><a href="#" data-toggle="dropdown" data-target="#menu-messages" class="dropdown-toggle"><i class="fa fa-envelope"></i> <span class="text">Messages</span> <span class="label label-danger">5</span> <b class="caret"></b></a>
                    <ul class="dropdown-menu messages-menu">
                        <li class="title"><i class="fa fa-envelope-alt"></i>Messages<a class="title-btn" href="#" title="Write new message"><i class="fa fa-share"></i></a></li>
                        <li class="message-item">
                        	<a href="#">
	                            <img alt="User Icon" src="img/demo/av1.jpg" />
	                            <div class="message-content">
	                            	<span class="message-time">
		                                3 mins ago
		                            </span>
	                                <span class="message-sender">
	                                    Nunc Cenenatis
	                                </span>
	                                <span class="message">
	                                    Hi, can you meet me at the office tomorrow morning?
	                                </span>
	                            </div>
                        	</a>
                        </li>
                        <li class="message-item">
							<a href="#">
	                            <img alt="User Icon" src="img/demo/av1.jpg" />
	                            <div class="message-content">
	                            	<span class="message-time">
		                                3 mins ago
		                            </span>
	                                <span class="message-sender">
	                                    Nunc Cenenatis
	                                </span>
	                                <span class="message">
	                                    Hi, can you meet me at the office tomorrow morning?
	                                </span>
	                            </div>
                        	</a>
                        </li>
                        <li class="message-item">
							<a href="#">
	                            <img alt="User Icon" src="img/demo/av1.jpg" />
	                            <div class="message-content">
	                            	<span class="message-time">
		                                3 mins ago
		                            </span>
	                                <span class="message-sender">
	                                    Nunc Cenenatis
	                                </span>
	                                <span class="message">
	                                    Hi, can you meet me at the office tomorrow morning?
	                                </span>
	                            </div>
                        	</a>
                        </li>
                    </ul>
                </li>
                <li class="btn"><a title="" href="#"><i class="fa fa-cog"></i> <span class="text">Settings</span></a></li>
                <li class="btn"><a title="" href="login.html"><i class="fa fa-share"></i> <span class="text">Logout</span></a></li>
            </ul>
        </div>
       
       <div id="switcher">
            <div id="switcher-inner">
                <h3>Theme Options</h3>
                <h4>Colors</h4>
                <p id="color-style">
                    <a data-color="orange" title="Orange" class="button-square orange-switcher" href="#"></a>
                    <a data-color="turquoise" title="Turquoise" class="button-square turquoise-switcher" href="#"></a>
                    <a data-color="blue" title="Blue" class="button-square blue-switcher" href="#"></a>
                    <a data-color="green" title="Green" class="button-square green-switcher" href="#"></a>
                    <a data-color="red" title="Red" class="button-square red-switcher" href="#"></a>
                    <a data-color="purple" title="Purple" class="button-square purple-switcher" href="#"></a>
                    <a href="#" data-color="grey" title="Grey" class="button-square grey-switcher"></a>
                </p>
                <!--
                <h4>Background Patterns</h4>
                <h5>for boxed version</h5>
                <p id="pattern-switch">
                    <a data-pattern="pattern1" style="background-image:url('assets/img/patterns/pattern1.png');" class="button-square" href="#"></a>
                    <a data-pattern="pattern2" style="background-image:url('assets/img/patterns/pattern2.png');" class="button-square" href="#"></a>
                    <a data-pattern="pattern3" style="background-image:url('assets/img/patterns/pattern3.png');" class="button-square" href="#"></a>
                    <a data-pattern="pattern4" style="background-image:url('assets/img/patterns/pattern4.png');" class="button-square" href="#"></a>
                    <a data-pattern="pattern5" style="background-image:url('assets/img/patterns/pattern5.png');" class="button-square" href="#"></a>
                    <a data-pattern="pattern6" style="background-image:url('assets/img/patterns/pattern6.png');" class="button-square" href="#"></a>
                    <a data-pattern="pattern7" style="background-image:url('assets/img/patterns/pattern7.png');" class="button-square" href="#"></a>
                    <a data-pattern="pattern8" style="background-image:url('assets/img/patterns/pattern8.png');" class="button-square" href="#"></a>
                </p>-->
                <h4 class="visible-lg">Layout Type</h4>
                <p id="layout-type">
                	<a data-option="flat" class="button" href="#">Flat</a>
                    <a data-option="old" class="button" href="#">Old</a>                    
                </p>
            </div>
            <div id="switcher-button">
                <i class="fa fa-cogs"></i>
            </div>
        </div>
            
		<div id="sidebar">
			<div id="search">
				<input type="text" placeholder="Search here..."/><button type="submit" class="tip-right" title="Search"><i class="fa fa-search"></i></button>
			</div>
			<ul>
				<li><a href="index.html"><i class="fa fa-home"></i> <span>Dashboard</span></a></li>
				<li class="submenu active open">
					<a href="#"><i class="fa fa-flask"></i> <span>UI Lab</span> <i class="arrow fa fa-chevron-right"></i></a>
					<ul>
						<li class="active"><a href="interface.html">Interface Elements</a></li>
						<li><a href="jquery-ui.html">jQuery UI</a></li>
						<li><a href="buttons.html">Buttons &amp; icons</a></li>
					</ul>
				</li>
				<li class="submenu">
					<a href="#"><i class="fa fa-th-list"></i> <span>Form elements</span> <i class="arrow fa fa-chevron-right"></i></a>
					<ul>
						<li><a href="form-common.html">Common elements</a></li>
						<li><a href="form-validation.html">Validation</a></li>
						<li><a href="form-wizard.html">Wizard</a></li>
					</ul>
				</li>
				<li><a href="tables.html"><i class="fa fa-th"></i> <span>Tables</span></a></li>
				<li><a href="grid.html"><i class="fa fa-th-list"></i> <span>Grid Layout</span></a></li>
				<li class="submenu">
					<a href="#"><i class="fa fa-file"></i> <span>Sample pages</span> <i class="arrow fa fa-chevron-right"></i></a>
					<ul>
						<li><a href="invoice.html">Invoice</a></li>
						<li><a href="chat.html">Support chat</a></li>
						<li><a href="calendar.html">Calendar</a></li>
						<li><a href="gallery.html">Gallery</a></li>
						<li><a href="messages.html">Messages</a></li>
					</ul>
				</li>
				<li>
					<a href="charts.html"><i class="fa fa-signal"></i> <span>Charts &amp; graphs</span></a>
				</li>
				<li>
					<a href="widgets.html"><i class="fa fa-inbox"></i> <span>Widgets</span></a>
				</li>
			</ul>
		
		</div>
		
		
		
		<div id="content">
			<div id="content-header">
				<h1>Interface elements</h1>
				<div class="btn-group">
					<a class="btn btn-large" title="Manage Files"><i class="fa fa-file"></i></a>
					<a class="btn btn-large" title="Manage Users"><i class="fa fa-user"></i></a>
					<a class="btn btn-large" title="Manage Comments"><i class="fa fa-comment"></i><span class="label label-danger">5</span></a>
					<a class="btn btn-large" title="Manage Orders"><i class="fa fa-shopping-cart"></i></a>
				</div>
			</div>
			<div id="breadcrumb">
				<a href="#" title="Go to Home" class="tip-bottom"><i class="fa fa-home"></i> Home</a>
				<a href="#">UI Lab</a>
				<a href="#" class="current">Interface elements</a>
			</div>
				<div class="row">
					<div class="col-xs-12 center" style="text-align: center;">					
						<ul class="stat-boxes">
							<li class="popover-visits">
								<div class="left sparkline_bar_good"><span>2,4,9,7,12,10,12</span><i class="fa fa-double-angle-up"></i> +10%</div>
								<div class="right">
									<strong>36094</strong>
									Visits
								</div>
							</li>
							<li class="popover-users">
								<div class="left sparkline_bar_neutral"><span>20,15,18,14,10,9,9,9</span><i class="fa fa-minus"></i> 0%</div>
								<div class="right">
									<strong>1433</strong>
									Users
								</div>
							</li>
							<li class="popover-orders">
								<div class="left sparkline_bar_bad"><span>3,5,9,7,12,20,10</span><i class="fa fa-double-angle-down"></i> -50%</div>
								<div class="right">
									<strong>8650</strong>
									Orders
								</div>
							</li>
							<li class="popover-tickets">
								<div class="left sparkline_line_good"><span>12,6,9,23,14,10,17</span><i class="fa fa-double-angle-up"></i> +70%</div>
								<div class="right">
									<strong>2968</strong>
									Tickets
								</div>
							</li>
						</ul>
					</div>	
				</div>
				<div class="row">
					<div class="col-xs-12 center" style="text-align: center;">					
						<ul class="quick-actions">
							<li>
								<a href="#">
									<i class="icon-cal"></i>
									Manage Events
								</a>
							</li>
							<li>
								<a href="#">
									<i class="icon-shopping-bag"></i>
									Manage Orders
								</a>
							</li>
							<li>
								<a href="#">
									<i class="icon-database"></i>
									Manage DB
								</a>
							</li>
							<li>
								<a href="#">
									<i class="icon-people"></i>
									Manage Users
								</a>
							</li>
							<li>
								<a href="#">
									<i class="icon-lock"></i>
									Security
								</a>
							</li>
							<li>
								<a href="#">
									<i class="icon-piechart"></i>
									Statistics
								</a>
							</li>
						</ul>
					</div>	
				</div>
				<div class="row">
					<div class="col-xs-12 center" style="text-align: center;">					
						<ul class="quick-actions-horizontal">
							<li>
								<a href="#">
									<i class="icon-cal"></i>
									<span>Manage Events</span>
								</a>
							</li>
							<li>
								<a href="#">
									<i class="icon-shopping-bag"></i>
									<span>Manage Orders</span>
								</a>
							</li>
							<li>
								<a href="#">
									<i class="icon-database"></i>
									<span>Manage DB</span>
								</a>
							</li>
							<li>
								<a href="#">
									<i class="icon-people"></i>
									<span>Manage Users</span>
								</a>
							</li>
							<li>
								<a href="#">
									<i class="icon-lock"></i>
									<span>Security</span>
								</a>
							</li>
							<li>
								<a href="#">
									<i class="icon-piechart"></i>
									<span>Statistics</span>
								</a>
							</li>
						</ul>
					</div>	
				</div>
				<div class="row">
					<div class="col-xs-12">
						<div class="alert alert-warning">
							<button class="close" data-dismiss="alert">×</button>
							<strong>Warning!</strong> Your monthly traffic is reaching limit.
						</div>
						<div class="alert alert-success">
							<button class="close" data-dismiss="alert">×</button>
							<strong>Success!</strong> The page has been added.
						</div>
						<div class="alert alert-info">
							<button class="close" data-dismiss="alert">×</button>
							<strong>Info!</strong> You have 198 unread messages.
						</div>
						<div class="alert alert-danger">
							<button class="close" data-dismiss="alert">×</button>
							<strong>Error!</strong> The daily cronjob has failed.
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12 col-sm-6">
						<div class="row">
							<div class="col-xs-12 nopadding">
								<div class="input-group nopadding">
									<input type="text" class="form-control">
							      	<span class="input-group-btn">
							        	<button class="btn btn-primary" type="button"><i class="fa fa-search"></i> Search</button>
							      	</span>
								</div>
							</div>
						</div>
						<div class="widget-box">
								<div class="widget-title">
									<h5>Modal dialogs</h5>
								</div>
								<div class="widget-content">
									<a href="#myModal" data-toggle="modal" class="btn btn-dark-green">Modal dialog</a>
									<a href="#myAlert" data-toggle="modal" class="btn btn-dark-red">Alert</a>
									<hr />
									<h5>Modals provided by Bootbox.js</h5>
									<a href="#" id="bootbox-confirm" class="btn btn-success">Confirm Dialog</a>
									<a href="#" id="bootbox-prompt" class="btn btn-inverse">Prompt Dialog</a>
									<a href="#" id="bootbox-alert" class="btn btn-danger">Alert Dialog</a>
									
									<div id="myModal" class="modal fade">
										<div class="modal-dialog">
											<div class="modal-content">
												<div class="modal-header">
													<button data-dismiss="modal" class="close" type="button">×</button>
													<h3>Modal header</h3>
												</div>
												<div class="modal-body">
													<p>One fine body…</p>
												</div>
											</div>
										</div>
									</div>
									<div id="myAlert" class="modal fade">
										<div class="modal-dialog">
											<div class="modal-content">
												<div class="modal-header">
													<button data-dismiss="modal" class="close" type="button">×</button>
													<h3>Alert modal</h3>
												</div>
												<div class="modal-body">
													<p>Lorem ipsum dolor sit amet...</p>
												</div>
												<div class="modal-footer">
													<a data-dismiss="modal" class="btn btn-primary btn-small" href="#">Confirm</a>
													<a data-dismiss="modal" class="btn btn-default btn-small" href="#">Cancel</a>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						<div class="widget-box">
							<div class="widget-title">
								<span class="icon">
									<i class="fa fa-th-list"></i>
								</span>
								<h5>Progress bars</h5>
							</div>
							<div class="widget-content">
									<h5>Basic progress bars</h5>
									<code>class="progress progress-xs"</code><br /><br />
										<div class="progress progress-xs">
											<div style="width: 60%;" class="progress-bar progress-bar-success"></div>
										</div>
									<code>class="progress progress-sm"</code><br /><br />
										<div class="progress progress-sm">
											<div style="width: 60%;" class="progress-bar"></div>
										</div>
									<code>class="progress"</code><br /><br />
										<div class="progress">
											<div style="width: 60%;" class="progress-bar progress-bar-info"></div>
										</div>
									<hr />
									<h5>Striped progress bars</h5>
									<div class="alert alert-info">
										Internet Explorer doesn't support striped progress bars!
									</div>
									<div class="progress progress-striped">
										<div style="width: 20%;" class="progress-bar"></div>
									</div>
									<div class="progress progress-striped progress-xs">
										<div style="width: 30%;" class="progress-bar progress-bar-info"></div>
									</div>
									<div class="progress progress-striped">
										<div style="width: 40%;" class="progress-bar progress-bar-success"></div>
									</div>
									<div class="progress progress-striped progress-xs">
										<div style="width: 50%;" class="progress-bar progress-bar-warning"></div>
									</div>
									<div class="progress progress-striped">
										<div style="width: 60%;" class="progress-bar progress-bar-danger"></div>
									</div>
									<hr />
									<h5>Animated progress bars</h5>										
									<div class="alert alert-info">
										Internet Explorer doesn't support animated progress bars!
									</div>
									<div class="progress progress-striped active">
										<div style="width: 60%;" class="progress-bar"></div>
									</div>
									<div class="progress progress-striped active progress-xs">
										<div style="width: 50%;" class="progress-bar progress-bar-info"></div>
									</div>
									<div class="progress progress-striped active">
										<div style="width: 40%;" class="progress-bar progress-bar-success"></div>
									</div>
									<div class="progress progress-striped active progress-sm">
										<div style="width: 30%;" class="progress-bar progress-bar-warning"></div>
									</div>
									<div class="progress progress-striped active">
										<div style="width: 20%;" class="progress-bar progress-bar-danger"></div>
									</div>
									<hr />
									<h5>Stacked progress bars</h5>
									<div class="progress progress-striped">
										<div class="progress-bar progress-bar-success" style="width: 35%"></div>
										<div class="progress-bar progress-bar-warning" style="width: 25%"></div>
										<div class="progress-bar progress-bar-danger" style="width: 15%"></div>
									</div>
									<hr />
									<h5>More Colors</h5>
									<div class="progress progress-striped active">
										<div style="width: 60%;" class="progress-bar progress-bar-dark-green"></div>
									</div>
									<div class="progress progress-striped active progress-xs">
										<div style="width: 50%;" class="progress-bar progress-bar-pink"></div>
									</div>
									<div class="progress progress-striped active">
										<div style="width: 40%;" class="progress-bar progress-bar-purple"></div>
									</div>
									<div class="progress progress-striped active progress-sm">
										<div style="width: 30%;" class="progress-bar progress-bar-dark-blue"></div>
									</div>
									<div class="progress progress-striped active">
										<div style="width: 20%;" class="progress-bar progress-bar-dark-red"></div>
									</div>
									<div class="progress progress-striped active">
										<div style="width: 20%;" class="progress-bar progress-bar-yellow"></div>
									</div>
									<div class="progress progress-striped active">
										<div style="width: 20%;" class="progress-bar progress-bar-inverse"></div>
									</div>
							</div>
						</div>
						<div class="widget-box">
								<div class="widget-title">
									<span class="icon">
										<i class="fa fa-comment"></i>
									</span>
									<h5>Tooltip directions</h5>
								</div>
								<div class="widget-content">
									<p>Four directions of the tooltips, just add a class: <code>tip-top</code>, <code>tip-bottom</code>, <code>tip-left</code> or <code>tip-right</code> to the element you want to have tooltip.</p>
									<p>
										<button class="btn btn-default tip-top" data-original-title="Tooltip in top">Top</button>
										<button class="btn btn-default tip-left" data-original-title="Tooltip in left">Left</button>
										<button class="btn btn-default tip-right" data-original-title="Tooltip in right">Right</button>
										<button class="btn btn-default tip-bottom" data-original-title="Tooltip in bottom">Bottom</button>
									</p>
								</div>
							</div>
							<div class="widget-box">
								<div class="widget-title">
									<span class="icon">
										<i class="fa fa-comment"></i>
									</span>
									<h5>Growl-like notifications</h5>
								</div>
								<div class="widget-content" id="gritter-notify">
									<a class="normal btn btn-block btn-dark-red">Standard notification</a>
									<a class="sticky btn btn-block btn-dark-green">Sticky notification</a>
									<a class="image btn btn-block btn-primary" data-image="img/demo/envelope.png">Notification with image</a>
									<a class="light btn btn-block btn-default">Light-colored Notification</a>
								</div>
							</div>						
					</div>
					<div class="col-xs-12 col-sm-6">
						<div class="row">
							<div class="col-xs-12 col-sm-6">
								<input type="button" class="btn btn-success btn-block" value="Button block">
							</div> 
							<div class="col-xs-12 col-sm-6">
								<input type="button" class="btn btn-danger btn-block" value="Button block">
							</div>
						</div>
						<div class="widget-box">
							<div class="widget-title">
								<span class="icon">
									<i class="fa fa-bell"></i>
								</span>
								<h5>Notifications</h5>
							</div>
							<div class="widget-content">
								<div class="alert alert-block alert-warning">
									<a class="close" data-dismiss="alert" href="#">×</a>
									<h4 class="alert-heading">Warning!</h4>
									Best check yo self, you're not looking too good. Nulla vitae elit libero, a pharetra augue. Praesent commodo cursus magna, vel scelerisque nisl consectetur et.
								</div>
								<div class="alert alert-success alert-block">
									<a class="close" data-dismiss="alert" href="#">×</a>
									<h4 class="alert-heading">Success!</h4>
									Best check yo self, you're not looking too good. Nulla vitae elit libero, a pharetra augue. Praesent commodo cursus magna, vel scelerisque nisl consectetur et.
								</div>
								<div class="alert alert-info alert-block">
									<a class="close" data-dismiss="alert" href="#">×</a>
									<h4 class="alert-heading">Info!</h4>
									Best check yo self, you're not looking too good. Nulla vitae elit libero, a pharetra augue. Praesent commodo cursus magna, vel scelerisque nisl consectetur et.
								</div>
								<div class="alert alert-danger alert-block">
									<a class="close" data-dismiss="alert" href="#">×</a>
									<h4 class="alert-heading">Error!</h4>
									Best check yo self, you're not looking too good. Nulla vitae elit libero, a pharetra augue. Praesent commodo cursus magna, vel scelerisque nisl consectetur et.
								</div>
							</div>
						</div>
						<div class="widget-box">
								<div class="widget-title">
									<span class="icon">
										<i class="fa fa-tag"></i>
									</span>
									<h5>Labels and badges</h5>
								</div>
								<div class="widget-content">
									<h3>Available labels</h3>
									<table class="table table-bordered table-striped">
										<thead>
										  <tr>
											<th>Labels</th>
											<th>Class</th>
										  </tr>
										</thead>
										<tbody>
										  <tr>
											<td>
											  <span class="label label-default">Default</span>
											</td>
											<td>
											  <code>label label-default</code>
											</td>
										  </tr>
										  <tr>
											<td>
											  <span class="label label-success">Success</span>
											</td>
											<td>
											  <code>label label-success</code>
											</td>
										  </tr>
										  <tr>
											<td>
											  <span class="label label-warning">Warning</span>
											</td>
											<td>
											  <code>label label-warning;</code>
											</td>
										  </tr>
										  <tr>
											<td>
											  <span class="label label-danger">Danger</span>
											</td>
											<td>
											  <code>label label-danger</code>
											</td>
										  </tr>
										  <tr>
											<td>
											  <span class="label label-info">Info</span>
											</td>
											<td>
											  <code>label label-info</code>
											</td>
										  </tr>
										  <tr>
											<td>
											  <span class="label label-inverse">Inverse</span>
											</td>
											<td>
											  <code>label label-inverse</code>
											</td>
										  </tr>
										</tbody>
									  </table>
									<h3>Available badges</h3>
										<table class="table table-bordered table-striped">
										<thead>
										  <tr>
											<th>Name</th>
											<th>Example</th>
											<th>Class</th>
										  </tr>
										</thead>
										<tbody>
										  <tr>
											<td>
											  Default
											</td>
											<td>
											  <span class="badge">1</span>
											</td>
											<td>
											  <code>badge badge-default</code>
											</td>
										  </tr>
										  <tr>
											<td>
											  Success
											</td>
											<td>
											  <span class="badge badge-success">2</span>
											</td>
											<td>
											  <code>badge badge-success</code>
											</td>
										  </tr>
										  <tr>
											<td>
											  Warning
											</td>
											<td>
											  <span class="badge badge-warning">4</span>
											</td>
											<td>
											  <code>badge badge-warning</code>
											</td>
										  </tr>
										  <tr>
											<td>
											  Danger
											</td>
											<td>
											  <span class="badge badge-danger">6</span>
											</td>
											<td>
											  <code>badge badge-danger</code>
											</td>
										  </tr>
										  <tr>
											<td>
											  Info
											</td>
											<td>
											  <span class="badge badge-info">8</span>
											</td>
											<td>
											  <code>badge badge-info</code>
											</td>
										  </tr>
										  <tr>
											<td>
											  Inverse
											</td>
											<td>
											  <span class="badge badge-inverse">10</span>
											</td>
											<td>
											  <code>badge badge-inverse</code>
											</td>
										  </tr>
										</tbody>
									  </table>
								</div>
							</div>
							<div class="widget-box">
							<div class="widget-title">
								<span class="icon">
									<i class="fa fa-exclamation-circle"></i>
								</span>
								<h5>Pagination</h5>
							</div>
							<div class="widget-content">
								<h5>Default pagination</h5>
								<code>&lt;ul class="pagination"&gt;</code>
								<br />
									<ul class="pagination">
										<li><a href="#">Prev</a></li>
										<li class="active">
											<a href="#">1</a>
										</li>
										<li><a href="#">2</a></li>
										<li><a href="#">3</a></li>
										<li><a href="#">4</a></li>
										<li><a href="#">Next</a></li>
									</ul>
								<hr />
								<h5>Alternate pagination</h5>
								<code>&lt;ul class="pagination alternate"&gt;</code>
								
									<ul class="pagination alternate">
										<li class="disabled"><a href="#">Prev</a></li>
										<li class="active">
											<a href="#">1</a>
										</li>
										<li><a href="#">2</a></li>
										<li><a href="#">3</a></li>
										<li><a href="#">4</a></li>
										<li><a href="#">Next</a></li>
									</ul>
								<hr />
								<h5>Pager</h5>
								<ul class="pager">
									<li><a href="#">Previous</a></li>
									<li><a href="#">Next</a></li>
								</ul>
								<h5>Aligned</h5>
								<ul class="pager">
									<li class="previous"><a href="#">Previous</a></li>
									<li class="next"><a href="#">Next</a></li>
								</ul>

							</div>
						</div>
					</div>
				</div>
				
			
		</div>
		<div class="row">
			<div id="footer" class="col-xs-12">
				2012 - 2013 &copy; Unicorn Admin. Brought to you by <a href="https://wrapbootstrap.com/user/diablo9983">diablo9983</a>
			</div>
		</div>
	</div>
		
            
            <script src="js/jquery.min.js"></script>
            <script src="js/jquery-ui.custom.js"></script>
            <script src="js/bootstrap.min.js"></script>
            <script src="js/bootbox.min.js"></script>
            <script src="js/jquery.gritter.min.js"></script>
            <script src="js/jquery.sparkline.min.js"></script>
            
            <script src="js/jquery.nicescroll.min.js"></script>
            <script src="js/unicorn.js"></script>
            <script src="js/unicorn.interface.js"></script>
	</body>
</html>
