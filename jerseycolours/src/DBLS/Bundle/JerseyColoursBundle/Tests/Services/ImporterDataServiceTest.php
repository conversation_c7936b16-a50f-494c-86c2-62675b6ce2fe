<?php

namespace DBLS\Bundle\JerseyColoursBundle\Tests\Services;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use DBLS\Bundle\JerseyColoursBundle\Adapters\FileAdapter;

/**
 *
 * <AUTHOR>
 */
class ImporterDataServiceTest extends WebTestCase
{
    /**
     *
     * DI Container
     */
    static $container;

    /**
     *
     * @var DBLS\Bundle\JerseyColoursBundle\Services\ImporterService
     */
    static $service;

    /**
     * @var \Doctrine\ORM\EntityManager
     */
    private $em;

    /**
     * {@inheritDoc}
     */
    public function setUp()
    {
        self::bootKernel();
        $this->em = static::$kernel->getContainer()
            ->get('doctrine')
            ->getManager()
        ;
    }

    /**
     * {@inheritDoc}
     */
    protected function tearDown()
    {
        parent::tearDown();
        $this->em->close();
    }

    /**
     * {@inheritDoc}
     */
    public static function setUpBeforeClass()
    {
         //start the symfony kernel
         $kernel = static::createKernel();
         $kernel->boot();

         //get the DI container
         self::$container = $kernel->getContainer();

         self::$service = self::$container->get('services.importer');
    }


    public function testDummy() {
        $this->assertEquals(1,1);
    }

    public function xtestImporter_data()
    {
        $adapter = new FileAdapter(self::$container);


        self::$service->setAdapter($adapter);
        self::$service->processCreate();

    }


}
