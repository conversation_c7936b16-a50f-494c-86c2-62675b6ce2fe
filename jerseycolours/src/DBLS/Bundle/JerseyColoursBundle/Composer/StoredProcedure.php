<?php

namespace DBLS\Bundle\JerseyColoursBundle\Composer;

use Composer\Script\CommandEvent;
use \Sensio\Bundle\DistributionBundle\Composer\ScriptHandler;

class StoredProcedure extends ScriptHandler
{

    /**
     * 
     * @param  CommandEvent $event
     */
    public static function installLevenshtein(CommandEvent $event)
    {
        $command = 'jersey:install:levenshtein';
        $options = self::getOptions($event);
        $consoleDir = self::getConsoleDir($event, $command);

        static::executeCommand($event, $consoleDir, $command);
    }
}