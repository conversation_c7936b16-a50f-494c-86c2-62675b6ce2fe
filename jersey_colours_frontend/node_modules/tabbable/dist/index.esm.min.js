/*!
* tabbable 6.2.0
* @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
*/
var t=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"],e=t.join(","),n="undefined"==typeof Element,o=n?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,r=!n&&Element.prototype.getRootNode?function(t){var e;return null==t||null===(e=t.getRootNode)||void 0===e?void 0:e.call(t)}:function(t){return null==t?void 0:t.ownerDocument},i=function t(e,n){var o;void 0===n&&(n=!0);var r=null==e||null===(o=e.getAttribute)||void 0===o?void 0:o.call(e,"inert");return""===r||"true"===r||n&&e&&t(e.parentNode)},a=function(t,n,r){if(i(t))return[];var a=Array.prototype.slice.apply(t.querySelectorAll(e));return n&&o.call(t,e)&&a.unshift(t),a=a.filter(r)},l=function t(n,r,a){for(var l=[],u=Array.from(n);u.length;){var d=u.shift();if(!i(d,!1))if("SLOT"===d.tagName){var c=d.assignedElements(),f=t(c.length?c:d.children,!0,a);a.flatten?l.push.apply(l,f):l.push({scopeParent:d,candidates:f})}else{o.call(d,e)&&a.filter(d)&&(r||!n.includes(d))&&l.push(d);var s=d.shadowRoot||"function"==typeof a.getShadowRoot&&a.getShadowRoot(d),p=!i(s,!1)&&(!a.shadowRootFilter||a.shadowRootFilter(d));if(s&&p){var h=t(!0===s?d.children:s.children,!0,a);a.flatten?l.push.apply(l,h):l.push({scopeParent:d,candidates:h})}else u.unshift.apply(u,d.children)}}return l},u=function(t){return!isNaN(parseInt(t.getAttribute("tabindex"),10))},d=function(t){if(!t)throw new Error("No node provided");return t.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(t.tagName)||function(t){var e,n=null==t||null===(e=t.getAttribute)||void 0===e?void 0:e.call(t,"contenteditable");return""===n||"true"===n}(t))&&!u(t)?0:t.tabIndex},c=function(t,e){return t.tabIndex===e.tabIndex?t.documentOrder-e.documentOrder:t.tabIndex-e.tabIndex},f=function(t){return"INPUT"===t.tagName},s=function(t){return function(t){return f(t)&&"radio"===t.type}(t)&&!function(t){if(!t.name)return!0;var e,n=t.form||r(t),o=function(t){return n.querySelectorAll('input[type="radio"][name="'+t+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)e=o(window.CSS.escape(t.name));else try{e=o(t.name)}catch(t){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",t.message),!1}var i=function(t,e){for(var n=0;n<t.length;n++)if(t[n].checked&&t[n].form===e)return t[n]}(e,t.form);return!i||i===t}(t)},p=function(t){var e=t.getBoundingClientRect(),n=e.width,o=e.height;return 0===n&&0===o},h=function(t,e){var n=e.displayCheck,i=e.getShadowRoot;if("hidden"===getComputedStyle(t).visibility)return!0;var a=o.call(t,"details>summary:first-of-type")?t.parentElement:t;if(o.call(a,"details:not([open]) *"))return!0;if(n&&"full"!==n&&"legacy-full"!==n){if("non-zero-area"===n)return p(t)}else{if("function"==typeof i){for(var l=t;t;){var u=t.parentElement,d=r(t);if(u&&!u.shadowRoot&&!0===i(u))return p(t);t=t.assignedSlot?t.assignedSlot:u||d===t.ownerDocument?u:d.host}t=l}if(function(t){var e,n,o,i,a=t&&r(t),l=null===(e=a)||void 0===e?void 0:e.host,u=!1;if(a&&a!==t)for(u=!!(null!==(n=l)&&void 0!==n&&null!==(o=n.ownerDocument)&&void 0!==o&&o.contains(l)||null!=t&&null!==(i=t.ownerDocument)&&void 0!==i&&i.contains(t));!u&&l;){var d,c,f;u=!(null===(c=l=null===(d=a=r(l))||void 0===d?void 0:d.host)||void 0===c||null===(f=c.ownerDocument)||void 0===f||!f.contains(l))}return u}(t))return!t.getClientRects().length;if("legacy-full"!==n)return!0}return!1},v=function(t,e){return!(e.disabled||i(e)||function(t){return f(t)&&"hidden"===t.type}(e)||h(e,t)||function(t){return"DETAILS"===t.tagName&&Array.prototype.slice.apply(t.children).some((function(t){return"SUMMARY"===t.tagName}))}(e)||function(t){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(t.tagName))for(var e=t.parentElement;e;){if("FIELDSET"===e.tagName&&e.disabled){for(var n=0;n<e.children.length;n++){var r=e.children.item(n);if("LEGEND"===r.tagName)return!!o.call(e,"fieldset[disabled] *")||!r.contains(t)}return!0}e=e.parentElement}return!1}(e))},m=function(t,e){return!(s(e)||d(e)<0||!v(t,e))},g=function(t){var e=parseInt(t.getAttribute("tabindex"),10);return!!(isNaN(e)||e>=0)},y=function t(e){var n=[],o=[];return e.forEach((function(e,r){var i=!!e.scopeParent,a=i?e.scopeParent:e,l=function(t,e){var n=d(t);return n<0&&e&&!u(t)?0:n}(a,i),c=i?t(e.candidates):a;0===l?i?n.push.apply(n,c):n.push(a):o.push({documentOrder:r,tabIndex:l,item:e,isScope:i,content:c})})),o.sort(c).reduce((function(t,e){return e.isScope?t.push.apply(t,e.content):t.push(e.content),t}),[]).concat(n)},w=function(t,e){var n;return n=(e=e||{}).getShadowRoot?l([t],e.includeContainer,{filter:m.bind(null,e),flatten:!1,getShadowRoot:e.getShadowRoot,shadowRootFilter:g}):a(t,e.includeContainer,m.bind(null,e)),y(n)},S=function(t,e){return(e=e||{}).getShadowRoot?l([t],e.includeContainer,{filter:v.bind(null,e),flatten:!0,getShadowRoot:e.getShadowRoot}):a(t,e.includeContainer,v.bind(null,e))},b=function(t,n){if(n=n||{},!t)throw new Error("No node provided");return!1!==o.call(t,e)&&m(n,t)},E=t.concat("iframe").join(","),N=function(t,e){if(e=e||{},!t)throw new Error("No node provided");return!1!==o.call(t,E)&&v(e,t)};export{S as focusable,d as getTabIndex,N as isFocusable,b as isTabbable,w as tabbable};
//# sourceMappingURL=index.esm.min.js.map
