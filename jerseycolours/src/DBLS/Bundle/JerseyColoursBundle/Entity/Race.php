<?php

namespace DBLS\Bundle\JerseyColoursBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Doctrine\Common\Collections\ArrayCollection;
use SamJ\DoctrineSluggableBundle\SluggableInterface;
use Doctrine\Common\Collections\Criteria;

/**
 * Race
 *
 * @ORM\Table()
 * @ORM\Entity(repositoryClass="DBLS\Bundle\JerseyColoursBundle\Entity\RaceRepository")
 */
class Race implements SluggableInterface
{

    /**
     * @var integer
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="externalRaceId", type="string")
     */
    private $externalRaceId;

    /**
     * @var string
     *
     * @ORM\Column(name="externalMeetId", type="string")
     */
    private $externalMeetId;
    
    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=255)
     */
    private $name;
    
    /**
     * @var integer
     *
     * @ORM\Column(name="number", type="integer")
     */
    private $number;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="racetime", type="datetime")
     */
    private $racetime;
    
    /**
     * @var integer
     *
     * @ORM\Column(name="approved", type="boolean")
     */
    private $approved = false;

    /**
     * @ORM\OneToMany(targetEntity="Jersey", mappedBy="race")
     */
    private $jerseys;

    /**
     * @ORM\Column(type="string")
     */
    protected $slug;

    /**
     * @ORM\ManyToOne(targetEntity="Meeting", inversedBy="races")
     * @ORM\JoinColumn(name="meeting_id", referencedColumnName="id", nullable=false)
    */
    protected $meeting;
    
    /*
     * 
     */
    public function __construct()
    {
	   $this->jerseys = new ArrayCollection();
    }

    /**
     * Get id
     *
     * @return integer 
     */
    public function getId()
    {
	   return $this->id;
    }

    /**
     * Set name
     *
     * @param string $name
     * @return Race
     */
    public function setName( $name )
    {
    	$this->name = $name;

    	return $this;
    }

    /**
     * Get name
     *
     * @return string 
     */
    public function getName()
    {
	   return $this->name;
    }

    /**
     * Add jerseys
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $jerseys
     * @return Race
     */
    public function addJersey( \DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $jerseys )
    {
    	$this->jerseys[] = $jerseys;

    	return $this;
    }

    /**
     * Remove jerseys
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $jerseys
     */
    public function removeJersey( \DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $jerseys )
    {
	   $this->jerseys->removeElement( $jerseys );
    }

    /**
     * Get jerseys
     *
     * @return \Doctrine\Common\Collections\Collection 
     */
    public function getJerseys()
    {
	   return $this->jerseys;
    }


    /**
     * Set approved
     *
     * @param boolean $approved
     * @return Race
     */
    public function setApproved($approved)
    {
        $this->approved = $approved;

        return $this;
    }

    /**
     * Get approved
     *
     * @return boolean 
     */
    public function getApproved()
    {
        return $this->approved;
    }
    
    /**
     * Check whether the Race and its Jerseys has error or not
     * 
     * @return boolean
     */
    public function hasError()
    {
    	$error = false;
    	$jerseys = $this->getUnapprovedJerseys();
    	foreach ( $jerseys as $jersey ) {
    	    $error = $jersey->hasError();
            if ( $error ) break;
        }
    	
    	return $error;
    }

    /**
    * @return string
    */
    public function getSlug()
    {
        return $this->slug;
    }

    /**
    * @param string
    */
    public function setSlug($slug)
    {
        if (!empty($this->slug)) return false;
        $this->slug = $slug;
    }

    /**
    * @return array
    */
    public function getSlugFields() 
    {
        $slug = array($this->getMeeting()->getId(), $this->name);
        return $slug;
    }

    /**
     * Set externalRaceId
     *
     * @param integer $externalRaceId
     * @return Race
     */
    public function setExternalRaceId($externalRaceId)
    {
        $this->externalRaceId = $externalRaceId;

        return $this;
    }

    /**
     * Get externalRaceId
     *
     * @return integer 
     */
    public function getExternalRaceId()
    {
        return $this->externalRaceId;
    }

    /**
     * Set externalMeetId
     *
     * @param integer $externalMeetId
     * @return Race
     */
    public function setExternalMeetId($externalMeetId)
    {
        $this->externalMeetId = $externalMeetId;

        return $this;
    }

    /**
     * Get externalMeetId
     *
     * @return integer 
     */
    public function getExternalMeetId()
    {
        return $this->externalMeetId;
    }

    /**
     * Set number
     *
     * @param integer $number
     * @return Race
     */
    public function setNumber($number)
    {
        $this->number = $number;

        return $this;
    }

    /**
     * Get number
     *
     * @return integer 
     */
    public function getNumber()
    {
        return $this->number;
    }

    /**
     * Set racetime
     *
     * @param \DateTime $racetime
     * @return Race
     */
    public function setRacetime($racetime)
    {
        $racetime = strtotime( $racetime );        
        $mysqldate = \DateTime::createFromFormat(
                    'Y-m-d H:i:s',
                    date( 'Y-m-d H:i:s', $racetime ),
                    new \DateTimeZone("GMT")
                );
        
        $this->racetime = $mysqldate;

        return $this;
    }

    /**
     * Get racetime
     *
     * @return \DateTime 
     */
    public function getRacetime()
    {
        return $this->racetime;
    }

    /**
     * Set meeting
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Meeting $meeting
     * @return Race
     */
    public function setMeeting(\DBLS\Bundle\JerseyColoursBundle\Entity\Meeting $meeting = null)
    {
        $this->meeting = $meeting;

        return $this;
    }

    /**
     * Get meeting
     *
     * @return \DBLS\Bundle\JerseyColoursBundle\Entity\Meeting 
     */
    public function getMeeting()
    {
        return $this->meeting;
    }

    /**
     * @return string 
     */
    public function getFolderPath()
    {
        $meetingPath = $this->getMeeting()->getFolderPath();

        //$raceFolder = implode('_', array($this->number, $this->getSlug()));
        $raceFolder = $this->getSlug();

        $path = array($meetingPath, $raceFolder);

        return implode('/', $path);
    }

    /**
     * 
     * @param  integer $approved
     * @return \Doctrine\Common\Collections\ArrayCollection
     */
    protected function _filterJerseysByStatus($approved = 0)
    {
        $criteria = Criteria::create();
        $criteria->where(
            Criteria::expr()->andX(
                Criteria::expr()->eq('status', $approved),
                Criteria::expr()->eq('parsed', 1)
            )
        )->orderBy(array("number" => Criteria::ASC));

        //var_dump($criteria);exit;

        return $this->jerseys->matching($criteria);
    }

    /**
     * Get jerseys
     *
     * @return \Doctrine\Common\Collections\Collection 
     */
    public function getUnapprovedJerseys()
    {
        return $this->_filterJerseysByStatus();
    }

    /**
     * Get jerseys
     *
     * @return \Doctrine\Common\Collections\Collection 
     */
    public function getApprovedJerseys()
    {
        return $this->_filterJerseysByStatus(1);
    }

    /**
     * 
     * @return string
     */
    public function getCleanFolder()
    {
        $meetingId = $this->getMeeting()->getId() . '_';
        return str_replace($meetingId, '', $this->getSlug());   
    }

    /**
     * 
     * @return string
     */
    public function getPdfName()
    {
        $meeting = $this->getMeeting();
        $meetingCode = $meeting->getCode();

        if ( empty($meetingCode) )
            $meetingCode = substr($meeting->getLocation(), 0, 3);

        $number = $this->getNumber();
        $number = str_pad($number, 2, '0', STR_PAD_LEFT);

        $code = "CAAPG3-%s%s%s.jpg";
        return sprintf($code,
                    $meeting->getDate()->format('Ymd'),
                    strtoupper($meetingCode),
                    $number
                );
    }
}
