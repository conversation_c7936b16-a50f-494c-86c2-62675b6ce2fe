<?php

namespace DBLS\Bundle\JerseyColoursBundle\StringParser;

/**
 * Description of Lexer
 *
 * <AUTHOR> <adrianorc.com>
 */
class Lexer {

    const S_DICTIONARY = 'S_DICTIONARY';
    const S_LEVENSHTEIN = 'S_LEVENSHTEIN';
    const S_DIRECTMATCH = 'S_DIRECTMATCH';

    /**
     * Array of synonyms
     */
    private $synonyms;
    
    /**
    * Queryable pspell
    *
    * @var IPspellQueryable
    */
    private $_dictionary;
    
    /**
     * Levenshtein Ratio
     *
     * @var int
     */
    private $_levenshteinRatio;

    /**
     *
     * @var IterableCollection
     */
    private $knownColours;

    /**
     *
     * @var IterableCollection
     */
    private $knownPatterns;

    /**
     *
     * @var IterableCollection
     */
    private $knownElements;

    /**
     *
     * @var array
     */
    private $tokensFound;

    /**
     *
     * @var array 
     */
    private $patternsPerElements;
    
    //-------------------------------------------------
    //
    //  Public methods
    //
    //-------------------------------------------------

    public function __construct(IPspellQueryable $dictionary) {
        $this->_dictionary = $dictionary;
    }
    
    public function setLevenshteinRatio($value)
    {
        $this->_levenshteinRatio = $value;
    }

    public function getLevenshteinRatio()
    {
        return $this->_levenshteinRatio;
    }
    
    public function tokenize($pString)
    {
        
        $string = $this->_preCheckings($pString);

        $tokens = array();

        $offset = 0;
        while ($offset < strlen($string)) {

            $result = $this->_match($string, $offset);
            if ( false === $result ) {
                $nextSpace = strpos($string, ' ', $offset);
                if ( $nextSpace === false) {
                    // there is no more spaces in the string.
                    $result['match'] = substr($string, $offset);
                    $result['token'] = Tokens::T_UNKOWN;
                    $result['original_term'] = $result['match'];
                    $result['len'] = strlen($result['match']);
                    $tokens[] = $result;
                    $offset += strlen($result['match']);
                }
                else {
                    $nextComma = strpos($string, ',', $offset);
                    if ( ($nextComma !== false) && ($nextComma < $nextSpace) ) {
                        // we find a comma before next space
                        $result['match'] = substr($string, $offset, $nextComma-$offset);
                        $result['token'] = Tokens::T_UNKOWN;
                        $result['original_term'] = $result['match'];
                        $result['len'] = strlen($result['match']);
                        $tokens[] = $result;
                        $offset += strlen($result['match']);
                    }
                    else {
                        $result['match'] = substr($string, $offset, $nextSpace-$offset);
                        $result['token'] = Tokens::T_UNKOWN;
                        $result['original_term'] = $result['match'];
                        $result['len'] = strlen($result['match']);
                        $tokens[] = $result;
                        $offset += strlen($result['match'])+1;
                    }
                }
            }
            else {
                $tokens[] = $result;
                $offset += intval($result['len']);
                
                //find any silly spaces
                if (preg_match("/^([\s]+)/", substr($string, $offset), $matches)) {
                    $offset += strlen($matches[1]);
                }
            }

        }

        $this->tokensFound = $tokens;
        
//        echo "<pre>";
//        print_r($tokens);
//        die();
    }

    /**
     *
     * @return array
     */
    public function getTokens() {
        return $this->tokensFound;
    }

    /**
     * This method returns the original term for a term.
     * 
     * @param string $term
     */
    public function getOriginalTerm($term) {
        if (!empty($this->tokensFound)) {
            foreach($this->tokensFound as $token) {
                if (strtolower(trim($term)) == strtolower(trim($token['match']))) {
                    if (!empty($token['original_term'])) {
                        return $token['original_term'];
                    }
                }
            }
        }
        return $term;
    }
    
    /**
     *
     * @param array $array
     */
    public function setKnownColours($array) {
        $this->knownColours = new IterableCollection($array);
    }

    /**
     *
     * @param array $array
     */
    public function setKnownPatterns($array, $patternsPerElement) {
        $this->knownPatterns = new IterableCollection($array);
        $this->patternsPerElements = $patternsPerElement;
    }

    /**
     *
     * @param array $array
     */
    public function setKnownElements($array) {
        $this->knownElements = new IterableCollection($array);
    }
    
    /**
     * Set synonyms
     * 
     * @param array $array
     */
    public function setSynonyms($array) {
        $this->synonyms = $array;
    }

    /**
     * Return a list of avaliable patterns for the $elementName
     * 
     * @param string $elementName
     * @return array
     */
    public function getAvaliablePatternsForElement($elementName) {
        $newElementName = strtolower(trim($elementName));
        
        if (empty($this->patternsPerElements[$newElementName])) {
            return null;
        }
        
        return $this->patternsPerElements[$newElementName];
    }
    
    public function findPatternToElement($elementName, $patternDescription) {
        $avaliablePatterns = $this->getAvaliablePatternsForElement($elementName);
        
        if (empty($avaliablePatterns)) {
            return null;
        }

        // First, try to find a synonym
        if (!empty($this->synonyms)) {
            foreach($this->synonyms as $arrSynonym) {
                if ($arrSynonym['term'] == strtolower(trim($patternDescription))) {
                    foreach($avaliablePatterns as $avaliablePattern) {
                        if ($avaliablePattern == strtolower(trim($arrSynonym['original_term']))) {
                            return array(
                                'token' => Tokens::T_PATTERN,
                                'match' => $arrSynonym['original_term'],
                                'src' => self::S_DICTIONARY
                            );
                        }
                    }
                }
            }
        }
        
        // Next, try levenshtein
        $result = $this->_matchLevenshtein(
                new IterableCollection($avaliablePatterns),
                $patternDescription,
                Tokens::T_PATTERN
        );
        
        if (false !== $result) {
            return $result;
        }
        
        return null;
    }
    
    //-------------------------------------------------
    //
    //  Private methods
    //
    //-------------------------------------------------

    private function _preCheckings($string) {
        
        if ( !empty($this->synonyms) ) {
            foreach($this->synonyms as $synonym) {
                $arr = explode(' ', $synonym['term']);
                
                if ( count($arr) > 1 ) {
                    // replacement is made only for terms with 2 words or more.
                    // Remember: navy is synonym for navy blue. So, if in the incoming string 
                    // there is already a "navy blue", that maybe will become "navy navy blue"
                    $pattern = sprintf('/%s/i', $synonym['term']);
                    $replacement = $synonym['original_term'];
                    $string = preg_replace($pattern, $replacement, $string);
                }
                
            }
        }
        
        return $string;
    }
    
    private function _match($item, $offset) {
        $string = substr($item, $offset);

        $result = $this->_matchPunctuation(new IterableCollection(array(Tokens::id_comma)), $string, Tokens::T_COMMA);
        if ( $result !== false ) { return $result; }

        $result = $this->_matchPunctuation(new IterableCollection(array(Tokens::id_colon)), $string, Tokens::T_COLON);
        if ( $result !== false ) { return $result; }

        $result = $this->_matchWord(new IterableCollection(array(Tokens::id_and, Tokens::id_ampersand)), $string, Tokens::T_AND);
        if ( $result !== false ) { return $result; }

        $result = $this->_matchWord(new IterableCollection(array(Tokens::id_with)), $string, Tokens::T_WITH);
        if ( $result !== false ) { return $result; }

        //
        //-- BEGIN - Direct match
        //
        

        // NOTE: firstly, we have to try to identify a colour. Look at this string:
        //   "Brown, White Braces And Sleeves, Quartered Cap"
        //   "Brown" -> colour. But also, there is a pattern called crown.
        //   So, if we first try to identify a pattern, brown will be identified as a pattern.
        
        // try to find a colour token
        $result = $this->_matchWord($this->knownColours, $string, Tokens::T_COLOUR);
        if ( $result !== false ) { return $result; }

        // try to find a pattern token
        $result = $this->_matchWord($this->knownPatterns, $string, Tokens::T_PATTERN);
        if ( $result !== false ) { return $result; }

        // try to find a element token
        $result = $this->_matchWord($this->knownElements, $string, Tokens::T_ELEMENT);
        if ( $result !== false ) { return $result; }

        //
        //-- END - Direct match
        //
        
        
        //
        //-- BEGIN - Levenshtein
        //
        
        $result = $this->_matchLevenshtein($this->knownColours, $string, Tokens::T_COLOUR);
        if ( $result !== false ) { return $result; }
        
        $result = $this->_matchLevenshtein($this->knownPatterns, $string, Tokens::T_PATTERN);
        if ( $result !== false ) { return $result; }
        
        $result = $this->_matchLevenshtein($this->knownElements, $string, Tokens::T_ELEMENT);
        if ( $result !== false ) { return $result; }
        
        //
        //-- END - Levenshtein
        //
        
        if ( null != $this->_dictionary ) {
            // if it was not possible to match a token even with levenshtein, try
            // to identify it using pspell
            $result = $this->_try_pspell($string);
            if ( $result !== false ) { return $result; }
        }
        
        return false;
    }

    /**
     * This method try to identify $string usgin pspell
     *
     * @param string $string
     * @return array
     */
    private function _try_pspell($string) {
        $nextComma = strpos($string, ',');
        if ( false === $nextComma ) {
            $nextComma = strlen($string);
        }
        
        $substr = substr($string, 0, $nextComma);
        
        $arr = explode(' ', $substr);
        while ( count($arr) > 0 ) { // must be >, not >=
            $streval = implode(' ', $arr);
            $len = strlen($streval);
            
            $result = $this->_dictionary->query($streval);
            if ( false  !==  $result ) {
                $result['len'] = $len;
                $result['src'] = self::S_DICTIONARY;
                $result['original_term'] = $streval;
                return $result;
            }
            
            $trash = array_pop($arr);
        }

        return false;
    }
    
    
    /**
     *
     * @param \Iterator $tokens
     * @param string $string
     * @param string $token_type
     * @return array
     */
    private function _matchWord(\Iterator $tokens, $string, $token_type) {
        $matches = array();
        foreach ($tokens as $token) {
            if ( trim($token) == "" ) continue; // avoid infinite loop

            if (preg_match(sprintf("/^(\s*%s\s+)/i", $token), $string, $matches)) {
                $result = array(
                  'match' => $matches[1],
                  'token' => $token_type,
                  'len' => strlen($matches[1]),
                  'src' => $result['src'] = self::S_DIRECTMATCH,
                  'original_term' => $matches[1]
                );
                return $result;
            }

            if (preg_match(sprintf("/^(%s\s*),/i", $token), $string, $matches)) {
                $result = array(
                  'match' => $matches[1],
                  'token' => $token_type,
                  'len' => strlen($matches[1]),
                  'src' => $result['src'] = self::S_DIRECTMATCH,
                  'original_term' => $matches[1]
                );
                return $result;
            }

            if (preg_match(sprintf("/^(%s\s*)$/i", $token), $string, $matches)) {
                $result = array(
                  'match' => $matches[1],
                  'token' => $token_type,
                  'len' => strlen($matches[1]),
                  'src' => $result['src'] = self::S_DIRECTMATCH,
                  'original_term' => $matches[1]
                );
                return $result;
            }
        }
        return false;
    }

    private function _matchPunctuation(\Iterator $tokens, $string, $token_type) {
        $result = false;
        $matches = array();
        foreach ($tokens as $token) {
            if (preg_match(sprintf("/^(\s*%s\s*)/", $token), $string, $matches)) {
                $result = array(
                  'match' => $matches[1],
                  'token' => $token_type,
                  'len' => strlen($matches[1]),
                  'src' => $result['src'] = self::S_DIRECTMATCH,
                  'original_term' => $matches[1]
                );
            }
        }
        return $result;
    }

    private function _findNextSpace($string, $offset) {
        $pos = strpos($string, ' ', $offset);
        if (false === $pos) {
            return strlen($string);
        }
        return $pos;
    }

    private function _matchLevenshtein(\Iterator $tokens, $string, $token_type) {
        $coefList = array();
        foreach ($tokens as $token) {

            if ( trim($token) == '' ) continue;
            
            $nextComma = strpos($string, ',');
            if ( false === $nextComma ) {
                $nextComma = strlen($string);
            }

            $substr = trim(substr($string, 0, $nextComma));

            $arr1 = explode(' ', $substr);
            $arr2 = array();
            while (count($arr1) > 0) {
                array_push($arr2, array_shift($arr1));
                $original_term = implode(' ', $arr2);
                $len = strlen($original_term);
                
                $coefList[] = array(
                    'levenshtein' => levenshtein($token, strtolower($original_term)),
                    'max_len' => strlen($token) > strlen($original_term) ? strlen($token) : strlen($original_term),
                    'len' => $len,
                    'token' => $token_type,
                    'match' => $token,
                    'original_term' => $original_term
                );
                
            }
            
        }

        
        foreach($coefList as $k => $v) {
            $leven[$k] = $v['levenshtein'];
            $length[$k] = $v['len'];
        }
        
        array_multisort($leven, SORT_ASC, $length, SORT_ASC, $coefList);
        
        $closest = array_shift($coefList);
        
        if (empty($closest)) {
            return false;
        }
        
        if (intval($closest['max_len']) <= 0) {
            return false;
        }
        
        $ratio = round((1 - $closest['levenshtein'] / $closest['max_len']) * 100);
        if (intval($ratio) >= intval($this->getLevenshteinRatio())) {
            $result = $closest;
            $result['src'] = self::S_LEVENSHTEIN;
            unset($result['levenshtein']);
            unset($result['max_len']);
            return $result;
        }

        return false;
    }
    
}
