<?php

namespace DBLS\Bundle\JerseyColoursBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Doctrine\Common\Collections\ArrayCollection;

/**
 * Synonym
 *
 * @ORM\Table()
 * @ORM\Entity(repositoryClass="DBLS\Bundle\JerseyColoursBundle\Entity\SynonymRepository")
 */
class Synonym
{

    const TYPE_COLOUR = 1;
    const TYPE_PATTERN = 2;
    const TYPE_ELEMENT = 3;
    
    /**
     * @var integer
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @ORM\ManyToMany(targetEntity="Jersey", mappedBy="synonyms")
     **/
   private $jerseys;
   
   /**
     * @ORM\ManyToOne(targetEntity="Colour", inversedBy="synonyms")
     * @ORM\JoinColumn(name="colour_id", referencedColumnName="id", nullable=true)
    */
   protected $colour;
   
   /**
     * @ORM\ManyToOne(targetEntity="Pattern", inversedBy="synonyms")
     * @ORM\JoinColumn(name="pattern_id", referencedColumnName="id", nullable=true)
    */
   protected $pattern;
   
   /**
     * @ORM\ManyToOne(targetEntity="ElementType", inversedBy="synonyms")
     * @ORM\JoinColumn(name="element_type_id", referencedColumnName="id", nullable=true)
    */
   protected $element_type;
   
   /**
     * @var string
     *
     * @ORM\Column(name="term", type="string", length=255)
     */
    private $term;
   
   /**
     * @var string
     *
     * @ORM\Column(name="original_term", type="string", length=255)
     */
    private $original_term;
    
    /**
     * @var integer
     *
     * @ORM\Column(name="type", type="integer")
     */
    private $type;
   
   public function __construct()
   {
       $this->jerseys = new ArrayCollection();
   }
   

    /**
     * Get id
     *
     * @return integer 
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Add jerseys
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $jerseys
     * @return Synonym
     */
    public function addJersey(\DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $jerseys)
    {
        $this->jerseys[] = $jerseys;

        return $this;
    }

    /**
     * Remove jerseys
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $jerseys
     */
    public function removeJersey(\DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $jerseys)
    {
        $this->jerseys->removeElement($jerseys);
    }

    /**
     * Get jerseys
     *
     * @return \Doctrine\Common\Collections\Collection 
     */
    public function getJerseys()
    {
        return $this->jerseys;
    }

    /**
     * Set colour
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Colour $colour
     * @return Synonym
     */
    public function setColour(\DBLS\Bundle\JerseyColoursBundle\Entity\Colour $colour = null)
    {
        $this->colour = $colour;

        return $this;
    }

    /**
     * Get colour
     *
     * @return \DBLS\Bundle\JerseyColoursBundle\Entity\Colour 
     */
    public function getColour()
    {
        return $this->colour;
    }

    /**
     * Set pattern
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Pattern $pattern
     * @return Synonym
     */
    public function setPattern(\DBLS\Bundle\JerseyColoursBundle\Entity\Pattern $pattern = null)
    {
        $this->pattern = $pattern;

        return $this;
    }

    /**
     * Get pattern
     *
     * @return \DBLS\Bundle\JerseyColoursBundle\Entity\Pattern 
     */
    public function getPattern()
    {
        return $this->pattern;
    }

    /**
     * Set element_type
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\ElementType $elementType
     * @return Synonym
     */
    public function setElementType(\DBLS\Bundle\JerseyColoursBundle\Entity\ElementType $elementType = null)
    {
        $this->element_type = $elementType;

        return $this;
    }

    /**
     * Get element_type
     *
     * @return \DBLS\Bundle\JerseyColoursBundle\Entity\ElementType 
     */
    public function getElementType()
    {
        return $this->element_type;
    }

    /**
     * Set term
     *
     * @param string $term
     * @return Synonym
     */
    public function setTerm($term)
    {
        $this->term = $term;

        return $this;
    }

    /**
     * Get term
     *
     * @return string 
     */
    public function getTerm()
    {
        return $this->term;
    }

    /**
     * Set original_term
     *
     * @param string $originalTerm
     * @return Synonym
     */
    public function setOriginalTerm($originalTerm)
    {
        $this->original_term = $originalTerm;

        return $this;
    }

    /**
     * Get original_term
     *
     * @return string 
     */
    public function getOriginalTerm()
    {
        return $this->original_term;
    }

    /**
     * Set type
     *
     * @param integer $type
     * @return Synonym
     */
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    /**
     * Get type
     *
     * @return integer 
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * 
     * @return string
     */
    public function getTypeName()
    {
        switch( $this->type ){
            case 1 :
                $type = 'Colour';
                break;
            case 2:
                $type = 'Pattern';
                break;
            case 3:
                $type = 'Element';
                break;
            default :
                $type = null;
        }

        return $type;
    }
}
