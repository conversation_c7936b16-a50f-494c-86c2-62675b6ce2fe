{% extends 'site.html.twig' %}

{% block page_header %}
    <div id="content-header">
        <h1>Dashboard</h1>	
    </div>
{% endblock page_header %}

{% block breadcrumb %}
    <div id="breadcrumb">
        <a href="{{ path('homepage') }}" class="tip-bottom" data-original-title="Go to Home"><i class="fa fa-home"></i> Home</a>
        <a href="{{ path('homepage') }}" class="current">Dashboard</a>
    </div>
{% endblock breadcrumb %}

{% block content %}

    <div class="row">
        <div style="text-align: center;" class="col-xs-12 center">					
            <ul class="quick-actions">

                <li>
                    <a href="{{ path('user') }}">
                        <i class="icon-people"></i>
                        Manage Users
                    </a>
                </li>
                <li>
                    <a href="{{ path('colour') }}">
                        <i class="icon-colours"></i>
                        Manage Colours
                    </a>
                </li>
                <li>
                    <a href="{{ path('pattern') }}">
                        <i class="icon-download"></i>
                        Manage Patterns
                    </a>
                </li>
                <li>
                    <a href="{{ path('jersey') }}">
                        <i class="icon-jersey"></i>
                        Manage Silks
                    </a>
                </li>
            </ul>
        </div>	
    </div>

    {% set class_col = 'col-xs-12 col-sm-6' %}
    {% set hide = '' %}

    {% if count_jersey == 0 %}
        {% set class_col = 'col-xs-12 col-sm-12' %}
        {% set hide = 'hide' %}
    {% endif %}                        
                        
                        
    <!-- Recent Silks -->
    <div class="row">
        <div class="{{ hide }} col-xs-12 col-sm-6">
            <div class="widget-box">
                <div class="widget-title">
                    <span class="icon"><i class="fa fa-bars"></i></span>
                    <h5>Recent Silks</h5>
                    <span title="{{ count_jersey }} total silks" class="label label-info tip-left">{{ count_jersey }}</span>
                </div>
                <div class="widget-content nopadding">
                    <ul class="recent-posts">
                        {% for entity in entities_jersey %}
                        <li class="thumb-home">
                            <div class="col-sm-3 col-md-2">
                                <div class="thumbnail">
                                    <img alt="Silks" src="{{ asset(entity.getFrontViewImage) }}" alt="{{ entity.name }}">
                                </div>
                            </div>
                            <div class="article-post">
                                <span class="label status-jersey label{% if entity.status %}-success">Approved{% else %}-danger">Pending{% endif %}</span>
                                <p>
                                    {{ entity.name|truncate(50) }}
                                </p>
                                <a  href="javascript:void(0);" onclick="JockerColour.Jersey.showJerseyPreview('{{ asset(entity.getFrontViewImage|escape('js')) }}','{{ asset(entity.getSideViewImage|escape('js')) }}', '{{ entity.name|escape('js') }}')" class="btn btn-success btn-xs">Preview</a>
                            </div>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>

        <!-- Recent Patterns -->
        <div class="{{ class_col }}">
            <div class="widget-box">
                <div class="widget-title">
                    <span class="icon"><i class="fa fa-download"></i></span>
                    <h5>Recent Patterns</h5>
                    <span title="{{ count_pattern }} total patterns" class="label label-info tip-left">{{ count_pattern }}</span>
                </div>
                <div class="widget-content nopadding">
                    <ul class="recent-posts">
                        {% for entity in entities_pattern %}
                        <li class="thumb-home">
                            
                            <div class="col-sm-3 col-md-2">
                                <div class="thumbnail">
                                    <img alt="Pattern" src="{{ asset(entity.getFrontViewPath) }}">
                                </div>
                            </div>
                            <div class="article-post">
                                <p>
                                    <a href="{{ path('pattern_edit', {'id': entity.id}) }}">{{ entity|title }}</a>
                                </p>
                                <a href="{{ path('pattern_edit', {'id': entity.id}) }}" class="btn btn-primary btn-xs">Edit</a>
                                <a  href="javascript:void(0);" onclick="JockerColour.Pattern.showPatternPreview('{{ asset(entity.getFrontViewPath|escape('js')) }}','{{ asset(entity.getSideViewPath|escape('js')) }}', '{{ entity|escape('js') }}')" class="btn btn-success btn-xs">Preview</a>
                            </div>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
    </div>

{% endblock %}

{% block extrascripts %}
    {% javascripts 
	'@JerseyColoursBundle/Resources/public/js/bundle/jersey.js'
	'@JerseyColoursBundle/Resources/public/js/bundle/pattern.js' %}
	<script src="{{ asset_url }}"></script>
    {% endjavascripts %}
{% endblock extrascripts %}