<?php

namespace DBLS\Bundle\JerseyColoursBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class SilksFolderStructureCommand extends ContainerAwareCommand
{
    protected $container;

    /**
     * Holds an instance of database connection
     * @var EntityManager
     */
    private $em = null;

    /**
     * 
     */
    protected function configure()
    {
        $this->setName('jersey:folder')
             ->setDescription('Move the approved silks to the new format');
    }

    /**
     * 
     * @param  InputInterface  $input
     * @param  OutputInterface $output
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->container = $this->getContainer();

        $this->em = $this->container->get('doctrine.orm.entity_manager');
        $raceApprovalService = $this->container->get('services.race.approval');

        $entities = $this->em->getRepository('JerseyColoursBundle:Jersey')->findBy(array('status' => 1));

        $racesApproved = array();
        foreach ( $entities as $silk ) {
            $race = $silk->getRace();

            if ( in_array($race->getId(), $racesApproved) )
                continue;

            $raceApprovalService->moveJerseysToFinalPath($race, false);
            $racesApproved[] = $race->getId();
        }

        $jerseyGenerator = $this->container->get('services.jersey_generator');
        $silksDir = realpath($jerseyGenerator->getJerseysDir());

        $folderRemover = $this->container->get('services.utils.remove_folders');
        $folderRemover->clean($silksDir);

        $output->writeln(sprintf("%s races moved.", implode(',', $racesApproved)));
    }

}