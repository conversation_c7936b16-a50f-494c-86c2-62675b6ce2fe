<?php

namespace DBLS\Bundle\JerseyColoursBundle\DataFixtures\ORM;

use Doctrine\Common\DataFixtures\AbstractFixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Common\Persistence\ObjectManager;
use DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElement;

class LoadJerseyElementData extends AbstractFixture implements OrderedFixtureInterface
{

    /**
     * {@inheritDoc}
     */
    public function load(ObjectManager $manager)
    {

        $arrayJerseys = array(
            'jersey-thubiaan',
            'jersey-waltzing',
            'jersey-vatican',
            'jersey-toocool'
        );

        $arrayElementTypes = array(
            'elementType-base',
            'elementType-shirt',
            'elementType-cap',
            'elementType-peak',
            'elementType-sleeves',
            'elementType-collar',
            'elementType-cuffs',
            'elementType-tassel',
            'elementType-pompom',
            'elementType-band'
        );

        $arrayColours = new \ArrayIterator(
            array(
                'colour-white',
                'colour-yellow',
                'colour-blue',
                'colour-black',
                'colour-red'
            )
        );

        $colourIterator = new \InfiniteIterator($arrayColours);

        foreach( $arrayJerseys as $jersey ){

            $ordem = 1;

            $nameJersey = str_replace("jersey-","",$jersey);

            foreach( $arrayElementTypes as $elementType ){

                $nameElement = str_replace("elementType-","",$elementType);

                $jerseyElement = new JerseyElement();
                $jerseyElement->setJersey($this->getReference($jersey));
                $jerseyElement->setElementType($this->getReference($elementType));
                $jerseyElement->setOrder($ordem++);

                $colourIterator->next();
                $nextColour = $colourIterator->current();
                $jerseyElement->setColour($this->getReference($nextColour));

                $manager->persist($jerseyElement);
                $this->addReference('jersey-element-'.$nameJersey.'-'.$nameElement, $jerseyElement);

            }

        }

        $manager->flush();

    }

    /**
     * {@inheritDoc}
     */
    public function getOrder()
    {
        return 8;
    }

}
