# frozen_string_literal: true

module JerseyParser
  class Parser
    # Error constants
    E_TOKEN_UNKNOWN = 'E_TOKEN_UNKNOWN'
    E_NO_SEMANTICAL_MEANING = 'E_NO_SEMANTICAL_MEANING'
    E_INVALID_STRING_START = 'E_INVALID_STRING_START'
    E_PATTERN_NOT_RELATED_TO_ELEMENT = 'E_PATTERN_NOT_RELATED_TO_ELEMENT'

    # Element structure constants
    S_ELEMENT_NAME = 'element_name'
    S_PATTERNS = 'patterns'
    S_COLOURS = 'colours'
    S_COLOUR = 'colour'
    S_DESCRIPTION = 'description'

    ELEMENT_SHIRT = 'shirt'

    attr_accessor :lexer, :jersey_name, :elements_found, :errors

    def initialize(lexer)
      @lexer = lexer
      @jersey_name = ""
      @elements_found = []
      @errors = []
    end

    def parse(tokens)
      @errors = []
      @elements_found = []
      
      return if tokens.empty?

      # Process tokens to build jersey structure
      process_tokens(tokens)
      
      # Check for unknown tokens and add errors
      tokens.each do |token|
        if token.unknown?
          add_error(
            E_TOKEN_UNKNOWN,
            "The word '#{token.match}' (originally: #{token.original_term}) is unknown. It is not a colour, pattern or an element",
            token.match
          )
        end
      end
    end

    def parse_base(tokens)
      return false if tokens.empty?
      
      first_token = tokens.first
      if first_token.colour?
        # Base color found
        add_element({
          S_ELEMENT_NAME => 'base',
          S_COLOUR => first_token.value
        })
        return true
      end
      
      false
    end

    def get_elements
      @elements_found
    end

    def get_errors
      @errors
    end

    def get_jersey_name
      @jersey_name
    end

    def find_element(element_name)
      @elements_found.find do |element|
        element[S_ELEMENT_NAME]&.downcase == element_name.downcase
      end
    end

    def include_error(error_type, error_message, error_expression)
      add_error(error_type, error_message, error_expression)
    end

    def get_original_term(term)
      @lexer.get_original_term(term)
    end

    private

    def process_tokens(tokens)
      i = 0
      while i < tokens.length
        token = tokens[i]
        
        case token.type
        when Token::T_COLOUR
          # Look ahead for patterns and elements
          element_info = parse_colour_sequence(tokens, i)
          add_element(element_info) if element_info
          i += element_info[:consumed] if element_info
        when Token::T_PATTERN
          # Pattern without preceding color
          element_info = parse_pattern_sequence(tokens, i)
          add_element(element_info) if element_info
          i += element_info[:consumed] if element_info
        when Token::T_ELEMENT
          # Element without preceding color or pattern
          element_info = parse_element_sequence(tokens, i)
          add_element(element_info) if element_info
          i += element_info[:consumed] if element_info
        else
          i += 1
        end
      end
    end

    def parse_colour_sequence(tokens, start_index)
      colour_token = tokens[start_index]
      consumed = 1
      
      # Look for following pattern and/or element
      next_token = tokens[start_index + 1]
      
      if next_token&.pattern?
        pattern_token = next_token
        consumed += 1
        
        # Look for element after pattern
        element_token = tokens[start_index + 2]
        if element_token&.element?
          consumed += 1
          return {
            S_ELEMENT_NAME => element_token.value,
            S_COLOUR => colour_token.value,
            S_PATTERNS => [{ 'pattern' => pattern_token.value, 'colours' => [colour_token.value] }],
            consumed: consumed
          }
        else
          # Pattern without specific element, assume shirt
          return {
            S_ELEMENT_NAME => ELEMENT_SHIRT,
            S_COLOUR => colour_token.value,
            S_PATTERNS => [{ 'pattern' => pattern_token.value, 'colours' => [colour_token.value] }],
            consumed: consumed
          }
        end
      elsif next_token&.element?
        consumed += 1
        return {
          S_ELEMENT_NAME => next_token.value,
          S_COLOUR => colour_token.value,
          consumed: consumed
        }
      else
        # Just a color, assume base
        return {
          S_ELEMENT_NAME => 'base',
          S_COLOUR => colour_token.value,
          consumed: consumed
        }
      end
    end

    def parse_pattern_sequence(tokens, start_index)
      pattern_token = tokens[start_index]
      consumed = 1
      
      # Look for following element
      next_token = tokens[start_index + 1]
      
      if next_token&.element?
        consumed += 1
        return {
          S_ELEMENT_NAME => next_token.value,
          S_PATTERNS => [{ 'pattern' => pattern_token.value, 'colours' => [] }],
          consumed: consumed
        }
      else
        # Pattern without specific element, assume shirt
        return {
          S_ELEMENT_NAME => ELEMENT_SHIRT,
          S_PATTERNS => [{ 'pattern' => pattern_token.value, 'colours' => [] }],
          consumed: consumed
        }
      end
    end

    def parse_element_sequence(tokens, start_index)
      element_token = tokens[start_index]
      
      {
        S_ELEMENT_NAME => element_token.value,
        consumed: 1
      }
    end

    def add_element(element)
      @elements_found << element
    end

    def add_error(error_type, error_message, error_expression)
      @errors << {
        'type' => error_type,
        'message' => error_message,
        'expression' => error_expression
      }
    end
  end
end
