import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { jerseyService, Jersey } from '../services/jerseyService';
import {
  ArrowLeftIcon,
  PlayIcon,
  PencilIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  EyeIcon,
  SwatchIcon,
} from '@heroicons/react/24/outline';

export const JerseyDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [jersey, setJersey] = useState<Jersey | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [parsing, setParsing] = useState(false);

  useEffect(() => {
    if (id) {
      loadJersey(parseInt(id));
    }
  }, [id]);

  const loadJersey = async (jerseyId: number) => {
    try {
      setLoading(true);
      const response = await jerseyService.getJersey(jerseyId);
      if (response.success) {
        setJersey(response.data);
      } else {
        setError(response.error || 'Failed to load jersey');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load jersey');
    } finally {
      setLoading(false);
    }
  };

  const handleParseJersey = async () => {
    if (!jersey) return;

    try {
      setParsing(true);
      const response = await jerseyService.parseJersey(jersey.id);
      if (response.success) {
        await loadJersey(jersey.id); // Reload to show updated data
      } else {
        setError(response.error || 'Failed to parse jersey');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to parse jersey');
    } finally {
      setParsing(false);
    }
  };

  const getStatusIcon = () => {
    if (!jersey) return null;
    if (jersey.parsing || parsing) return <ClockIcon className="h-5 w-5 text-yellow-500" />;
    if (jersey.parsed && jersey.error_count === 0) return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
    if (jersey.error_count && jersey.error_count > 0) return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
    return <ClockIcon className="h-5 w-5 text-gray-400" />;
  };

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
        <p className="mt-2 text-sm text-gray-500">Loading jersey...</p>
      </div>
    );
  }

  if (error || !jersey) {
    return (
      <div className="text-center py-12">
        <ExclamationTriangleIcon className="h-12 w-12 text-red-400 mx-auto" />
        <p className="mt-2 text-sm text-red-600">{error || 'Jersey not found'}</p>
        <Link
          to="/jerseys"
          className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-600 bg-primary-100 hover:bg-primary-200"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-2" />
          Back to Jerseys
        </Link>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Link
              to="/jerseys"
              className="mr-4 p-2 rounded-md text-gray-400 hover:text-gray-600"
            >
              <ArrowLeftIcon className="h-5 w-5" />
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{jersey.name}</h1>
              <p className="text-sm text-gray-500">
                Jersey ID: {jersey.id} • Number: #{jersey.number || 'N/A'}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={handleParseJersey}
              disabled={parsing || jersey.parsing}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"
            >
              <PlayIcon className="h-4 w-4 mr-2" />
              {parsing || jersey.parsing ? 'Parsing...' : 'Parse Jersey'}
            </button>
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              <PencilIcon className="h-4 w-4 mr-2" />
              Edit
            </button>
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50">
              <TrashIcon className="h-4 w-4 mr-2" />
              Delete
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Jersey Information */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Jersey Information</h3>
            </div>
            <div className="px-6 py-4">
              <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Name</dt>
                  <dd className="mt-1 text-sm text-gray-900">{jersey.name}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Status</dt>
                  <dd className="mt-1 flex items-center">
                    {getStatusIcon()}
                    <span className={`ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${jerseyService.getStatusBadgeColor(jersey)}`}>
                      {jerseyService.getStatusText(jersey)}
                    </span>
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Description</dt>
                  <dd className="mt-1 text-sm text-gray-900">{jersey.description || 'No description'}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Number</dt>
                  <dd className="mt-1 text-sm text-gray-900">#{jersey.number || 'N/A'}</dd>
                </div>
                <div className="sm:col-span-2">
                  <dt className="text-sm font-medium text-gray-500">String Value</dt>
                  <dd className="mt-1 text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded">
                    {jersey.string_value}
                  </dd>
                </div>
                {jersey.generated_string && (
                  <div className="sm:col-span-2">
                    <dt className="text-sm font-medium text-gray-500">Generated String</dt>
                    <dd className="mt-1 text-sm text-gray-900 font-mono bg-green-50 p-2 rounded">
                      {jersey.generated_string}
                    </dd>
                  </div>
                )}
                <div>
                  <dt className="text-sm font-medium text-gray-500">Created</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {new Date(jersey.created_at).toLocaleDateString()}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {new Date(jersey.updated_at).toLocaleDateString()}
                  </dd>
                </div>
              </dl>
            </div>
          </div>

          {/* Jersey Elements */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">
                Jersey Elements ({jersey.jersey_elements?.length || 0})
              </h3>
            </div>
            <div className="px-6 py-4">
              {jersey.jersey_elements && jersey.jersey_elements.length > 0 ? (
                <div className="space-y-4">
                  {jersey.jersey_elements.map((element, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <SwatchIcon className="h-5 w-5 text-gray-400 mr-2" />
                          <div>
                            <h4 className="text-sm font-medium text-gray-900">
                              {element.element_type?.name || 'Unknown Element'}
                            </h4>
                            <p className="text-sm text-gray-500">
                              Order: {element.order}
                            </p>
                          </div>
                        </div>
                        {element.colour && (
                          <div className="flex items-center">
                            <div
                              className="w-6 h-6 rounded border border-gray-300 mr-2"
                              style={{ backgroundColor: element.colour.hexadecimal }}
                            ></div>
                            <span className="text-sm text-gray-900">
                              {element.colour.name}
                            </span>
                          </div>
                        )}
                      </div>

                      {element.jersey_element_patterns && element.jersey_element_patterns.length > 0 && (
                        <div className="mt-3 pl-7">
                          <h5 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">
                            Patterns
                          </h5>
                          {element.jersey_element_patterns.map((jep: any, patternIndex: number) => (
                            <div key={patternIndex} className="mb-2">
                              <p className="text-sm text-gray-700">
                                {jep.pattern?.description || 'Unknown Pattern'}
                              </p>
                              {jep.jersey_pattern_colours && jep.jersey_pattern_colours.length > 0 && (
                                <div className="flex items-center mt-1">
                                  <span className="text-xs text-gray-500 mr-2">Colors:</span>
                                  {jep.jersey_pattern_colours.map((jpc: any, colorIndex: number) => (
                                    <div key={colorIndex} className="flex items-center mr-3">
                                      <div
                                        className="w-4 h-4 rounded border border-gray-300 mr-1"
                                        style={{ backgroundColor: jpc.colour?.hexadecimal }}
                                      ></div>
                                      <span className="text-xs text-gray-600">
                                        {jpc.colour?.name}
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">
                  No elements found. Try parsing the jersey to generate elements.
                </p>
              )}
            </div>
          </div>

          {/* Errors */}
          {jersey.errors && jersey.errors.length > 0 && (
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900 text-red-600">
                  Parse Errors ({jersey.errors.length})
                </h3>
              </div>
              <div className="px-6 py-4">
                <div className="space-y-3">
                  {jersey.errors.map((error, index) => (
                    <div key={index} className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <div className="flex">
                        <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                        <div className="ml-3">
                          <h4 className="text-sm font-medium text-red-800">
                            {error.type || 'Parse Error'}
                          </h4>
                          <p className="text-sm text-red-700 mt-1">
                            {error.message}
                          </p>
                          {error.expression && (
                            <p className="text-xs text-red-600 mt-1 font-mono">
                              Expression: {error.expression}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Jersey Preview */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Jersey Preview</h3>
            </div>
            <div className="px-6 py-4">
              <div className="jersey-preview bg-gray-50 rounded-lg p-8 text-center">
                <div className="w-24 h-32 mx-auto bg-gradient-to-b from-gray-300 to-gray-400 rounded-lg flex items-center justify-center">
                  <SwatchIcon className="h-12 w-12 text-gray-600" />
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  SVG preview will be generated here
                </p>
                <button className="mt-2 inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                  <EyeIcon className="h-4 w-4 mr-2" />
                  Generate Preview
                </button>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Quick Stats</h3>
            </div>
            <div className="px-6 py-4">
              <dl className="space-y-3">
                <div className="flex justify-between">
                  <dt className="text-sm text-gray-500">Elements</dt>
                  <dd className="text-sm font-medium text-gray-900">
                    {jersey.element_count || jersey.jersey_elements?.length || 0}
                  </dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-sm text-gray-500">Errors</dt>
                  <dd className="text-sm font-medium text-gray-900">
                    {jersey.error_count || jersey.errors?.length || 0}
                  </dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-sm text-gray-500">Base Color</dt>
                  <dd className="text-sm font-medium text-gray-900">
                    {jersey.has_base_color ? 'Yes' : 'No'}
                  </dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-sm text-gray-500">Parsed</dt>
                  <dd className="text-sm font-medium text-gray-900">
                    {jersey.parsed ? 'Yes' : 'No'}
                  </dd>
                </div>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
