<?php

namespace DBLS\Bundle\JerseyColoursBundle\Services;

use Symfony\Component\Process\Exception\ProcessFailedException;
use Symfony\Component\Process\Process;

/**
 * Description of SvgUtils
 *
 * <AUTHOR>
 */
class SvgUtilsService extends ContainerAware
{


    /**
    * Constructor
    */
    public function __construct() 
    {
        
    }

    /**
     * Convert SVG $inFile to JPEG at $outFile
     * 
     * $params can have the keys:
     *    - compression_quality
     *    - width
     *    - height
     * 
     * @param string $inFile
     * @param string $outFile
     * @param array $params
     */
    public function toJpeg($inFile, $outFile, $params = array()) 
    {
        $width = 119;
        $height = 142;

        if ( !empty($params['width']) )
            $width = $params['width'];

        if ( !empty($params['height']) )
            $height = $params['height'];

        $image = new \Imagick();
        $image->setformat('svg');
        $image->readImageBlob(file_get_contents($inFile));
        
        // jpeg
        $image->setImageFormat("jpg");
        $image->resizeImage($width, $height, \Imagick::FILTER_LANCZOS,1);
        $image->setImageCompression(\Imagick::COMPRESSION_LOSSLESSJPEG); 
        $image->setImageCompressionQuality(100);
        $image->stripImage();
        
        $image->writeImage($outFile);
        $image->clear();
        $image->destroy();
    }


    /**
     * 
     * @param  array $silks
     */
    public function outputFile($race, $silks)
    {
        $pdfGenerator = $this->container->get('knp_snappy.image');
        $templating = $this->container->get('templating');

        $generateSilks = array();
        foreach ( $silks as $silk ) {
            $generateSilks = array(
                'silk'  => $silk,
                'image' => $this->_tmpJpeg($silk)
            );
        }

        $htmlOutput = $templating->render(
                            'JerseyColoursBundle:Pdf:silks.html.twig', 
                            array('silks' => $generateSilks)
                        );

        $cleanRaceSlug = $race->getCleanFolder();
        $pdfFile = $race->getPdfName();

        $options = array( 'width'  => '2000' );

        $finalFile = $folder . $pdfFile;
        $pdfGenerator->generateFromHtml( $htmlOutput, $finalFile, $options, true );

        $i = new \Imagick($finalFile);
        $i->transformimagecolorspace(\Imagick::COLORSPACE_CMYK);
        $i->setFormat('JPG');
        $i->writeImage($finalFile);
    }
}

