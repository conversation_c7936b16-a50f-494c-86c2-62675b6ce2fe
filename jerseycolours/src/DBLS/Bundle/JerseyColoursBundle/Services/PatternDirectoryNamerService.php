<?php

namespace DBLS\Bundle\JerseyColoursBundle\Services;

use Vich\UploaderBundle\Naming\DirectoryNamerInterface;
use Vich\UploaderBundle\Mapping\PropertyMapping;
use DBLS\Bundle\JerseyColoursBundle\Entity\Pattern;
use <PERSON><PERSON>\DoctrineSluggableBundle\Listener\SluggableListener;
use Doctrine\ORM\EntityManager;
use SamJ\DoctrineSluggableBundle\Slugger;

/**
 * This class is intended to customize how directory names are created.
 * 
 * <AUTHOR>
 */
class PatternDirectoryNamerService implements DirectoryNamerInterface {

    /**
     * Holds an instance of database connection
     * @var EntityManager
     */
    private $em = null;

    public function __construct(EntityManager $em) 
    {
        $this->em = $em;
    }

    /**
     * Creates a directory name for the file being uploaded.
     *
     * @param object          $object  The object the upload is attached to.
     * @param Propertymapping $mapping The mapping to use to manipulate the given object.
     *
     * @return string The directory name.
     */
    public function directoryName($object, PropertyMapping $mapping) 
    {
        $directory = '';

        if ($object instanceof Pattern) {

            $slugger = new Slugger('-', '-');

            $repository = $this->em->getRepository('JerseyColoursBundle:Pattern');
            
            $sluggerListener = new SluggableListener($slugger);
            $sluggerListener->generateUniqueSlug($object, $repository);

            $directory = $object->getSlug();
        }

        return $directory;
    }

}
