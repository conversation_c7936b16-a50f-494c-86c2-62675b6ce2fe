import { apiService } from './api';

export interface Jersey {
  id: number;
  name: string;
  description?: string;
  string_value: string;
  status: boolean;
  parsed: boolean;
  parsing: boolean;
  parsing_time?: string;
  external_jersey_id: string;
  number?: number;
  race?: any;
  trainer?: any;
  jersey_elements: any[];
  errors: any[];
  created_at: string;
  updated_at: string;
  generated_string?: string;
  folder_path?: string;
  element_count?: number;
  error_count?: number;
  has_base_color?: boolean;
}

export interface JerseySearchParams {
  page?: number;
  per_page?: number;
  search?: string;
  parsed?: boolean;
}

export interface JerseyForm {
  name: string;
  description?: string;
  string_value: string;
  status: boolean;
  number?: number;
  race_id?: number;
  trainer_id?: number;
}

class JerseyService {
  async getJerseys(params: JerseySearchParams = {}): Promise<any> {
    const queryString = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryString.append(key, value.toString());
      }
    });

    const url = queryString.toString() ? 
      `${apiService.endpoints.jerseys}?${queryString.toString()}` : 
      apiService.endpoints.jerseys;
      
    return await apiService.get(url);
  }

  async getJersey(id: number): Promise<any> {
    return await apiService.get(`${apiService.endpoints.jerseys}/${id}`);
  }

  async createJersey(data: JerseyForm): Promise<any> {
    return await apiService.post(apiService.endpoints.jerseys, { jersey: data });
  }

  async updateJersey(id: number, data: Partial<JerseyForm>): Promise<any> {
    return await apiService.put(`${apiService.endpoints.jerseys}/${id}`, { jersey: data });
  }

  async deleteJersey(id: number): Promise<any> {
    return await apiService.delete(`${apiService.endpoints.jerseys}/${id}`);
  }

  async parseJersey(id: number): Promise<any> {
    return await apiService.post(`${apiService.endpoints.jerseys}/${id}/parse`);
  }

  async bulkParseJerseys(jerseyIds: number[]): Promise<any> {
    return await apiService.post(`${apiService.endpoints.jerseys}/bulk_parse`, { jersey_ids: jerseyIds });
  }

  async generateSvg(id: number, view: 'front' | 'side' = 'front'): Promise<any> {
    return await apiService.get(`${apiService.endpoints.jerseys}/${id}/svg/${view}`);
  }

  // Helper method to get jersey status badge color
  getStatusBadgeColor(jersey: Jersey): string {
    if (jersey.parsing) return 'bg-yellow-100 text-yellow-800';
    if (jersey.parsed && jersey.error_count === 0) return 'bg-green-100 text-green-800';
    if (jersey.error_count && jersey.error_count > 0) return 'bg-red-100 text-red-800';
    if (!jersey.parsed) return 'bg-gray-100 text-gray-800';
    return 'bg-blue-100 text-blue-800';
  }

  // Helper method to get jersey status text
  getStatusText(jersey: Jersey): string {
    if (jersey.parsing) return 'Parsing...';
    if (jersey.parsed && jersey.error_count === 0) return 'Parsed Successfully';
    if (jersey.error_count && jersey.error_count > 0) return `${jersey.error_count} Error${jersey.error_count > 1 ? 's' : ''}`;
    if (!jersey.parsed) return 'Not Parsed';
    return 'Unknown';
  }

  // Helper method to format jersey string for display
  formatJerseyString(jersey: Jersey): string {
    if (jersey.generated_string) {
      return jersey.generated_string;
    }
    return jersey.string_value || 'No description';
  }

  // Helper method to get element summary
  getElementSummary(jersey: Jersey): string {
    if (!jersey.jersey_elements || jersey.jersey_elements.length === 0) {
      return 'No elements';
    }
    
    const elementCount = jersey.jersey_elements.length;
    const baseElement = jersey.jersey_elements.find(el => el.element_type?.name === 'base');
    const baseColor = baseElement?.colour?.name || 'No base color';
    
    return `${elementCount} element${elementCount > 1 ? 's' : ''} • ${baseColor}`;
  }
}

export const jerseyService = new JerseyService();
export default jerseyService;
