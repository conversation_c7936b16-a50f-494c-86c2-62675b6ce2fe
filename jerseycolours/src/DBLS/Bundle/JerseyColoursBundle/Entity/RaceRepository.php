<?php

namespace DBLS\Bundle\JerseyColoursBundle\Entity;

use Doctrine\ORM\EntityRepository;

/**
 * RaceRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class RaceRepository extends EntityRepository
{

    /**
     * 
     * @return [type] [description]
     */
    public function findAllPending($id)
    {
        $qb = $this->createQueryBuilder("r");
        
        $qb->innerJoin('JerseyColoursBundle:Jersey', 'je', 'WITH', 'je.race = r.id')
            ->where(
                $qb->expr()->andX(
                        $qb->expr()->eq('je.status', ':unapproved'),
                        $qb->expr()->eq('je.parsed', ':parsed'),
                        $qb->expr()->eq('r.id', ':id')
                )
            )
            ->orderBy('r.racetime','ASC')
            ->distinct()
            ->setParameter('unapproved', 0)
            ->setParameter('parsed', 1)
            ->setParameter('id', $id);

        $q = $qb->getQuery();
        
        try {
            return $q->getOneOrNullResult();
        } catch (NoResultException $ex) {
            return null;
        }
        
    }
}
