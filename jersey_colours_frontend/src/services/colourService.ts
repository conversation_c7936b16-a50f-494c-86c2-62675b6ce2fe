import { apiService } from './api';

export interface Colour {
  id: number;
  name: string;
  cmyk: string;
  hexadecimal: string;
  created_at: string;
  updated_at: string;
  usage_count?: number;
  rgb?: {
    r: number;
    g: number;
    b: number;
  };
}

export interface ColourSearchParams {
  page?: number;
  per_page?: number;
  search?: string;
}

export interface ColourForm {
  name: string;
  cmyk: string;
  hexadecimal: string;
}

class ColourService {
  async getColours(params: ColourSearchParams = {}): Promise<any> {
    const queryString = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryString.append(key, value.toString());
      }
    });

    const url = queryString.toString() ? 
      `${apiService.endpoints.colours}?${queryString.toString()}` : 
      apiService.endpoints.colours;
      
    return await apiService.get(url);
  }

  async getColour(id: number): Promise<any> {
    return await apiService.get(`${apiService.endpoints.colours}/${id}`);
  }

  async createColour(data: ColourForm): Promise<any> {
    return await apiService.post(apiService.endpoints.colours, { colour: data });
  }

  async updateColour(id: number, data: Partial<ColourForm>): Promise<any> {
    return await apiService.put(`${apiService.endpoints.colours}/${id}`, { colour: data });
  }

  async deleteColour(id: number): Promise<any> {
    return await apiService.delete(`${apiService.endpoints.colours}/${id}`);
  }

  // Helper method to convert hex to RGB
  hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }

  // Helper method to convert RGB to hex
  rgbToHex(r: number, g: number, b: number): string {
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
  }

  // Helper method to validate hex color
  isValidHex(hex: string): boolean {
    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(hex);
  }

  // Helper method to validate CMYK
  isValidCMYK(cmyk: string): boolean {
    const parts = cmyk.split(',').map(p => p.trim());
    if (parts.length !== 4) return false;
    
    return parts.every(part => {
      const num = parseFloat(part);
      return !isNaN(num) && num >= 0 && num <= 100;
    });
  }

  // Helper method to get color brightness (for text contrast)
  getColorBrightness(hex: string): number {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return 0;
    
    // Calculate relative luminance
    return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
  }

  // Helper method to determine if text should be light or dark
  shouldUseLightText(hex: string): boolean {
    return this.getColorBrightness(hex) < 128;
  }

  // Helper method to format CMYK display
  formatCMYK(cmyk: string): string {
    const parts = cmyk.split(',').map(p => p.trim());
    if (parts.length === 4) {
      return `C:${parts[0]} M:${parts[1]} Y:${parts[2]} K:${parts[3]}`;
    }
    return cmyk;
  }
}

export const colourService = new ColourService();
export default colourService;
