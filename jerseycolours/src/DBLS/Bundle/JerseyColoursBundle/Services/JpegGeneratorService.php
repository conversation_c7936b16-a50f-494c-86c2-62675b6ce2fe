<?php

namespace DBLS\Bundle\JerseyColoursBundle\Services;

use Doctrine\ORM\EntityManager;
use Symfony\Component\Process\Exception\ProcessFailedException;
use Symfony\Component\Process\Process;
use Symfony\Component\DependencyInjection\ContainerAware;

class JpegGeneratorService extends ContainerAware
{
    /**
     * Holds an instance of database connection
     * @var EntityManager
     */
    private $em = null;


    public function __construct(EntityManager $em) 
    {
        $this->em = $em;
    }

    /**
     * 
     * @param  array $silks
     */
    public function outputFile($race, $silks, $finalFile = null)
    {
        $imageGenerator = $this->container->get('knp_snappy.image');
        $templating = $this->container->get('templating');

        $generateSilks = array();
        foreach ( $silks as $silk ) {
            $generateSilks = array(
                'silk'  => $silk,
            );
        }

        $fontFile = realpath('bundles/jerseycolours/fonts/Helvetica-Condensed.ttf');

        $htmlOutput = $templating->render(
                            'JerseyColoursBundle:Pdf:silks.html.twig', 
                            array(
                                'silks'     => $silks,
                                'font_file' => $fontFile
                            )
                        );

        $options = array( 'width'  => '2000' );

        if ( empty($finalFile) )
            $finalFile = $race->getPdfName();

        $imageGenerator->generateFromHtml( $htmlOutput, $finalFile, $options, true );

    
        $command = "convert -colorspace sRGB %s -colorspace CMYK %s";
        $toExecute = sprintf($command, $finalFile, $finalFile);

        $process = new Process($toExecute);
        $process->run();

        // Generate PNG
        $pngFile = str_replace('.jpg', '.png', $finalFile);
        $command = "convert -resize 160x203 %s %s";
        $toExecute = sprintf($command, $finalFile, $pngFile);

        $process = new Process($toExecute);
        $process->run();
    }

    /**
     * 
     * @param  Jersey $silk
     * @return string
     */
    protected function _tmpJpeg($silk)
    {
        $pathSideView = $silk->getSideViewImage(false);
        $approvedJpeg = md5(str_replace('/', '_', $silk->getApprovedJpeg())) . '.jpg';
        $outFile = sys_get_temp_dir() . '/' . $approvedJpeg;

        $params = array(
            'width'     => 300,
            'height'    => 300
        );

        $this->toJpeg($pathSideView, $outFile, $params);

        return $outFile;
    }


    public function toJpeg($inFile, $outFile, $params = array()) 
    {
        $width = 119;
        $height = 142;

        if ( !empty($params['width']) )
            $width = $params['width'];

        if ( !empty($params['height']) )
            $height = $params['height'];

        $image = new \Imagick();
        $image->setFormat('svg');
        $image->readImageBlob(file_get_contents($inFile));
        
        // jpeg
        $image->setImageFormat("jpg");
        $image->resizeImage($width, $height, \Imagick::FILTER_LANCZOS,1);
        $image->setImageCompression(\Imagick::COMPRESSION_LOSSLESSJPEG); 
        $image->setImageCompressionQuality(100);
        $image->stripImage();
        
        $image->writeImage($outFile);
        $image->clear();
        $image->destroy();
    }

    public function toPng($inFile, $outFile, $params = array()) 
    {
        $width = 152;
        $height = 203;

        if ( !empty($params['width']) )
            $width = $params['width'];

        if ( !empty($params['height']) )
            $height = $params['height'];

        $image = new \Imagick();
        $image->setFormat('svg');
        $image->setBackgroundColor(new \ImagickPixel('transparent'));
        $image->readImageBlob(file_get_contents($inFile));
        
        // png
        $image->setImageFormat("png");
        $image->resizeImage($width, $height, \Imagick::FILTER_LANCZOS, 1);
        $image->setImageCompressionQuality(100);
        
        $image->writeImage($outFile);
        $image->clear();
        $image->destroy();
    }
}