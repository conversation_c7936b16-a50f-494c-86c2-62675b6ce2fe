<?php

namespace DBLS\Bundle\JerseyColoursBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Doctrine\Common\Collections\ArrayCollection;

/**
 * JerseyElement
 *
 * @ORM\Table()
 * @ORM\Entity(repositoryClass="DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElementPatternRepository")
 */
class JerseyElementPattern
{

    /**
     * @var integer
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="Pattern", inversedBy="jersey_element_pattern_associations")
     * @ORM\JoinColumn(name="pattern_id", referencedColumnName="id")
     */
    private $pattern;

    /**
     * @ORM\ManyToOne(targetEntity="JerseyElement", inversedBy="jersey_patterns")
     * @ORM\JoinColumn(name="jersey_element_id", referencedColumnName="id", onDelete="CASCADE")
     */
    private $jersey_element;

    /**
     * @ORM\OneToMany(targetEntity="JerseyPatternColour", mappedBy="jersey_element_pattern")
     * @ORM\OrderBy({"order" = "ASC"})
    */
    protected $pattern_colours;

    public function __construct() {
        $this->pattern_colours = new ArrayCollection();
    }
    
    /**
     * Get id
     *
     * @return integer 
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set pattern
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Pattern $pattern
     * @return JerseyElementPattern
     */
    public function setPattern(\DBLS\Bundle\JerseyColoursBundle\Entity\Pattern $pattern = null)
    {
        $this->pattern = $pattern;

        return $this;
    }

    /**
     * Get pattern
     *
     * @return \DBLS\Bundle\JerseyColoursBundle\Entity\Pattern 
     */
    public function getPattern()
    {
        return $this->pattern;
    }

    /**
     * Set jersey_element
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElement $jerseyElement
     * @return JerseyElementPattern
     */
    public function setJerseyElement(\DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElement $jerseyElement = null)
    {
        $this->jersey_element = $jerseyElement;

        return $this;
    }

    /**
     * Get jersey_element
     *
     * @return \DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElement 
     */
    public function getJerseyElement()
    {
        return $this->jersey_element;
    }

    /**
     * Add pattern_colours
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\JerseyPatternColour $patternColours
     * @return JerseyElementPattern
     */
    public function addPatternColour(\DBLS\Bundle\JerseyColoursBundle\Entity\JerseyPatternColour $patternColours)
    {
        $this->pattern_colours[] = $patternColours;

        return $this;
    }

    /**
     * Remove pattern_colours
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\JerseyPatternColour $patternColours
     */
    public function removePatternColour(\DBLS\Bundle\JerseyColoursBundle\Entity\JerseyPatternColour $patternColours)
    {
        $this->pattern_colours->removeElement($patternColours);
    }

    /**
     * Get pattern_colours
     *
     * @return \Doctrine\Common\Collections\Collection 
     */
    public function getPatternColours()
    {
        return $this->pattern_colours;
    }
}
