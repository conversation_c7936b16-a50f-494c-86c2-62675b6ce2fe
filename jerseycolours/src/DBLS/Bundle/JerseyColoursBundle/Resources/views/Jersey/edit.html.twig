{% import '::macros.html.twig' as macros %}

{{ form_start(form) }}

    <div class="row">
        <div class="col-xs-12 col-sm-4 col-md-3">
            {{ macros.jersey_image(jersey) }}
        </div>
        <div class="col-xs-12 col-sm-8 col-md-9">

            <h4 class="jersey-name">{{ form.vars.data.name }}</h4>

            <p class="well well-sm">
                <strong>Original String</strong>: {{ form.vars.data.stringValue|title }}
            </p>

            <div class="well well-sm">
                <strong>Edited String </strong>
                <div class="row">
                    <div class="col-xs-7 col-sm-11 col-md-11">
                        {{ form_widget(form.description) }}
                    </div>
                    <div class="col-xs-5 col-sm-1 col-md-1">
                        <button type="button" title="Refresh Silk" 
                          id="refresh-jersey" data-id="{{ jersey.id }}"
                          class="btn btn-success pull-left">
                          <i class="fa fa-refresh"></i>
                        </button>

                        {% set error_class = 'hide' %}
                        {% if errors %}
                          {% set error_class = '' %}
                        {% endif %}

                        <!-- Trigger the modal with a button -->
                        <button type="button" title="View Errors" class="btn btn-danger error-btn {{ error_class }}" onclick="javascript:showErrorDialog();">
                            <i class="fa fa-warning"></i>
                        </button>

                        <!-- Trigger the modal with a button -->
                        <!--<button type="button" title="View Errors" class="btn btn-danger error-btn {{ error_class }}" data-toggle="modal" data-target="#modalError">
                            <i class="fa fa-warning"></i>
                        </button>-->


                    </div>
                </div>
            </div>

        </div>
    </div>

    {% if similar is defined %}
        <div class="alert bold alert-info not-remove">
            There is a similar silk with the name <b>{{ similar.name }}</b>.
            <a href="javascript:;" class="btn btn-default btn-primary copy-similar" data-from="{{ similar.id }}" data-to="{{ jersey.id }}">
                <i class="fa fa-copy"></i>
                <b>Click here</b>
            </a> 
            if you want to copy it.
        </div>
    {% endif %}

    <div class="elements-container">
        <div class="row">
        	{{ include('JerseyColoursBundle:Jersey:elements.html.twig', {'form':form}) }}
        </div>
    </div>

    <div class="row margin-top-10">
        <div class="col-xs-12 text-center">
            <button class="btn-primary btn btn save-jersey" type="submit">Regenerate</button>
            <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
        </div>
    </div>

    {% do form.elements.setRendered %}

{{ form_end(form) }}


<!-- Modal -->
<div id="modalError" class="modal fade" role="dialog">
  <div class="modal-dialog">

    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-body">                        
          <div class="error-danger">         
                <h4 class="alert-heading">Error!</h4>
                <div id="error-container">
                  {{ include('JerseyColoursBundle:Jersey:errors.html.twig', {'errors':errors}) }}
                </div>
          </div>
      </div>
      <div class="modal-footer">
        <button onclick="javascript: closeThis(this);" type="button" class="btn btn-default btn-close">Close</button>
      </div>
    </div>

  </div>
</div>

<script>
    
    // scripts for handing the modal
    
    function showErrorDialog() {
        var modal = $('#modalError').clone();
        $(modal).modal({backdrop: false});
    }
    
    function closeThis(element) {
        $(element).closest("#modalError").modal('hide').remove();
    }
    
</script>