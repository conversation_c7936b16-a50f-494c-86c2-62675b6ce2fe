/*
SAMARA
*/
/*----------------------------
   ** Reset.**
----------------------------*/
html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, font, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, center, u, b, i {
	margin: 0;
	padding: 0;
	border: 0;
	outline: 0;
	font-weight: normal;
	font-style: normal;
	font-size: 100%;
	font-family: inherit;
	vertical-align: baseline;
	text-decoration: none;
	-webkit-appearance: none;
	-webkit-border-radius: 0;
}
html, body {
	margin: 0;
	padding: 0;
	height: 100%;
}
:focus {
	outline: 0
}
ol, ul {
	list-style: none
}
input, textarea {
	margin: 0;
	padding: 0
}
input[type=text] {
	outline: none;
}
input[type=text]::-ms-clear {
	display: none;
}
input[type=submit] {
	-webkit-appearance: none;
}

/*----------------------------
   ** Custom Style **
----------------------------*/
*, *:after, *::before {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}
html, body {
	font-family: 'Montserrat Alternates', sans-serif;
	font-weight: normal;
	font-size: 16px;
	word-spacing: normal;
	color: #787878;
	height: 100%;
    -webkit-text-size-adjust: 100%;
	-ms-text-size-adjust: 100%;

}
body {
    background-color: #F8F8F8;
}
img {
	max-width: 100%;
}
a {
	color: #fff;
	transition: all 0.3s;
	-webkit-transition: all 0.3s;
	-moz-transition: all 0.3s;
	-o-transition: all 0.3s;
}
a:hover {
	color: #ccc;
	text-decoration: none;
}
.preload * {
	-webkit-transition: none !important;
	-moz-transition: none !important;
	-ms-transition: none !important;
	-o-transition: none !important;
}

.content-inner {
	position: relative;
	height: auto !important;
	min-height: 99%;
	z-index: 100;
	padding-bottom: 60px;
}
.content {
	margin-top: 80px;
}
.clearfix{
clear:both;
float:none;
}
/*----------------------------
    ** 404 text **
----------------------------*/

.full-width{
background-image: linear-gradient(to bottom, #cd3e3d 0%, #ab2d2c 100%);
width:100%;
min-height:500px;
-moz-box-shadow: 0px 1px 1px 1px rgba(0,0,0,0.4);
-webkit-box-shadow: 0px 1px 1px 1px rgba(0,0,0,0.4);
box-shadow:0px 1px 1px 1px rgba(0,0,0,0.4);
-ms-filter: "progid:DXImageTransform.Microsoft.Shadow(Strength=1, Direction=90, Color='#000000')";
filter: progid:DXImageTransform.Microsoft.Shadow(Strength=1, Direction=90, Color='#000000');
}
.Out {
	opacity: 0;
	transform: scaleY(0);
}
.In {
	opacity: 1;
	transform: scaleY(1);
}
.main {
	position: relative;
}
.desc {
	position: relative;
	z-index: 100;
	border-top:1px dotted #cccccc;
	margin-top:40px;
}

.text404 {
	width: auto;
	margin: 0px auto;
	cursor: default;
	animation: fadeIn 0.6s ease-in-out 2s;
	animation-fill-mode: forwards;
}
.text404 > span {
	display: block;
	-webkit-backface-visibility: hidden;
}
.text404-cut {
	width: 100%;
	height: 60px;
	overflow: hidden;
	-webkit-transition: all 0.6s ease-in-out;
	-moz-transition: all 0.6s ease-in-out;
	-o-transition: all 0.6s ease-in-out;
	-ms-transition: all 0.6s ease-in-out;
	transition: all 0.6s ease-in-out;
}

.text404-cut span {
	display: block;
	line-height: 120px;
	color: rgba(255,255,255,0.9);
	font-size: 120px;
	text-transform: uppercase;
	margin-top: 6px;
	letter-spacing: -2px;
	font-family: 'Days One', cursive;
	text-shadow: 0px 2px 4px rgba(255,255,255,0.5);
}
.text404-cut:last-child span {
	margin-top: -54px;
}
.text404-mid {
	font-family: 'Days One', cursive;
	text-transform: uppercase;
	font-size: 40px;
	letter-spacing: 3px;
	line-height: 40px;
	text-indent: 20px;
	position: absolute;
	top: 50%;
	margin-top: -20px;
	width: auto;
	color: rgba(255,255,255,0.5);
	opacity: 0;
	-webkit-transform: scale(0.3);
	-moz-transform: scale(0.3);
	-o-transform: scale(0.3);
	-ms-transform: scale(0.3);
	transform: scale(0.3);
	-webkit-transition: all 0.6s ease-in-out 0s;
	-moz-transition: all 0.6s ease-in-out 0s;
	-o-transition: all 0.6s ease-in-out 0s;
	-ms-transition: all 0.6s ease-in-out 0s;
	transition: all 0.6s ease-in-out 0s;
}
.text404:hover .text404-cut:first-child {
	-webkit-transform: translateY(-40px);
	-moz-transform: translateY(-40px);
	-o-transform: translateY(-40px);
	-ms-transform: translateY(-40px);
	transform: translateY(-40px);
	opacity: 0;
}
.text404:hover .text404-cut:last-child {
	-webkit-transform: translateY(40px);
	-moz-transform: translateY(40px);
	-o-transform: translateY(40px);
	-ms-transform: translateY(40px);
	transform: translateY(40px);
	opacity: 0;
}
.text404:hover .text404-mid {
	-webkit-transition-delay: 0.6s;
	-moz-transition-delay: 0.6s;
	-o-transition-delay: 0.6s;
	-ms-transition-delay: 0.6s;
	transition-delay: 0.6s;
	opacity: 1;
	-webkit-transform: scale(1);
	-moz-transform: scale(1);
	-o-transform: scale(1);
	-ms-transform: scale(1);
	transform: scale(1);
}
.desc h4{
	font-family: 'Montserrat Alternates', sans-serif;
	font-size: 28px;
	line-height: 34px;
	color: #cccccc;
	padding-bottom: 10px;
	padding-top: 30px;
	max-width:600px;
}
.desc-back{
	font-family: 'Montserrat Alternates', sans-serif;
	font-size: 12px;
	color: #cccccc;
	padding-bottom: 10px;
	padding-top: 30px;
	max-width:600px;
}
.desc .desc-back a {
	text-decoration: none;
}

/* ------------------------------
     **Search Box**
--------------------------------*/

.search {
	position: relative;
	height: 40px;
	margin: 20px 0px;
	max-width:450px;
	}
#search input::-webkit-input-placeholder, #search input:-moz-placeholder, #search input::-moz-placeholder, #search input:-ms-input-placeholder {
	color: #999;
}
#search input[type="text"] {
	background-color: rgba(255,255,255,0.8);
	background-image: url("../images/search.png");
	background-position: 97% 9px;
	background-repeat: no-repeat;
	border: 0 none;
	border-radius: 20px;
	color: #000000;
	font-family: inherit;
	font-size: 16px;
	padding: 9px 45px 9px 15px;
	transition: all 0.7s ease 0s;
	width: 100%;
}
#search input[type="text"]:focus {
	background-color: rgba(255,255,255,1);
}
.search-submit {
	background: #fff;
/* IE needs this */
	-ms-filter: "progid: dximagetransform.microsoft.alpha(opacity=0)";
/* IE 8 */
	filter: alpha(opacity=0);
/* IE 5-7 */
	opacity: 0;
	color: transparent;
	border: none;
	outline: none;
}

/*----------------------------
        **Footer**
----------------------------*/

.footer {
	position: relative;
    bottom:0;
    left:0;
	padding: 30px 0px;
	margin: 0px;
	width: 100%;
	z-index: 101;
}
.footer a{
 color:#141414;
}
.custom-top{
    margin-top: 20px;
}
/*--------------------------------------------------
       **Social Icons Roll Over**
---------------------------------------------------*/

.social-icons {
	padding: 0!important;
	margin: 0!important;
	clear:both;
	list-style-type: none!important;
}
.social-icons li{
	float: left!important;
	margin-right: 6px!important;
	margin-bottom: 6px!important;
	padding: 0px!important;
	display: inline!important;
	width: auto!important;

}			
.social-icons li a {
	-moz-transition: all 0.3s ease 0s;
	display: block!important;
	float: left!important;
	height: 100%!important;
	margin: 0!important;
	padding: 0!important;
	width: 40px!important;
	height: 40px!important;
	text-indent: -9999px!important;
}
.social-icons li a:hover {
	background-position: 0 -40px!important;
}
.social-icons li a {
	background-color:transparent;
}
.social-icons li.facebook a {
	background: url(../images/icons/facebook.png) no-repeat 0 0;
}
.social-icons li.facebook a:hover {
	background-color: #3b5998;
}
.social-icons li.googleplus a {
	background: url(../images/icons/googleplus.png) no-repeat 0 0;
}
.social-icons li.googleplus a:hover {
	background-color: #d94a39;
}
.social-icons li.linkedin a {
	background: url(../images/icons/linkedin.png) no-repeat 0 0;
}
.social-icons li.linkedin a:hover {
	background-color: #71b2d0;
}
.social-icons li.pinterest a {
	background: url(../images/icons/pinterest.png) no-repeat 0 0;
}
.social-icons li.pinterest a:hover {
	background-color: #cb2027;
}
.social-icons li.rss a {
	background: url(../images/icons/rss.png) no-repeat 0 0;
}
.social-icons li.rss a:hover {
	background-color: #fe9900;
}
.social-icons li.skype a {
	background: url(../images/icons/skype.png) no-repeat 0 0;
}
.social-icons li.skype a:hover {
	background-color: #18b7f1;
}
.social-icons li.twitter a {
	background: url(../images/icons/twitter.png) no-repeat 0 0;
}
.social-icons li.twitter a:hover {
	background-color: #48c4d2;
}

/*------------------------------
      **Footer links Effect ** 
--------------------------------*/

.right{
float:right;
}
.footer nav a {
	position: relative;
	display: inline-block;
	margin: 10px;
	outline: none;
	text-decoration: none;
	text-transform: uppercase;
	font-size: 14px;
	float:right;
}

.footer nav a:hover,
.footer nav a:focus {
	outline: none;
}
.footer nav a {
	padding: 15px 0 5px 0px;
}

.footer nav a::after {
	position: absolute;
	top: 100%;
	left: 0;
	width: 100%;
	height: 2px;
	background: rgba(0,0,0,0.2);
	content: '';
	opacity: 0;
	-webkit-transition: opacity 0.3s, -webkit-transform 0.3s;
	-moz-transition: opacity 0.3s, -moz-transform 0.3s;
	transition: opacity 0.3s, transform 0.3s;
	-webkit-transform: translateY(10px);
	-moz-transform: translateY(10px);
	transform: translateY(10px);
}

.footer nav a:hover::after,
.footer nav a:focus::after {
	opacity: 1;
	-webkit-transform: translateY(0px);
	-moz-transform: translateY(0px);
	transform: translateY(0px);
}

.copyright {
	clear: both;
	font-size: 14px;
	padding: 10px 0px 0px 0px;
}
/* Mobile Portrait Size to Mobile Landscape Size (devices and browsers) */
@media (max-width:480px) {
.content {
	margin-top: 60px;
}
.desc h4 {
	font-size: 18px;
	line-height: 26px;
	letter-spacing:0px;
}
.text404-cut {
	height: 40px;
}
.text404-cut span {
	line-height: 80px;
	font-size: 80px;
}
.text404-cut:last-child span {
	margin-top: -34px;
}
.text404-mid {
	font-size: 30px;
	letter-spacing: 1px;
	line-height: 36px;
	text-indent: 20px;
	margin-top: -15px;
}
.footer nav a {
float:left;
font-size:12px;
}
.copyright {
	font-size: 12px;
	padding: 0px;
}
}
@media (max-width:960px) {
.clear{
clear:both;
float:none;
width:100%;
}
.right{
float:none;
clear:both;
}
.footer nav a{
float:none;
}
.footer nav a:first-child{
margin:10px 0px;
}
}