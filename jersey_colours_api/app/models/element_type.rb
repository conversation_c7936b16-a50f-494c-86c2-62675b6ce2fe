class ElementType < ApplicationRecord
  has_many :patterns, dependent: :destroy
  has_many :jersey_elements, dependent: :destroy

  validates :name, presence: true, uniqueness: { case_sensitive: false }

  # Constants matching PHP application
  BASE = 1
  SHIRT = 2
  CAP = 3
  PEAK = 4
  SLEEVES = 5
  COLLAR = 6
  CUFFS = 7
  TASSEL = 8
  POMPOM = 9
  ARMBANDS = 10
  SEAMS = 11
  SASH = 12
  EPAULETTES = 13
  CROSSED_SASHES = 14

  scope :by_name, ->(name) { where("LOWER(name) = ?", name.downcase) }

  def to_s
    name
  end
end
