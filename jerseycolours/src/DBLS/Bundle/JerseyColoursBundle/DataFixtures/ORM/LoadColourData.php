<?php

namespace DBLS\Bundle\JerseyColoursBundle\DataFixtures\ORM;

use Doctrine\Common\DataFixtures\AbstractFixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Common\Persistence\ObjectManager;
use DBLS\Bundle\JerseyColoursBundle\Entity\Colour;

class LoadColourData extends AbstractFixture implements OrderedFixtureInterface
{

    /**
     * {@inheritDoc}
     */
    public function load(ObjectManager $manager)
    {

        /*
         * "white","0,0,0,0","#FFFFFF"
         * "yellow","0,2,255,0","#FFFC00"
         * "blue","255,255,0,0","#0000FF"
         * "black","0,0,0,255","#000000"
         * "red","0,255,255,0","#FF0000"
         * "limegreen","154,0,154,49","#32CD32"
         */

        $colourPurple = new Colour();
        $colourPurple->setName('purple');
        $colourPurple->setCMYK("0,0,0,0");
        $colourPurple->setHexadecimal("#2E0854");
        $manager->persist($colourPurple);
        $manager->flush();
        $this->addReference('colour-purple', $colourPurple);

        $colourBrown = new Colour();
        $colourBrown->setName('brown');
        $colourBrown->setCMYK("0,0,0,0");
        $colourBrown->setHexadecimal("#f4a460");
        $manager->persist($colourBrown);
        $manager->flush();
        $this->addReference('colour-brown', $colourBrown);

        $colourRoyalBlue = new Colour();
        $colourRoyalBlue->setName('royal blue');
        $colourRoyalBlue->setCMYK("255,255,0,0");
        $colourRoyalBlue->setHexadecimal("#4169e1");
        $manager->persist($colourRoyalBlue);
        $manager->flush();
        $this->addReference('colour-royal-blue', $colourRoyalBlue);

        $colourNavyBlue = new Colour();
        $colourNavyBlue->setName('navy blue');
        $colourNavyBlue->setCMYK("255,255,0,0");
        $colourNavyBlue->setHexadecimal("#000080");
        $manager->persist($colourNavyBlue);
        $manager->flush();
        $this->addReference('colour-navy-blue', $colourNavyBlue);

        $colourGreen = new Colour();
        $colourGreen->setName('green');
        $colourGreen->setCMYK("0,0,0,0");
        $colourGreen->setHexadecimal("#00ff00");
        $manager->persist($colourGreen);
        $manager->flush();
        $this->addReference('colour-green', $colourGreen);

        $colourLime = new Colour();
        $colourLime->setName('lime');
        $colourLime->setCMYK("154,0,154,49");
        $colourLime->setHexadecimal("#32cd32");
        $manager->persist($colourLime);
        $manager->flush();
        $this->addReference('colour-lime', $colourLime);

        $colourPink = new Colour();
        $colourPink->setName('pink');
        $colourPink->setCMYK("154,0,154,49");
        $colourPink->setHexadecimal("#ff69b4");
        $manager->persist($colourPink);
        $manager->flush();
        $this->addReference('colour-pink', $colourPink);

        $colourLightBlue = new Colour();
        $colourLightBlue->setName('light blue');
        $colourLightBlue->setCMYK("154,0,154,49");
        $colourLightBlue->setHexadecimal("#add8e6");
        $manager->persist($colourLightBlue);
        $manager->flush();
        $this->addReference('colour-light-blue', $colourLightBlue);

        $colourWhite = new Colour();
        $colourWhite->setName('white');
        $colourWhite->setCMYK("0,0,0,0");
        $colourWhite->setHexadecimal("#FFFFFF");
        $manager->persist($colourWhite);
        $manager->flush();
        $this->addReference('colour-white', $colourWhite);

        $colourYellow = new Colour();
        $colourYellow->setName('yellow');
        $colourYellow->setCMYK("0,2,255,0");
        $colourYellow->setHexadecimal("#FFFC00");
        $manager->persist($colourYellow);
        $manager->flush();
        $this->addReference('colour-yellow', $colourYellow);

        $colourBlue = new Colour();
        $colourBlue->setName('blue');
        $colourBlue->setCMYK("255,255,0,0");
        $colourBlue->setHexadecimal("#0000FF");
        $manager->persist($colourBlue);
        $manager->flush();
        $this->addReference('colour-blue', $colourBlue);

        $colourBlack = new Colour();
        $colourBlack->setName('black');
        $colourBlack->setCMYK("0,0,0,255");
        $colourBlack->setHexadecimal("#000000");
        $manager->persist($colourBlack);
        $manager->flush();
        $this->addReference('colour-black', $colourBlack);

        $colourRed = new Colour();
        $colourRed->setName('red');
        $colourRed->setCMYK("0,255,255,0");
        $colourRed->setHexadecimal("#FF0000");
        $manager->persist($colourRed);
        $manager->flush();
        $this->addReference('colour-red', $colourRed);

        $colourLimeGreen = new Colour();
        $colourLimeGreen->setName('limegreen');
        $colourLimeGreen->setCMYK("154,0,154,49");
        $colourLimeGreen->setHexadecimal("#32CD32");
        $manager->persist($colourLimeGreen);
        $manager->flush();
        $this->addReference('colour-limegreen', $colourLimeGreen);

        $colourLimeGreen2 = new Colour();
        $colourLimeGreen2->setName('lime green');
        $colourLimeGreen2->setCMYK("154,0,154,49");
        $colourLimeGreen2->setHexadecimal("#32CD32");
        $manager->persist($colourLimeGreen2);
        $manager->flush();
        $this->addReference('colour-limegreen-2', $colourLimeGreen2);

        $colourOrange = new Colour();
        $colourOrange->setName('orange');
        $colourOrange->setCMYK("0,93,255,0");
        $colourOrange->setHexadecimal("#FFA200");
        $manager->persist($colourOrange);
        $manager->flush();
        $this->addReference('colour-orange', $colourOrange);

        $colourSilver = new Colour();
        $colourSilver->setName('silver');
        $colourSilver->setCMYK("0,93,255,0");
        $colourSilver->setHexadecimal("#c0c0c0");
        $manager->persist($colourSilver);
        $manager->flush();
        $this->addReference('colour-silver', $colourSilver);

        $colourGold = new Colour();
        $colourGold->setName('gold');
        $colourGold->setCMYK("0,93,255,0");
        $colourGold->setHexadecimal("#ffd700");
        $manager->persist($colourGold);
        $manager->flush();
        $this->addReference('colour-gold', $colourGold);

        $colourDarkBlue = new Colour();
        $colourDarkBlue->setName('dark blue');
        $colourDarkBlue->setCMYK("0,93,255,0");
        $colourDarkBlue->setHexadecimal("#000080");
        $manager->persist($colourDarkBlue);
        $manager->flush();
        $this->addReference('colour-dark-blue', $colourDarkBlue);

        $colourAqua = new Colour();
        $colourAqua->setName('aqua');
        $colourAqua->setCMYK("0,93,255,0");
        $colourAqua->setHexadecimal("#028482");
        $manager->persist($colourAqua);
        $manager->flush();
        $this->addReference('colour-aqua', $colourAqua);

        $colourMaroon = new Colour();
        $colourMaroon->setName('maroon');
        $colourMaroon->setCMYK("0,93,255,0");
        $colourMaroon->setHexadecimal("#b03060");
        $manager->persist($colourMaroon);
        $manager->flush();
        $this->addReference('colour-maroon', $colourMaroon);

        $colourGrey = new Colour();
        $colourGrey->setName('grey');
        $colourGrey->setCMYK("0,93,255,0");
        $colourGrey->setHexadecimal("#dddddd");
        $manager->persist($colourGrey);
        $manager->flush();
        $this->addReference('colour-grey', $colourGrey);

        $colourTurquoise = new Colour();
        $colourTurquoise->setName('turquoise');
        $colourTurquoise->setCMYK("0,93,255,0");
        $colourTurquoise->setHexadecimal("#34DDDD");
        $manager->persist($colourTurquoise);
        $manager->flush();
        $this->addReference('colour-turquoise', $colourTurquoise);

        $colourCerise = new Colour();
        $colourCerise->setName('cerise');
        $colourCerise->setCMYK("0,93,255,0");
        $colourCerise->setHexadecimal("#CA278C");
        $manager->persist($colourCerise);
        $manager->flush();
        $this->addReference('colour-cerise', $colourCerise);
    }

    /**
     * {@inheritDoc}
     */
    public function getOrder()
    {
        return 5;
    }

}
