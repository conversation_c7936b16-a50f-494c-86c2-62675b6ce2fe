<?php

namespace DBLS\Bundle\JerseyColoursBundle\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolverInterface;
use Doctrine\ORM\EntityManager;

use DBLS\Bundle\JerseyColoursBundle\Form\ElementAssociationType;
use DBLS\Bundle\JerseyColoursBundle\Form\PatternAssociationType;

class JerseyType extends AbstractType
{
    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder->add('description', 'textarea', array( 'required' => false ))
		      ->add('elements', 'collection', array( 'type' => new JerseyElementType() ));
    }
    
    /**
     * @param OptionsResolverInterface $resolver
     */
    public function setDefaultOptions(OptionsResolverInterface $resolver)
    {
        $resolver->setDefaults(array(
            'data_class' => 'DBLS\Bundle\JerseyColoursBundle\Entity\Jersey'
        ));
    }

    /**
     * @return string
     */
    public function getName()
    {
        return 'dbls_bundle_jerseycoloursbundle_jersey';
    }
}
