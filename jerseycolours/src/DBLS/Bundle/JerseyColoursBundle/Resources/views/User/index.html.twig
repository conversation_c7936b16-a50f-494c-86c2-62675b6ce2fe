{% extends 'site.html.twig' %}

{% block page_header %}
    <div id="content-header">
	<h1>User list</h1>
	<div class="btn-group">
	    <a class="btn btn-large" href="{{ path('user_new') }}" title="New User"><i class="fa fa-plus-circle"></i> New User</a>
	</div>
    </div>
{% endblock page_header %}

{% block breadcrumb %}
    <div id="breadcrumb">
	<a href="{{ path('homepage') }}" class="tip-bottom" data-original-title="Go to Home"><i class="fa fa-home"></i> Home</a>
	<a href="{{ path('user') }}" class="current">User list</a>
    </div>
{% endblock breadcrumb %}

{% block content %}
    <div class="row">
	<div class="col-xs-12">
	    <div class="widget-box">
		<div class="widget-title">
		    <span class="icon">
			<i class="fa fa-th"></i>
		    </span>
		    <h5>User list</h5>
		</div>
		<div class="widget-content nopadding">
		    <table class="table table-bordered table-striped table-hover data-table auto-init">
			<thead>
			    <tr>
				<th>#</th>
				<th>Username</th>
				<th>Email</th>
				<th>Status</th>
				<th class="no-sort">Action</th>
			    </tr>
			</thead>
			<tbody>
			    {% for entity in entities %}
				<tr>
				    <td>{{ entity.id }}</td>
				    <td>{{ entity.username|capitalize }}</td>
				    <td>{{ entity.email }}</td>
				    <td><label class="label label{% if entity.enabled %}-success">Active{% else %}-danger">Inactive{% endif %}</label></td>
				    <td>
					<a href="{{ path('user_edit', {'id': entity.id}) }}" class="btn btn-primary btn-xs"><i class="fa fa-pencil"></i> Edit</a>
				    </td>
				</tr>
			    {% endfor %}
			</tbody>
		    </table>
		</div>
	    </div>
	</div>
    </div>
{% endblock %}