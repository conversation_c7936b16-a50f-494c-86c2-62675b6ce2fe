import o,{createContext as H,useContext as E,useEffect as m,useState as u}from"react";import{createPortal as g}from"react-dom";import{useDisposables as h}from'../hooks/use-disposables.js';import{objectToFormEntries as x}from'../utils/form.js';import{compact as y}from'../utils/render.js';import{Hidden as l,HiddenFeatures as d}from'./hidden.js';let f=H(null);function W(t){let[e,r]=u(null);return o.createElement(f.Provider,{value:{target:e}},t.children,o.createElement(l,{features:d.Hidden,ref:r}))}function c({children:t}){let e=E(f);if(!e)return o.createElement(o.Fragment,null,t);let{target:r}=e;return r?g(o.createElement(o.Fragment,null,t),r):null}function j({data:t,form:e,disabled:r,onReset:n,overrides:F}){let[i,a]=u(null),p=h();return m(()=>{if(n&&i)return p.addEventListener(i,"reset",n)},[i,e,n]),o.createElement(c,null,o.createElement(C,{setForm:a,formId:e}),x(t).map(([s,v])=>o.createElement(l,{features:d.Hidden,...y({key:s,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:e,disabled:r,name:s,value:v,...F})})))}function C({setForm:t,formId:e}){return m(()=>{if(e){let r=document.getElementById(e);r&&t(r)}},[t,e]),e?null:o.createElement(l,{features:d.Hidden,as:"input",type:"hidden",hidden:!0,readOnly:!0,ref:r=>{if(!r)return;let n=r.closest("form");n&&t(n)}})}export{j as FormFields,W as FormFieldsProvider,c as HoistFormFields};
