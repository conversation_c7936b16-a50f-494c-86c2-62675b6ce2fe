<?php

namespace DBLS\Bundle\JerseyColoursBundle\Services;

use Doctrine\ORM\EntityManager;
use DBLS\Bundle\JerseyColoursBundle\Entity\Jersey;
use Symfony\Component\DependencyInjection\ContainerAware;

/**
 * The purpose of this class is to generate Jerseys.
 *
 * <AUTHOR>
 */
class JerseyCloneService extends ContainerAware
{

    /**
     * Holds an instance of database connection
     * @var EntityManager
     */
    private $em = null;

    public function __construct(EntityManager $em)
    {
        $this->em = $em;
    }

    /**
     * 
     * @param  Jersey $source
     * @param  Jersey $target
     * @return boolean
     */
    public function cloneJersey(Jersey $source, Jersey $target)
    {
        $description = $source->generateString();

        $jerseyParser = $this->container->get('services.jersey_parser');
        $jerseyGenerator = $this->container->get('services.jersey_generator');

        $target->setDescription($description);

        $jerseyParser->setJersey($target);
        $target = $jerseyParser->createJerseyFromString($description);

        return $jerseyGenerator->createJersey($target, false, false);
    }
}