var port, server, service, page, url
    fs = require('fs');

port = 9494;
server = require('webserver').create();

// Since <PERSON> has control, the invoked script is deep in the argument stack
var currentFile = require('system').args[0];
var curFilePath = fs.absolute(currentFile).split('/');

// I only bother to change the directory if we weren't already there when invoking casperjs
if (curFilePath.length > 1) {
    curFilePath.pop(); // PhantomJS does not have an equivalent path.baseName()-like method
    fs.changeWorkingDirectory(curFilePath.join('/'));
}

page = require('webpage').create();
url = fs.absolute('./canvas.html');
if ( phantom.version.major >= 2 )
  url = 'file://' + url;

var executeJersey = function(jersey)
{
    jerseyGenerator(jersey);
};


var log = function(msg)
{
    var now = new Date();
    console.log(now, msg);
};

var checkIsFinished = function()
{
    return isFinished;
};

var getSvg = function()
{
    return document.getElementById("svg-container").innerHTML;
};

var loadInProgress = true;
page.onLoadStarted = function() 
{
  log('Page loading');
  loadInProgress = true;
};

page.onLoadFinished = function() {
  log('Page Ready!');
  loadInProgress = false;
};

var checkMethod = function(request, response)
{
    if ( 'POST' != request.method ) {

        response.statusCode = 405;
        var msg = 'Method not allowed';
        window.log(msg);
        response.write(msg);
        response.close();
        return false;
    }

    return true;
}

service = server.listen(
  '127.0.0.1:9494', 
  function (request, response) 
  {
      if ( !checkMethod(request, response) )
        return false;

      var jersey = JSON.parse(request.post);
      var svg = '';

      window.log('Generating: ' + jersey.name);

      loadInProgress = true;
      page.open(url, 
          function (status) 
          {
            if ( 'fail' == status ) {
                response.statusCode = 400;
                var msg = 'Error opening canvas';
                window.log(msg);
                response.write(msg);
                response.close();
                return false;
            }

            var checkPageLoaded = function() 
            {
                if ( !loadInProgress ) {

                    window.log('Executing SVG');
                    page.evaluate(executeJersey, jersey);

                    var tryAtLeast = 100;
                    var interval = setInterval(
                        function()
                        {
                            tryAtLeast--;

                            if ( page.evaluate(checkIsFinished) || tryAtLeast <= 0 ) {
                                
                                clearInterval(interval);
                                svg = page.evaluate(getSvg);

                                window.log('SVG Ready');

                                response.statusCode = 200;
                                response.write(svg);
                                response.closeGracefully();
                            }
                        },
                        50
                    );

                } else {
                    setTimeout(checkPageLoaded, 10);
                }
            };

            setTimeout(checkPageLoaded, 10);
          }
      );
  }
);

if (service) {
  console.log('Web server running on port ' + port);
} else {
  console.log('Error: Could not create web server listening on port ' + port);
  phantom.exit();
}
