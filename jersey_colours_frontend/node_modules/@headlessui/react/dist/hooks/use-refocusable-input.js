import{useRef as u}from"react";import*as r from'../utils/dom.js';import{useEvent as o}from'./use-event.js';import{useEventListener as s}from'./use-event-listener.js';function f(e){let l=u({value:"",selectionStart:null,selectionEnd:null});return s(e,"blur",n=>{let t=n.target;r.isHTMLInputElement(t)&&(l.current={value:t.value,selectionStart:t.selectionStart,selectionEnd:t.selectionEnd})}),o(()=>{if(document.activeElement!==e&&r.isHTMLInputElement(e)&&e.isConnected){if(e.focus({preventScroll:!0}),e.value!==l.current.value)e.setSelectionRange(e.value.length,e.value.length);else{let{selectionStart:n,selectionEnd:t}=l.current;n!==null&&t!==null&&e.setSelectionRange(n,t)}l.current={value:"",selectionStart:null,selectionEnd:null}}})}export{f as useRefocusableInput};
