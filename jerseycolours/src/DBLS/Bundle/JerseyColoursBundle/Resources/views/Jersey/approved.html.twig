{% extends 'site.html.twig' %}

{% block page_header %}
    <div id="content-header">
        <h1>Approved race list</h1>
    </div>
{% endblock page_header %}

{% block breadcrumb %}
    <div id="breadcrumb">
        <a href="{{ path('homepage') }}" class="tip-bottom" data-original-title="Go to Home"><i class="fa fa-home"></i> Home</a>
        <a href="{{ path('jersey_approved') }}" class="current">Approved race list</a>
    </div>
{% endblock breadcrumb %}


{% block content %}
    <div class="row">
        <div class="col-xs-12">
            <div class="widget-box">
                <div class="widget-title">
                    <span class="icon">
                        <i class="fa fa-th"></i>
                    </span>
                    <h5>Pattern list</h5>
                </div>
                <div class="widget-content nopadding">
                    <table class="table table-bordered table-striped table-hover data-table auto-init">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th style="width: 35%">Race name</th>
                                <th>Meeting</th>
                                <th>Date</th>
                                <th>No. of Silks</th>
                                <th style="width: 20%" class="no-sort">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for entity in entities %}
                                <tr>
                                    <td>{{ entity.id }}</td>
                                    <td>{{ entity.name|title }}</td>
                                    <td>{{ entity.meeting.location|title }}</td>
                                    <td>{{ entity.racetime|date('d M, Y') }}</td>
                                    <td>{{ entity.getApprovedJerseys|length}}</td>
                                    <td>
                                        <a href="{{ path('race_approved', {'id': entity.id}) }}" class="btn btn-primary btn-xs">
                                            <i class="fa fa-pencil"></i> Edit
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% endblock %}