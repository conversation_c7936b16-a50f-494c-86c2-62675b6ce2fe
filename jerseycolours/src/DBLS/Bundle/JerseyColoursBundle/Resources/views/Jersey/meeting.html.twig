{% extends 'site.html.twig' %}

{% block page_header %}
<div id="content-header">
    <h1>Meeting listing</h1>
</div>
{% endblock page_header %}

{% block breadcrumb %}
<div id="breadcrumb">
    <a href="{{ path('homepage') }}" class="tip-bottom" data-original-title="Go to Home"><i class="fa fa-home"></i> Home</a>
    <a href="{{ path('jersey_meeting') }}" class="current">Meeting list</a>
</div>
{% endblock breadcrumb %}

{% block content %}
    <div class="row">
        <div class="col-xs-12">
        	<div class="widget-box">
        	    <div class="widget-title">
        		<span class="icon">
        		    <i class="fa fa-cogs"></i>
        		</span>
        		<h5>Meeting list</h5>
        	    </div>
        	    <div class="widget-content jersey-container">
                    {% set hide_message = 'show' %}
                    
                    {% for date, meetings in dados %}
                        {% if meetings is defined and meetings|length > 0 %}            		                                
                            {{ include('JerseyColoursBundle:Jersey:meetingdetail.html.twig', { 'meetings' : meetings, 'date' : date }) }}
                            {% set hide_message = 'hide' %}
            		{% endif %}
                    {% endfor %}
                        
                    <div id="message-no-race" class="alert alert-warning {{ hide_message }}">
                         There is no <b>pending</b> Meeting.
                    </div>
        	    </div>
        	</div>
        </div>
    </div>

{% endblock content %}