<?php

namespace DBLS\Bundle\JerseyColoursBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use DBLS\Bundle\JerseyColoursBundle\Adapters\FileAdapter;

class ImporterCommand extends ContainerAwareCommand
{
    protected function configure()
    {
        $this->setName('jersey:import')
             ->setDescription('Import the Silks from the source');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $container = $this->getContainer();

        $adapter = new FileAdapter($container);
        
        $importer = $container->get('services.importer');
        $importer->setAdapter($adapter)->processCreate();
    }
}