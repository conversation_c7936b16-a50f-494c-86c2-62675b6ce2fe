# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_07_08_095757) do
  create_table "colours", force: :cascade do |t|
    t.string "name"
    t.string "cmyk"
    t.string "hexadecimal"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "element_types", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "errors", force: :cascade do |t|
    t.string "type"
    t.text "message"
    t.string "expression"
    t.text "details"
    t.integer "jersey_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["jersey_id"], name: "index_errors_on_jersey_id"
  end

  create_table "jersey_element_patterns", force: :cascade do |t|
    t.integer "jersey_element_id", null: false
    t.integer "pattern_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["jersey_element_id"], name: "index_jersey_element_patterns_on_jersey_element_id"
    t.index ["pattern_id"], name: "index_jersey_element_patterns_on_pattern_id"
  end

  create_table "jersey_elements", force: :cascade do |t|
    t.integer "order"
    t.integer "jersey_id", null: false
    t.integer "element_type_id", null: false
    t.integer "colour_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["colour_id"], name: "index_jersey_elements_on_colour_id"
    t.index ["element_type_id"], name: "index_jersey_elements_on_element_type_id"
    t.index ["jersey_id"], name: "index_jersey_elements_on_jersey_id"
  end

  create_table "jersey_pattern_colours", force: :cascade do |t|
    t.integer "order"
    t.integer "jersey_element_pattern_id", null: false
    t.integer "colour_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["colour_id"], name: "index_jersey_pattern_colours_on_colour_id"
    t.index ["jersey_element_pattern_id"], name: "index_jersey_pattern_colours_on_jersey_element_pattern_id"
  end

  create_table "jerseys", force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.text "string_value"
    t.boolean "status"
    t.boolean "parsed"
    t.boolean "parsing"
    t.datetime "parsing_time"
    t.string "external_jersey_id"
    t.integer "number"
    t.integer "race_id", null: false
    t.integer "trainer_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["race_id"], name: "index_jerseys_on_race_id"
    t.index ["trainer_id"], name: "index_jerseys_on_trainer_id"
  end

  create_table "meetings", force: :cascade do |t|
    t.string "external_id"
    t.date "date"
    t.string "location"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "patterns", force: :cascade do |t|
    t.string "description"
    t.string "path_svg"
    t.string "side_path_svg"
    t.boolean "status"
    t.integer "element_type_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["element_type_id"], name: "index_patterns_on_element_type_id"
  end

  create_table "races", force: :cascade do |t|
    t.string "name"
    t.string "external_race_id"
    t.string "external_meet_id"
    t.integer "number"
    t.datetime "racetime"
    t.integer "meeting_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["meeting_id"], name: "index_races_on_meeting_id"
  end

  create_table "synonyms", force: :cascade do |t|
    t.string "term"
    t.string "original_term"
    t.integer "colour_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["colour_id"], name: "index_synonyms_on_colour_id"
  end

  create_table "trainers", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "users", force: :cascade do |t|
    t.string "email"
    t.string "password_digest"
    t.string "first_name"
    t.string "last_name"
    t.string "role"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  add_foreign_key "errors", "jerseys"
  add_foreign_key "jersey_element_patterns", "jersey_elements"
  add_foreign_key "jersey_element_patterns", "patterns"
  add_foreign_key "jersey_elements", "colours"
  add_foreign_key "jersey_elements", "element_types"
  add_foreign_key "jersey_elements", "jerseys"
  add_foreign_key "jersey_pattern_colours", "colours"
  add_foreign_key "jersey_pattern_colours", "jersey_element_patterns"
  add_foreign_key "jerseys", "races"
  add_foreign_key "jerseys", "trainers"
  add_foreign_key "patterns", "element_types"
  add_foreign_key "races", "meetings"
  add_foreign_key "synonyms", "colours"
end
