{% extends 'site.html.twig' %}

{% block page_header %}
    <div id="content-header">
	<h1>Colour list</h1>
	<div class="btn-group">
	    <a class="btn btn-large" href="{{ path('colour_new') }}" title="New Colour"><i class="fa fa-plus-circle"></i> New Colour</a>
	</div>
    </div>
{% endblock page_header %}

{% block breadcrumb %}
    <div id="breadcrumb">
	<a href="{{ path('homepage') }}" class="tip-bottom" data-original-title="Go to Home"><i class="fa fa-home"></i> Home</a>
	<a href="{{ path('colour') }}" class="current">Colour list</a>
    </div>
{% endblock breadcrumb %}

{% block content %}
    <div class="row">
	<div class="col-xs-12">
	    <div class="widget-box">
		<div class="widget-title">
		    <span class="icon">
			<i class="fa fa-th"></i>
		    </span>
		    <h5>Colour list</h5>
		</div>
		<div class="widget-content nopadding">
		    <table class="table table-bordered table-striped table-hover data-table auto-init">
			<thead>
			    <tr>
				<th style="width: 8%">#</th>
				<th style="width: 33%">Name</th>
				<th style="width: 15%">Hexadecimal</th>
				<th style="width: 7%">CMYK</th>
				<th style="width: 12%">Colour</th>
				<th style="width: 15%" class="no-sort">Actions</th>
			    </tr>
			</thead>
			<tbody>
			    {% for entity in entities %}
				<tr>
				    <td>{{ entity.id }}</td>
				    <td>{{ entity.name|capitalize }}</td>
				    <td>{{ entity.hexadecimal }}</td>
				    <td>{{ entity.CMYK }}</td>
				    <td><span class="colour-square" style="background-color: {{ entity.hexadecimal }}" title="{{ entity.name|capitalize }} - {{ entity.hexadecimal }}"></span></td>
				    <td>
					<a href="{{ path('colour_edit', {'id': entity.id}) }}" class="btn btn-primary btn-xs"><i class="fa fa-pencil"></i> Edit</a>
					<a href="javascript:;" data-id="{{ entity.id }}" class="btn btn-danger btn-xs delete-item"><i class="fa fa-trash-o"></i> Delete</a>
				    </td>
				</tr>
			    {% endfor %}
			</tbody>
		    </table>
		</div>
	    </div>
	</div>
    </div>
{% endblock content %}


{% block extrascripts %}
    {% javascripts 
	'@JerseyColoursBundle/Resources/public/js/bundle/colour.js' %}
		<script type="text/javascript" src="{{ asset_url }}"></script>
    {% endjavascripts %}
{% endblock extrascripts %}