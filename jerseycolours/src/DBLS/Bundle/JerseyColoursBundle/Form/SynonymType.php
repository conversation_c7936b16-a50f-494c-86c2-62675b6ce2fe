<?php

namespace DBLS\Bundle\JerseyColoursBundle\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolverInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Form\Form;
use DBLS\Bundle\JerseyColoursBundle\Form\DataTransformer\SynonymTypeTransformer;
use DBLS\Bundle\JerseyColoursBundle\Services\SynonymOptions;
use DBLS\Bundle\JerseyColoursBundle\Entity\Synonym;


class SynonymType extends AbstractType
{
    private $synonymOptionsService;

    /**
     * 
     * @param SynonymOptions $synonymOptionsService 
     */
    public function __construct(SynonymOptions $synonymOptionsService)
    {
        $this->synonymOptionsService = $synonymOptionsService;
    }

    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder->add('term')
                ->add('type', 'hidden');

        $builder->addEventListener(FormEvents::PRE_SET_DATA, function(FormEvent $event) use ($builder) {
            $synonym = $event->getData();

            if ( !$synonym )
                return; // $data is null when form is first constructed

            $form = $event->getForm();

            $choices = array();

            if ( $synonym->getType() != null ) {

                $original_term = $this->synonymOptionsService->buildFormTerm($synonym);
                $choices[$original_term] = $synonym->getOriginalTerm();
            }

            $this->_addOriginalTermField($form, $choices);
        });

        $builder->addEventListener(
            FormEvents::PRE_SUBMIT,
            function (FormEvent $event) 
            {
                $data = $event->getData();
                $form = $event->getForm();

                $type = explode('|', $data['original_term']);
                $data['type'] = $type[0];

                switch($type[0]) {
                    case Synonym::TYPE_COLOUR:
                        $field = 'colour';
                        break;
                    case Synonym::TYPE_ELEMENT:
                        $field = 'element_type';
                        break;
                    case Synonym::TYPE_PATTERN:
                        $field = 'pattern';
                        break;
                    default:
                        throw new \Exception('Could not find the Synonym type');
                }

                $data[$field] = $type[1];
                $data['original_term'] = $this->synonymOptionsService->getOriginalTerm($type[1], $type[0]);
                
                $choices = array();
                $choices[$data['original_term']] = $data['original_term'];

                $form->add($field);
                $this->_addOriginalTermField($form, $choices);

                $event->setData($data);
            }
        );
    }

    /**
     * 
     * @param Form   $form
     * @param int $colour_id
     */
    protected function _addOriginalTermField(Form $form, $choices = array())
    {
        // add colour
        $form->add(
                'original_term',
                'choice',
                array(
                    'attr'        => array('class' => 'original-term-select'),
                    'required'    => true,
                    'empty_data'  => null,
                    'choices'     => $choices
                )
        )->add('save', 'submit', array( 'attr' => array( 'data-loading-text' => "Loading..." ) ) );
    }
    
    /**
     * @param OptionsResolverInterface $resolver
     */
    public function setDefaultOptions(OptionsResolverInterface $resolver)
    {
        $resolver->setDefaults(array(
            'data_class' => 'DBLS\Bundle\JerseyColoursBundle\Entity\Synonym'
        ));
    }

    /**
     * @return string
     */
    public function getName()
    {
        return 'dbls_bundle_jerseycoloursbundle_synonym';
    }
}
