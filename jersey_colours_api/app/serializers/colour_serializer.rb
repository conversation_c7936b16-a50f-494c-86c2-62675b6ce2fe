# frozen_string_literal: true

class ColourSerializer
  include JSONAPI::Serializer

  attributes :name, :cmyk, :hexadecimal, :created_at, :updated_at

  attribute :usage_count do |colour|
    colour.jersey_elements.count + colour.jersey_pattern_colours.count
  end

  attribute :rgb do |colour|
    # Convert hex to RGB
    hex = colour.hexadecimal.gsub('#', '')
    {
      r: hex[0..1].to_i(16),
      g: hex[2..3].to_i(16),
      b: hex[4..5].to_i(16)
    }
  end
end
