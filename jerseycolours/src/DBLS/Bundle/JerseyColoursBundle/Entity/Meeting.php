<?php

namespace DBLS\Bundle\JerseyColoursBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Doctrine\Common\Collections\ArrayCollection;
use SamJ\DoctrineSluggableBundle\SluggableInterface;

/**
 * Meeting
 *
 * @ORM\Table()
 * @ORM\Entity(repositoryClass="DBLS\Bundle\JerseyColoursBundle\Entity\MeetingRepository")
 */
class Meeting implements SluggableInterface
{

    /**
     * @var integer
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;
    
    /**
     * @var string
     *
     * @ORM\Column(name="location", type="string", length=255)
     */
    private $location;
    
     /**
     * @var string
     *
     * @ORM\Column(name="externalId", type="string", length=255)
     */
    private $externalId;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="date", type="datetime")
     */
    private $date;
    
    /**
     * @ORM\ManyToOne(targetEntity="Import")
     * @ORM\JoinColumn(name="import_id", referencedColumnName="id")
     **/
    private $import;

    /**
     * @ORM\Column(type="string")
     */
    protected $slug;

    /**
     * @ORM\Column(type="string", nullable=true)
     */
    protected $code;

    /**
     * @ORM\OneToMany(targetEntity="Race", mappedBy="meeting")
     */
    private $races;
    
    public function __construct()
    {        
        $this->races = new ArrayCollection();
    }

    /**
     * Get id
     *
     * @return integer 
     */
    public function getId()
    {
	   return $this->id;
    }

    /**
     * Set location
     *
     * @param string $location
     * @return Meeting
     */
    public function setLocation($location)
    {
        $this->location = $location;

        return $this;
    }

    /**
     * Get location
     *
     * @return string 
     */
    public function getLocation()
    {
        return $this->location;
    }

    /**
     * Set code
     *
     * @param string $code
     * @return Meeting
     */
    public function setCode($code)
    {
        $this->code = $code;

        return $this;
    }

    /**
     * Get code
     *
     * @return string 
     */
    public function getCode()
    {
        return $this->code;
    }



    /**
     * Set externalId
     *
     * @param string $externalId
     * @return Meeting
     */
    public function setExternalId($externalId)
    {
        $this->externalId = $externalId;

        return $this;
    }

    /**
     * Get externalId
     *
     * @return string 
     */
    public function getExternalId()
    {
        return $this->externalId;
    }

    /**
     * Set import
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Import $import
     * @return Meeting
     */
    public function setImport(\DBLS\Bundle\JerseyColoursBundle\Entity\Import $import = null)
    {
        $this->import = $import;

        return $this;
    }

    /**
     * Get import
     *
     * @return \DBLS\Bundle\JerseyColoursBundle\Entity\Import 
     */
    public function getImport()
    {
        return $this->import;
    }

    /**
    * @return string
    */
    public function getSlug()
    {
        return $this->slug;
    }

    /**
    * @param string
    */
    public function setSlug($slug)
    {
        if (!empty($this->slug)) return false;
        $this->slug = $slug;
    }

    /**
    * @return array
    */
    public function getSlugFields() 
    {
        $slug = array($this->date->format('Y_m_d'), $this->location);
        return $slug;
    }

    /**
     * Set date
     *
     * @param \DateTime $date
     * @return Meeting
     */
    public function setDate($date)
    {
        
        $date = strtotime( $date );        
        $mysqldate = \DateTime::createFromFormat(
                    'Y-m-d H:i:s',
                    date( 'Y-m-d H:i:s', $date ),
                    new \DateTimeZone("GMT")
                );
        
        $this->date = $mysqldate;

        return $this;
    }

    /**
     * Get date
     *
     * @return \DateTime 
     */
    public function getDate()
    {
        return $this->date;
    }

    /**
     * Add races
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Race $races
     * @return Meeting
     */
    public function addRace(\DBLS\Bundle\JerseyColoursBundle\Entity\Race $races)
    {
        $this->races[] = $races;

        return $this;
    }

    /**
     * Remove races
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Race $races
     */
    public function removeRace(\DBLS\Bundle\JerseyColoursBundle\Entity\Race $races)
    {
        $this->races->removeElement($races);
    }

    /**
     * Get races
     *
     * @return \Doctrine\Common\Collections\Collection 
     */
    public function getRaces()
    {
        return $this->races;
    }

    /**
     * @return string 
     */
    public function getFolderPath()
    {
        $date = $this->getDate()->format('Y_m_d');
        $path = array($date, $this->slug);

        return implode('/', $path);
    }

    /**
     * @return string
     */
    public function getCleanFolder()
    {
        $date = $this->getDate()->format('Y_m_d') . '_';
        return str_replace($date, '', $this->getSlug());
    }
}
