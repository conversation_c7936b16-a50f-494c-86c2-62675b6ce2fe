<?php

namespace DBLS\Bundle\JerseyColoursBundle\Adapters;

use \Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Description of FileAdapter
 *
 * <AUTHOR>
 */
class FileAdapter extends DataAdapter implements DataSourceInterface
{
    /*
     * @var $path
     */
    protected $path;
    protected $container;
    
    /**
     * @ContainerInterface $container 
     */
    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
        $this->path = $this->container->get('kernel')->getRootDir() . "/../../documentation/data.json";                
    }

    /*
     * Return data to ImporterService
     */
    public function getData()
    {
        $data = json_decode( file_get_contents( $this->path ) );
        return $this->getDataProcessed( $data );   
    }
    
    
}
