# frozen_string_literal: true

module Api
  module V1
    class PatternsController < BaseController
      before_action :set_pattern, only: [:show, :update, :destroy]

      def index
        patterns = Pattern.includes(:element_type)
        patterns = patterns.where('description ILIKE ?', "%#{params[:search]}%") if params[:search].present?
        patterns = patterns.where(status: params[:status]) if params[:status].present?
        patterns = patterns.where(element_type_id: params[:element_type_id]) if params[:element_type_id].present?
        patterns = paginate(patterns.order(:description))

        render_json_api(patterns, PatternSerializer, include: [:element_type])
      end

      def show
        render_json_api(@pattern, PatternSerializer, include: [:element_type])
      end

      def create
        pattern = Pattern.new(pattern_params)
        
        if pattern.save
          render_json_api(pattern, PatternSerializer, status: :created, include: [:element_type])
        else
          render_error('Failed to create pattern', :unprocessable_entity, pattern.errors.full_messages)
        end
      end

      def update
        if @pattern.update(pattern_params)
          render_json_api(@pattern, PatternSerializer, include: [:element_type])
        else
          render_error('Failed to update pattern', :unprocessable_entity, @pattern.errors.full_messages)
        end
      end

      def destroy
        if @pattern.jersey_element_patterns.exists?
          render_error('Cannot delete pattern that is in use', :unprocessable_entity)
        else
          @pattern.destroy
          render_success(nil, 'Pattern deleted successfully')
        end
      end

      private

      def set_pattern
        @pattern = Pattern.find(params[:id])
      end

      def pattern_params
        params.require(:pattern).permit(:description, :path_svg, :side_path_svg, :status, :element_type_id)
      end
    end
  end
end
