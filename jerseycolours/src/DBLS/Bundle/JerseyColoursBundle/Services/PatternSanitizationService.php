<?php

namespace DBLS\Bundle\JerseyColoursBundle\Services;

use Doctrine\ORM\Event\LifecycleEventArgs;
use DBLS\Bundle\JerseyColoursBundle\Entity\Pattern;

class PatternSanitizationService
{

    protected $em;

    /**
    * @LifecycleEventArgs $args
    */
    public function prePersist(LifecycleEventArgs $args)
    {
        $entity = $args->getEntity();

        if (!$entity instanceof Pattern)
            return true;

        $this->em = $args->getEntityManager();
        $this->cleanEntityName($entity);
    }

    /**
    * @LifecycleEventArgs $args
    */
    public function preUpdate(LifecycleEventArgs $args)
    {
        $entity = $args->getEntity();

        if (!$entity instanceof Pattern)
            return true;

        $this->em = $args->getEntityManager();
        $this->cleanEntityName($entity);
    }

    /**
     * @Pattern $pattern
     */
    public function cleanEntityName(Pattern $pattern)
    {
        $patternDescription = $pattern->getDescription();
        $elements = $this->em->getRepository('JerseyColoursBundle:ElementType')->findAll();

        $knownElements = array();
        foreach ( $elements as $element ) {
            $elementName = strtolower(trim($element->getName()));

            $regex = sprintf('/%s/i', preg_quote($elementName));
            $patternDescription = trim(preg_replace($regex, '', $patternDescription));
        }

        $patternDescription = preg_replace('/ {2,}/i', ' ', $patternDescription);

        $pattern->setDescription($patternDescription);
    }
}