<?php

namespace DBLS\Bundle\JerseyColoursBundle\Validator\Constraints;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;

/**
 * Prevents duplicate entries in a column.
 *
 * <AUTHOR>
 */
class UniqueValidator extends ConstraintValidator {

    /**
     * Holds an instance of database connection
     * @var \Doctrine\ORM\EntityManager
     */
    private $em = null;

    public function __construct(\Doctrine\ORM\EntityManager $em) {
        $this->em = $em;
    }

    protected function createQuery($repo, $fieldName, $entity) {
        $id = '';
        if (method_exists($entity, 'getId')) {
            $id = $entity->getId();
        }

        if ($id == '' || $id == null) {
            $id = 0;
        }

        $val = '';
        if (method_exists($entity, 'get' . ucwords($fieldName))) {
            $invoke = 'get' . ucwords($fieldName);
            $val = $entity->$invoke();
        }

        $qb = $repo->createQueryBuilder('t');
        $qb->where($qb->expr()->andX(
                                $qb->expr()->neq('t.id', ':pId'), $qb->expr()->eq($qb->expr()->lower($qb->expr()->trim('t.' . $fieldName)), ':pVal')
                ))
                ->setParameter('pId', $id)
                ->setParameter('pVal', strtolower(trim($val)));

        $query = $qb->getQuery();
        return $query;
    }

    public function validate($entity, Constraint $constraint) {
        $repo = $this->em->getRepository($constraint->repositoryClass);
        if (!$repo) {
            throw new Exception(sprintf("Repository '%s' not found", $constraint->repositoryClass));
        }

        if (!is_null($constraint->fields)) {
            foreach ($constraint->fields as $field) {

                $fieldName = trim($field[0]);
                if ($fieldName == '') {
                    throw new \Exception(sprintf("Incorrect use of Unique Constraint. It does not have a field name."));
                }

                $fieldMessage = trim($field[1]);
                if ($fieldMessage == '') {
                    throw new \Exception(sprintf("Incorrect use of Unique Constraint. It does not have an error message."));
                }

                $query = $this->createQuery($repo, $fieldName, $entity);
                $r = $query->getResult();
                if (count($r) > 0) {
                    /* Raise an error for current field */
                    $this->context->buildViolation($fieldMessage)
                            ->atPath($fieldName)
                            ->addViolation();
                }
            }
        }
    }

}
