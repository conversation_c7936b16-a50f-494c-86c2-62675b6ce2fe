<?php

namespace DBLS\Bundle\JerseyColoursBundle\StringParser;

interface IPspellQueryable {

    /*
    * This method MUST retur false (if not found) or an array like:
    *
    * Ex1: A call on query as: ->query("armband") MUST return an array like:
    *      array(
    *          'token' => Tokens::T_ELEMENT,
    *          'match' => 'bands' // this HAS TO be a valid element name (or colour name or pattern name)
    *      )
    *
    * Ex2: A call on query as: ->query("asdfjkljqwert") MUST return FALSE:
    *
    */
    public function query($string);
}
