<?php

namespace DBLS\Bundle\JerseyColoursBundle\Entity;

use Doctrine\ORM\EntityRepository;

/**
 * ErrorRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class ErrorRepository extends EntityRepository
{
    
    public function getErrorsNotSolved(Jersey $entity) 
    {
        $qb = $this->createQueryBuilder("e")
                    ->innerJoin('JerseyColoursBundle:Jersey', 'j', 'WITH', 'e.jersey = j.id')
                    ->where('e.solved = :pSolved')
                    ->andWhere('e.jersey = :pJersey')
                    ->orderBy('e.date','ASC')
                    ->distinct()
                    ->setParameter('pSolved',0)
                    ->setParameter('pJersey',$entity->getId());

        return $qb->getQuery()->getResult();
    }

    /**
     * 
     * @param  Jersey $jersey
     */
    public function solveErrorsJersey(Jersey $jersey)
    {
        $query = 'update JerseyColoursBundle:Error e set e.solved = :solved where e.jersey = :jersey';
        $em = $this->getEntityManager();

        $q = $em->createQuery($query)
                    ->setParameter('solved', 1)
                    ->setParameter('jersey', $jersey);

        $q->execute();
    }
    
    /*
     * 
     */
    public function findCommonErros()
    {
        $qb = $this->createQueryBuilder("e")
                    ->select('COUNT(e.expression) AS amount, e.expression, e.message, e.type, e.id')
                    ->where('e.expression is not null')
                    ->groupBy('e.expression')
                    ->orderBy('amount','DESC')
                    ->setMaxResults( 50 );
        
        return $qb->getQuery()->getResult();
    }
    
}
