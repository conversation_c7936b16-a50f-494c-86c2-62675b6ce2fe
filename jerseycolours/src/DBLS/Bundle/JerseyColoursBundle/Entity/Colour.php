<?php

namespace DBLS\Bundle\JerseyColoursBundle\Entity;

use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

use Doctrine\ORM\Mapping as ORM;
use Doctrine\Common\Collections\ArrayCollection;
use DBLS\Bundle\JerseyColoursBundle\Validator\Constraints as JerseyAssert;

/**
 * Colour
 *
 * @ORM\Table()
 * @ORM\Entity(repositoryClass="DBLS\Bundle\JerseyColoursBundle\Entity\ColourRepository")
 *
 * @JerseyAssert\Unique(
 *   repositoryClass = "JerseyColoursBundle:Colour",
 *   fields = {
 *      {"name", "A colour with the same name already exists"},
 *      {"hexadecimal", "A colour with the same hexadecimal already exists"}
 *   }
 * )
 *
 */
class Colour
{
    /**
     * @var integer
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     * 
     * @Assert\NotBlank()
     * @Assert\Length(min=3)
     * @ORM\Column(name="name", type="string", length=255)
     */
    private $name;

    /**
     * @var string
     * @Assert\NotBlank()
     *
     * @ORM\Column(name="CMYK", type="string", length=255)
     */
    private $CMYK;

    /**
     * @var string
     *
     * @ORM\Column(name="hexadecimal", type="string", length=255)
     */
    private $hexadecimal;
    
    /**
     * @ORM\OneToMany(targetEntity="JerseyPatternColour", mappedBy="colour")
    */
    protected $pattern_colours;
    
    /**
     * @ORM\OneToMany(targetEntity="Synonym", mappedBy="colour")
     */
    private $synonyms;
	
    public function __construct()
    {        
        $this->pattern_colours = new ArrayCollection();
    }	

    /**
     * Get id
     *
     * @return integer 
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set name
     *
     * @param string $name
     * @return Colour
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string 
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set CMYK
     *
     * @param string $cMYK
     * @return Colour
     */
    public function setCMYK($cMYK)
    {
        $this->CMYK = $cMYK;

        return $this;
    }

    /**
     * Get CMYK
     *
     * @return string 
     */
    public function getCMYK()
    {
        return $this->CMYK;
    }

    /**
     * Set hexadecimal
     *
     * @param string $hexadecimal
     * @return Colour
     */
    public function setHexadecimal($hexadecimal)
    {
        $this->hexadecimal = $hexadecimal;

        return $this;
    }

    /**
     * Get hexadecimal
     *
     * @return string 
     */
    public function getHexadecimal()
    {
        return $this->hexadecimal;
    }
    
    /**
     * 
     * @return string
     */
    public function __toString()
    {
	return $this->name;
    }


    /**
     * Add pattern_colours
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\JerseyPatternColour $patternColours
     * @return Colour
     */
    public function addPatternColour(\DBLS\Bundle\JerseyColoursBundle\Entity\JerseyPatternColour $patternColours)
    {
        $this->pattern_colours[] = $patternColours;

        return $this;
    }

    /**
     * Remove pattern_colours
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\JerseyPatternColour $patternColours
     */
    public function removePatternColour(\DBLS\Bundle\JerseyColoursBundle\Entity\JerseyPatternColour $patternColours)
    {
        $this->pattern_colours->removeElement($patternColours);
    }

    /**
     * Get pattern_colours
     *
     * @return \Doctrine\Common\Collections\Collection 
     */
    public function getPatternColours()
    {
        return $this->pattern_colours;
    }

    /**
     * Add synonyms
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Synonym $synonyms
     * @return Colour
     */
    public function addSynonym(\DBLS\Bundle\JerseyColoursBundle\Entity\Synonym $synonyms)
    {
        $this->synonyms[] = $synonyms;

        return $this;
    }

    /**
     * Remove synonyms
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Synonym $synonyms
     */
    public function removeSynonym(\DBLS\Bundle\JerseyColoursBundle\Entity\Synonym $synonyms)
    {
        $this->synonyms->removeElement($synonyms);
    }

    /**
     * Get synonyms
     *
     * @return \Doctrine\Common\Collections\Collection 
     */
    public function getSynonyms()
    {
        return $this->synonyms;
    }
}
