import React, { useState, useEffect } from 'react';
import { colourService } from '../services/colourService';
import type { Colour, ColourSearchParams, ColourForm } from '../services/colourService';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';

export const Colours: React.FC = () => {
  const [colours, setColours] = useState<Colour[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [searchParams, setSearchParams] = useState<ColourSearchParams>({
    page: 1,
    per_page: 50,
    search: '',
  });
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingColour, setEditingColour] = useState<Colour | null>(null);
  const [formData, setFormData] = useState<ColourForm>({
    name: '',
    cmyk: '',
    hexadecimal: '',
  });
  const [formErrors, setFormErrors] = useState<Partial<ColourForm>>({});

  useEffect(() => {
    loadColours();
  }, [searchParams]);

  const loadColours = async () => {
    try {
      setLoading(true);
      const response = await colourService.getColours(searchParams);
      if (response.success) {
        setColours(response.data || []);
      } else {
        setError(response.error || 'Failed to load colours');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load colours');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (searchTerm: string) => {
    setSearchParams(prev => ({ ...prev, search: searchTerm, page: 1 }));
  };

  const validateForm = (): boolean => {
    const errors: Partial<ColourForm> = {};

    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    }

    if (!formData.hexadecimal.trim()) {
      errors.hexadecimal = 'Hex color is required';
    } else if (!colourService.isValidHex(formData.hexadecimal)) {
      errors.hexadecimal = 'Invalid hex color format (e.g., #FF0000)';
    }

    if (!formData.cmyk.trim()) {
      errors.cmyk = 'CMYK values are required';
    } else if (!colourService.isValidCMYK(formData.cmyk)) {
      errors.cmyk = 'Invalid CMYK format (e.g., 0,100,100,0)';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      let response;
      if (editingColour) {
        response = await colourService.updateColour(editingColour.id, formData);
      } else {
        response = await colourService.createColour(formData);
      }

      if (response.success) {
        setShowCreateModal(false);
        setEditingColour(null);
        setFormData({ name: '', cmyk: '', hexadecimal: '' });
        setFormErrors({});
        loadColours();
      } else {
        setError(response.error || 'Failed to save colour');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to save colour');
    }
  };

  const handleEdit = (colour: Colour) => {
    setEditingColour(colour);
    setFormData({
      name: colour.name,
      cmyk: colour.cmyk,
      hexadecimal: colour.hexadecimal,
    });
    setFormErrors({});
    setShowCreateModal(true);
  };

  const handleDelete = async (colour: Colour) => {
    if (!confirm(`Are you sure you want to delete "${colour.name}"?`)) return;

    try {
      const response = await colourService.deleteColour(colour.id);
      if (response.success) {
        loadColours();
      } else {
        setError(response.error || 'Failed to delete colour');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to delete colour');
    }
  };

  const handleCloseModal = () => {
    setShowCreateModal(false);
    setEditingColour(null);
    setFormData({ name: '', cmyk: '', hexadecimal: '' });
    setFormErrors({});
  };

  const handleHexChange = (hex: string) => {
    setFormData(prev => ({ ...prev, hexadecimal: hex }));

    // Auto-generate CMYK if hex is valid (simplified conversion)
    if (colourService.isValidHex(hex)) {
      const rgb = colourService.hexToRgb(hex);
      if (rgb) {
        // Simplified RGB to CMYK conversion
        const r = rgb.r / 255;
        const g = rgb.g / 255;
        const b = rgb.b / 255;

        const k = 1 - Math.max(r, Math.max(g, b));
        const c = (1 - r - k) / (1 - k) || 0;
        const m = (1 - g - k) / (1 - k) || 0;
        const y = (1 - b - k) / (1 - k) || 0;

        const cmyk = `${Math.round(c * 100)},${Math.round(m * 100)},${Math.round(y * 100)},${Math.round(k * 100)}`;
        setFormData(prev => ({ ...prev, cmyk }));
      }
    }
  };

  return (
    <div>
      {/* Header */}
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-xl font-semibold text-gray-900">Colours</h1>
          <p className="mt-2 text-sm text-gray-700">
            Manage the color palette used for jersey generation with CMYK and Hex values.
          </p>
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Colour
          </button>
        </div>
      </div>

      {/* Search */}
      <div className="mt-6 bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="relative rounded-md shadow-sm">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              className="focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md"
              placeholder="Search colours..."
              value={searchParams.search}
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mt-4 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Colours Grid */}
      <div className="mt-6 bg-white shadow overflow-hidden sm:rounded-md">
        <div className="px-4 py-5 sm:p-6">
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
              <p className="mt-2 text-sm text-gray-500">Loading colours...</p>
            </div>
          ) : colours.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">No colours found.</p>
              <button
                onClick={() => setShowCreateModal(true)}
                className="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-600 bg-primary-100 hover:bg-primary-200"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Create your first colour
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {colours.map((colour) => (
                <div key={colour.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between mb-3">
                    <div
                      className="w-12 h-12 rounded-lg border border-gray-300 flex-shrink-0"
                      style={{ backgroundColor: colour.hexadecimal }}
                    ></div>
                    <div className="flex space-x-1">
                      <button
                        onClick={() => handleEdit(colour)}
                        className="p-1 text-gray-400 hover:text-gray-600"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(colour)}
                        className="p-1 text-gray-400 hover:text-red-600"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-900 mb-1">
                      {colour.name}
                    </h3>
                    <p className="text-xs text-gray-500 mb-1">
                      {colour.hexadecimal}
                    </p>
                    <p className="text-xs text-gray-500 mb-2">
                      {colourService.formatCMYK(colour.cmyk)}
                    </p>
                    {colour.usage_count !== undefined && (
                      <p className="text-xs text-gray-400">
                        Used {colour.usage_count} time{colour.usage_count !== 1 ? 's' : ''}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Create/Edit Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <form onSubmit={handleSubmit}>
              <div className="mt-3">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    {editingColour ? 'Edit Colour' : 'Create New Colour'}
                  </h3>
                  <button
                    type="button"
                    onClick={handleCloseModal}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Name
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      placeholder="e.g., Royal Blue"
                    />
                    {formErrors.name && (
                      <p className="mt-1 text-xs text-red-600">{formErrors.name}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Hex Color
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="text"
                        value={formData.hexadecimal}
                        onChange={(e) => handleHexChange(e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        placeholder="#FF0000"
                      />
                      <div
                        className="w-10 h-10 rounded border border-gray-300"
                        style={{
                          backgroundColor: colourService.isValidHex(formData.hexadecimal)
                            ? formData.hexadecimal
                            : '#ffffff'
                        }}
                      ></div>
                    </div>
                    {formErrors.hexadecimal && (
                      <p className="mt-1 text-xs text-red-600">{formErrors.hexadecimal}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      CMYK Values
                    </label>
                    <input
                      type="text"
                      value={formData.cmyk}
                      onChange={(e) => setFormData(prev => ({ ...prev, cmyk: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      placeholder="0,100,100,0"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Format: C,M,Y,K (e.g., 0,100,100,0)
                    </p>
                    {formErrors.cmyk && (
                      <p className="mt-1 text-xs text-red-600">{formErrors.cmyk}</p>
                    )}
                  </div>
                </div>

                <div className="flex justify-end space-x-2 mt-6">
                  <button
                    type="button"
                    onClick={handleCloseModal}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700"
                  >
                    {editingColour ? 'Update' : 'Create'}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};
