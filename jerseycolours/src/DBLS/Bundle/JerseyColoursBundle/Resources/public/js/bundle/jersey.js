JockerColour = window.JockerColour || {};
$.fn.modal.Constructor.prototype.enforceFocus = function() {};

JockerColour.Jersey = {

    colours: null,

    jerseyPath: '',

    init: function()
    {
        this.bindApproveMeeting();
        this.bindEditJersey();
    },


    bindApproveMeeting: function()
    {
        $('button.approve-race').on(
            'click',
            function()
            {
                $(this).attr('disabled', true);
                var self = this;
                var id = $(this).data('id');
                var container = $(this).closest('.well');

                var raceApproved = function(response)
                {
                    if ( !response.status ) {

                        if ( response.msg )
                            Message.msgError(response.msg, container);
                        else
                            Message.msgError('Error approving race', container);

                    } else {

                        Message.msgSuccess('The race was approved', container);
                        General.waitTo(
                            function()
                            {
                                container.fadeOut(
                                    "slow",
                                    function()
                                    {
                                        container.remove();

                                        if ( !$('.jersey-container > .well').length )
                                            $('#message-no-race').removeClass('hide');
                                    }
                                );
                            },
                            3
                        );
                    }
                };

                Message.setFadeOut(false).msgInfo('Loading...', container);

                $.post(Routing.generate('race_approval', {id: id}))
                .done(raceApproved)
                .fail(
                    function()
                    {
                        Message.msgError('There was a problem approving the race', container);
                    }
                )
                .always(
                    function()
                    {
                        $(self).removeAttr('disabled');
                    }
                );
            }
        );
    },

    bindEditJersey: function()
    {
        $('a.edit-jersey').on('click',
            function()
            {
                var id = $(this).data('id');
                JockerColour.Jersey.editJersey(id);
            }
        );

        window.onpopstate = function(event) 
        {
            if ( event.state ) {
                JockerColour.Jersey.editJersey(event.state);
            } else {
                $('.jersey-modal').modal('hide');
            }
        };
    },

    editJersey: function(id)
    {
        var modal = $('#model-modal').clone();
        var body = modal.find('.modal-body').empty();
        
        modal.find('.modal-title').html('Edit Silk');

        var url = Routing.generate('jersey_edit', {id: id});

        var urlEdit = Routing.generate('jersey', {id: id});
        window.history.pushState(id, 'Jersey Edit', urlEdit);

        body.empty();

        Message.setFadeOut(false).msgInfo('Loading...', body, false);

        modal.addClass('jersey-modal').attr('width', '90%');
        modal.modal('show');//.on('hide.bs.modal', function(){ modal.remove(); });
        body.load(url,
            function()
            {
                //modal.scrollTop(0)
                JockerColour.Jersey.configEditJersey();
            }
        );

        modal.on(
            'hide.bs.modal',
            function()
            {
                modal.remove();
                var race = $("#race").val();
                var url = Routing.generate('jersey_race', {id:race});
                window.history.pushState(null, 'Jersey Listing', url);
            }
        );
    },

    configEditJersey: function()
    {
        this.initEditJerseyForm();
        this.initPatternControl();
        this.initColorControl();
        this.initColorAdd();
        this.initColorRemove();
        this.initReorderControl();
        this.initModalErrorJersey();
        this.initColourSelect();
        this.initPatternSelect();
        this.initStringRefresh();
        this.initCopyJersey();
        this.initAddSynonym();
    },

    initAddSynonym: function()
    {
        $(document).off('click.jersey').on(
            'click.jersey',
            '.error-item b',
            function()
            {
                var term = $(this).html();
                $(this).closest('.modal').modal('hide');

                var settings = {
                    url: Routing.generate('synonym_new'), 
                    callback: JockerColour.Jersey.configSynonymForm,
                    term: term
                };

                JockerColour.Synonym.modalSynonym(settings);
            }
        );
    },

    configSynonymForm: function(modal, settings)
    {
        var form  = $( 'form', modal );

        form.find('input').eq(0).val(settings.term);

        $('.btn.reset', form).on(
            'click',
            function()
            {
                modal.modal('hide');
            }
        );

        submit = function()
        {
            var obj = {
                callback: JockerColour.Jersey.afterSaveSynonym
            };

            Form.submitAjax( form, obj );
            return false;
        };

        Form.addValidate(form, submit);
    },

    afterSaveSynonym: function(response)
    {
        if (!response.status)
            return;

        $(response.form).closest('.modal').modal('hide');

        var message = 'You have just added the synonym <b>' + response.data.term
                    + '</b> for the ' + response.data.type
                    + ' <b>' + response.data.original
                    + '</b>. Do you want to refresh the silk?';

        bootbox.confirm(message, 
            function(result) 
            {
                if (!result)  return true;


                $('#refresh-jersey').trigger('click');
            }
        );
    },

    initCopyJersey: function()
    {
        $('a.copy-similar')
            .off('click.jersey')
            .on(
                'click.jersey',
                function()
                {
                    var from = $(this).data('from');
                    var to = $(this).data('to');
                    
                    bootbox.confirm("Are you sure you want to copy it? This whole silk will be overridden.", 
                        function(result) 
                        {
                            if ( !result ) return true;
                        
                            JockerColour.Jersey.copySilk(from, to);
                        }
                    );
                }
        );
    },

    copySilk: function(from, to)
    {
        var form = $('form');

        var obj = {
            callback: JockerColour.Jersey.afterEditJersey,
            action: Routing.generate('jersey_copy', {from: from, to: to})
        };

        form.closest('.modal').scrollTop(0);
        Form.submitAjax( form, obj );
    },

    initStringRefresh: function()
    {
        $('button#refresh-jersey')
            .off('click.jersey')
            .on(
                'click.jersey',
                function()
                {
                    var btnRefresh = $(this);
                    var id = $(this).data('id');
                    var form = $(this).closest('form');

                    btnRefresh.attr('disabled', true);

                    var obj = {
                        callback: JockerColour.Jersey.afterEditJersey,
                        callbackError: function()
                        {
                            btnRefresh.removeAttr('disabled');
                        },
                        action: Routing.generate('jersey_refresh', {id: id})
                    };

                    form.closest('.modal').scrollTop(0);
                    Form.submitAjax( form, obj );
                }
        );
    },

    initColourSelect: function()
    {
        var format = function(o)
        {
            color = null;
            if ( !General.empty(JockerColour.Jersey.colours[o.id]) ) {
                color = JockerColour.Jersey.colours[o.id];
            }

            if ( !color ) {
                return o.text;
            }
            else {
                return "<span class='colour-square square-select' style='background-color: " + color + "'></span> " + o.text;
            }
        };

        $("select.color-select:not(.select2-hidden-accessible)").select2(
            {
                ajax: {
                    url: Routing.generate('getcoloursentities'),
                    dataType: 'json',
                    delay: 0,
                    data: function (params)
                    {
                        return {
                            term: params.term, // search term
                            page: params.page
                        };
                    },
                    processResults: function (data, page)
                    {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                allowClear: true,
                templateResult: format,
                templateSelection: format,
                minimumInputLength: 3,
                required: false,
                placeholder: '-- Select a colour --',
                escapeMarkup: function(m) { return m; }
            }
        );
    },

    initPatternSelect: function()
    {
        $("select:not(.color-select):not(.select2-hidden-accessible)").each(
            function()
            {
                $(this).select2(
                    {
                        allowClear: true,
                        required: false,
                        placeholder: '-- Select a pattern --'
                    }
                );
            }
        );
    },

    initModalErrorJersey: function()
    {
        $("#modalError .btn-close").on('click',
            function()
            {
                $('#modalError').modal('hide');
            }
        );
    },

    afterEditJersey: function(response)
    {
        var form = $('form');
        var id = response.id;

        $('button#refresh-jersey').removeAttr('disabled');
        form.find('.save-jersey').removeAttr('disabled');

        if ( !response.status )
            return false;

        if ( response.can_approve )
            $('.btn-race-' + response.id_race).removeAttr('disabled');
        else
            $('.btn-race-' + response.id_race).attr('disabled', true);

        $('textarea', form).val(response.livery);
        $('.detail-' + id).html(response.livery).parent().attr('title', response.livery);

        JockerColour.Jersey.reloadImagesJersey(response);
        JockerColour.Jersey.reloadErrorsJersey(response);

        $('.elements-container').html(response.elements);

        JockerColour.Jersey.configEditJersey();
        JockerColour.Jersey.sizeColumns();
    },

    reloadErrorsJersey: function(response)
    {
        //this.initModalErrorJersey();

        var classErrorFlag = '.silk-container-' + response.id;
        $(classErrorFlag).removeClass('error').removeClass('success').removeClass('warning');

        if ( response.has_errors ) {

            $(classErrorFlag).addClass('error');

            $('.error-btn').removeClass('hide');
            $('#error-container').html(response.errors);

            var newTooltip = "There are some errors on the silk matching.";

        } else {

            $(classErrorFlag).addClass('warning');

            $('.error-btn').addClass('hide');
            $('.error-container').empty();

            var newTooltip = "Silk has matched patterns and colours.";
        }

        $(classErrorFlag).tooltip('hide')
                        .attr('data-original-title', newTooltip)
                        .tooltip('fixTitle')
                        .tooltip('show');
    },

    reloadImagesJersey: function(response)
    {
        var id = response.id;
        var imgFront = '/' + response.front_view + '?' + (new Date().getTime());
        var imgSide = '/' + response.side_view + '?' + (new Date().getTime());

        General.waitTo(
            function()
            {
                $('img', '.jersey-thumb-' + id).each(
                    function()
                    {
                        var img = $(this).hasClass('side') ? imgSide : imgFront;
                        var src = $(this).attr('src', img);
                    }
                );
            },
            2
        );
    },

    initEditJerseyForm: function()
    {
        var form  = $( 'form' );

        submit = function()
        {
            form.find('.save-jersey').attr('disabled', true);

            var obj = {
                callback: JockerColour.Jersey.afterEditJersey,
                callbackError: function()
                {
                    form.find('.save-jersey').removeAttr('disabled');
                }
            };

            form.closest('.modal').scrollTop(0);
            Form.submitAjax( form, obj );
            return false;
        };

        Form.addValidate(form, submit);
    },


    initPatternControl: function()
    {
        $('.pattern-control select')
            .off('change.jersey')
            .on(
                'change.jersey',
                function()
                {
                    var id = $(this).val();
                    var patternColors = $(this).closest('.widget-content').find('.color-container');
                    if ( !id )
                        patternColors.hide().find('.color-pattern-container').empty();
                    else
                        patternColors.show();

                    JockerColour.Jersey.sizeColumns();
                }
        );

        $('.pattern-control select').trigger('change.jersey');
    },

    initColorControl: function()
    {
    	$(document).on(
    	    'change',
    	    '.color-control select',
    	    function()
    	    {
        		var id = $(this).val();
        		var color = null;

        		if ( !General.empty(JockerColour.Jersey.colours[id]) )
        		    color = JockerColour.Jersey.colours[id];

        		$(this).closest('.color-control').find('.colour-square').css('background-color', color);
    	    }
    	);
    },

    initColorAdd:function()
    {
        $('.add-colour-pattern')
            .off('click.jersey')
            .on(
                'click.jersey',
                function()
                {
                    var container = $(this).closest('.panel').find('.color-pattern-container');
                    var prototype = $(this).data('prototype');
                    var index = $(this).data('index');

                    if ( !index )
                        index = 0;

                    prototype = prototype.replace(/__name__/g, 0);
                    prototype = prototype.replace(/__colour__/g, index);

                    index++;

                    $(this).data('index', index);

                    container.append(prototype);

                    JockerColour.Jersey.setColoursOrder(container);
                    JockerColour.Jersey.initColourSelect();

                    $('.color-select:last', container).focus().select2('open');

                    JockerColour.Jersey.sizeColumns();
                }
        );
    },

    initColorRemove: function()
    {
    	$(document).on(
            'click',
            '.remove-colour',
    	    function()
    	    {
                var container = $(this).closest('.panel').find('.add-colour-pattern');

                $(this).closest('.color-control').hide().find('select').val('');

                JockerColour.Jersey.setColoursOrder(container);
                JockerColour.Jersey.sizeColumns();
    	    }
    	);
    },

    setColoursOrder: function( container )
    {
        $('[id$=order]', container ).each(
            function(index)
            {
                $(this).val(++index);
            }
        );
    },

    showJerseyPreview: function(imgSrcFront,imgSrcSide, jerseyTitle)
    {
        bootbox.dialog({
            size: 'small',
            title: jerseyTitle,
            message: '<div class="row"><div class="col-sm-6 col-md-6"><div class="thumbnail"><img class="jersey-preview" src="'+imgSrcFront+'"/><div class="caption text-center"><h4>Front</h4></div></div></div><div class="col-sm-6 col-md-6"><div class="thumbnail"><img class="jersey-preview" src="'+imgSrcSide+'"/><div class="caption text-center"><h4>Side</h4></div></div></div></div>'
        });
    },

    initReorderControl: function()
    {
        $( ".color-pattern-container" ).sortable(
            {
                  handle: ".sorter",
                  update: function( event, ui )
                  {
                        JockerColour.Jersey.setColoursOrder($(this));
                  }
            }
        );
    },
    
    showNextError: function(link)
    {
        $next = $(link).next('span');
        
        if ($next.is(':visible')) {
            $next.hide();
        } else {
            $next.show();
        }
        
    },

    sizeColumns: function()
    {
        $('.elements-container > .row').each(
            function()
            {
                var parent = $(this);
                var items = $('.same-size', parent);
                var parentSize = parent.innerWidth();

                var itemsResize = [];
                var parentIndex = 0;
                var totalSize = 0;

                items.each(
                    function()
                    {
                        var divSize = $(this).outerWidth(true) ;
                        totalSize += divSize;

                        if ( !(parentIndex in itemsResize ) )
                            itemsResize[parentIndex] = [];

                        itemsResize[parentIndex].push($(this));

                        if ( totalSize >= parentSize ) {

                            parentIndex++;
                            totalSize = 0;
                        }
                    }
                );

                $.each(
                    itemsResize,
                    function(index, items)
                    {
                        var maxHeight = -1;

                        $.each(
                            items, 
                            function(index, item)
                            {
                                var box = $('> .widget-box', this).eq(0);
                                var h = box.removeAttr('style').outerHeight(true); 
                                maxHeight = h > maxHeight ? h : maxHeight;
                            }
                        );

                        $.each(
                            items, 
                            function(index, item)
                            {
                                var box = $('> .widget-box', this).eq(0);
                                var h = box.outerHeight(); 
                                if ( h < maxHeight ) 
                                    box.height(maxHeight);
                            }
                        );
                    }
                );
            }
        );
    }
};

$( document ).ready(
    function()
    {
	   JockerColour.Jersey.init();
    }
);
