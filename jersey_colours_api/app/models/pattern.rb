class Pattern < ApplicationRecord
  belongs_to :element_type
  has_many :jersey_element_patterns, dependent: :destroy
  has_many :jersey_elements, through: :jersey_element_patterns

  validates :description, presence: true
  validates :path_svg, presence: true
  validates :status, inclusion: { in: [true, false] }

  scope :active, -> { where(status: true) }
  scope :for_element, ->(element_type) { where(element_type: element_type) }

  def to_s
    "#{description} (#{element_type.name})"
  end

  def front_view_path(fallback: true)
    path = File.join("images/patterns", slug, path_svg)
    return path if !fallback || (File.exist?(path) && File.size(path) > 0)
    "images/no-image.svg"
  end

  def side_view_path(fallback: true)
    path = File.join("images/patterns", slug, side_path_svg)
    return path if !fallback || (File.exist?(path) && File.size(path) > 0)
    "images/no-image.svg"
  end

  private

  def slug
    description.parameterize
  end
end
