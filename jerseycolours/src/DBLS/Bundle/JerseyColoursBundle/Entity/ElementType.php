<?php

namespace DBLS\Bundle\JerseyColoursBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Doctrine\Common\Collections\ArrayCollection;

/**
 * ElementType
 *
 * @ORM\Table()
 * @ORM\Entity(repositoryClass="DBLS\Bundle\JerseyColoursBundle\Entity\ElementTypeRepository")
 */
class ElementType
{

    /*
    *  1	Base
       2	Shirt
       3	Cap
       4	Peak
       5	Sleeves
       6	Colar
       7	Cuffs
       8	Tassel
       9	Pom Pom
       10	Band
       11   Seams
       12   Sash
       13   Eapulettes
       14   Crossed Sashes
    */

    const BASE = 1;
    const SHIRT = 2;
    const CAP = 3;
    const PEAK = 4;
    const SLEEVES = 5;
    const COLLAR = 6;
    const CUFFS = 7;
    const TASSEL = 8;
    const POMPOM = 9;
    const ARMBANDS = 10;
    const SEAMS = 11;
    const SASH = 12;
    const EPAULETTES = 13;
    const CROSSED_SASHES = 14;

    /**
     * @var integer
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=255)
     */
    private $name;

    /**
     * @var string
     *
     * @ORM\Column(name="description", type="string", length=255)
     */
    private $description;

    /**
     * @ORM\OneToMany(targetEntity="JerseyElement", mappedBy="element_type")
    */
    protected $elements_jersey;
    
    /**
     * @ORM\OneToMany(targetEntity="Synonym", mappedBy="element_type")
     */
    private $synonyms;

    public function __construct()
    {
        $this->jersey_element_associations = new ArrayCollection();
    }


    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set id
     *
     * @param integer $id
     * @return ElementType
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * Set name
     *
     * @param string $name
     * @return ElementType
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set description
     *
     * @param string $description
     * @return ElementType
     */
    public function setDescription($description)
    {
        $this->description = $description;

        return $this;
    }

    /**
     * Get description
     *
     * @return string
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     *
     * @return string
     */
    public function __toString()
    {
	return $this->name;
    }

    /**
     * Add elements_jersey
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElement $elementsJersey
     * @return ElementType
     */
    public function addElementsJersey(\DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElement $elementsJersey)
    {
        $this->elements_jersey[] = $elementsJersey;

        return $this;
    }

    /**
     * Remove elements_jersey
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElement $elementsJersey
     */
    public function removeElementsJersey(\DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElement $elementsJersey)
    {
        $this->elements_jersey->removeElement($elementsJersey);
    }

    /**
     * Get elements_jersey
     *
     * @return \Doctrine\Common\Collections\Collection
     */
    public function getElementsJersey()
    {
        return $this->elements_jersey;
    }

    /**
     * Add synonyms
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Synonym $synonyms
     * @return ElementType
     */
    public function addSynonym(\DBLS\Bundle\JerseyColoursBundle\Entity\Synonym $synonyms)
    {
        $this->synonyms[] = $synonyms;

        return $this;
    }

    /**
     * Remove synonyms
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Synonym $synonyms
     */
    public function removeSynonym(\DBLS\Bundle\JerseyColoursBundle\Entity\Synonym $synonyms)
    {
        $this->synonyms->removeElement($synonyms);
    }

    /**
     * Get synonyms
     *
     * @return \Doctrine\Common\Collections\Collection 
     */
    public function getSynonyms()
    {
        return $this->synonyms;
    }
}
