{% extends 'bootstrap_3_layout.html.twig' %}

{% block button_widget -%}
    {% if type == "submit" %}

	{% set label = 'Save' %}
	{% set attr = attr|merge({class: (attr.class|default('btn-primary') ~ ' btn')|trim}) %}
	{{- parent() -}}

	{% set type = 'reset' %}
	{% set label = 'Close' %}
	{% set attr = attr|merge({class: 'btn-warning btn reset'}) %}
	{{- parent() -}}
	
    {% else %}
	{{- parent() -}}
    {% endif %}
{%- endblock %}

{% block form_row -%}
{% spaceless %}
    <div class="form-group{% if (not compound or force_error|default(false)) and not valid %} has-error{% endif %}">
        {{ form_label(form) }}
        <div class="{{ block('form_group_class') }}">
            {{ form_widget(form) }}
            {{ form_errors(form) }}
            
            {% for attrname,attrvalue in attr %}
                {% if attrname == 'helper-text' %}
                    <span class="help-block">{{ attrvalue }}</span>
                {% endif %}
            {% endfor %}            
        </div>
    </div>
{% endspaceless %}
{%- endblock form_row %}
