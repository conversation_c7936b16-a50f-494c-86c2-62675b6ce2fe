<?php

namespace DBLS\Bundle\JerseyColoursBundle\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolverInterface;

use DBLS\Bundle\JerseyColoursBundle\Entity\ElementType;

class PatternType extends AbstractType
{
    protected $isUpdate = false;
    
    public function __construct($isUpdate = false) {
        $this->isUpdate = $isUpdate;
    }

    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('description', 'text', array(
                    'attr' => array('helper-text' => 'Please do not enter any element name with the pattern name. (e.g. Hooped sleeve, Checked cap)')
                )
            )
            ->add('element_type', 'entity', array(
                    'class' => 'JerseyColoursBundle:ElementType',
                    'property' => 'name',
                    'label' => 'Element',
                )
            )
	    ->add('imageFile', 'file', array(
                    'image_url' => 'pathSVG',
                    'required' => $this->isUpdate == false,
                    'attr' => array('accept'   => '.svg')
                )
            )
	    ->add('sideImageFile', 'file', array(
                    'image_url' => 'sidePathSVG',
                    'required' => $this->isUpdate == false,
                    'attr' => array('accept'   => '.svg')
                )
            )
            ->add('status', 'checkbox', array( 'required' => false ))
	    ->add('save', 'submit', array( 'attr' => array( 'data-loading-text' => "Loading..." ) ) );
    }
    
    /**
     * @param OptionsResolverInterface $resolver
     */
    public function setDefaultOptions(OptionsResolverInterface $resolver)
    {
        $resolver->setDefaults(array(
            'data_class' => 'DBLS\Bundle\JerseyColoursBundle\Entity\Pattern'
        ));
    }

    /**
     * @return string
     */
    public function getName()
    {
        return 'dbls_bundle_jerseycoloursbundle_pattern';
    }
}
