Rails.application.routes.draw do
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # API routes
  namespace :api do
    namespace :v1 do
      # Authentication routes
      post 'auth/login', to: 'auth#login'
      post 'auth/register', to: 'auth#register'
      get 'auth/me', to: 'auth#me'

      # Jersey routes
      resources :jerseys do
        member do
          post :parse
          get 'svg/:view', to: 'jerseys#generate_svg', as: :svg
        end
        collection do
          post :bulk_parse
        end
      end

      # Core resource routes
      resources :colours
      resources :patterns
      resources :element_types, only: [:index, :show]
      resources :races
      resources :meetings
      resources :trainers
      resources :synonyms
      resources :errors, only: [:index, :show, :destroy]

      # Import/Export routes
      post 'import', to: 'import#create'
      get 'export/jerseys', to: 'export#jerseys'
    end
  end

  # Defines the root path route ("/")
  root "rails/health#show"
end
