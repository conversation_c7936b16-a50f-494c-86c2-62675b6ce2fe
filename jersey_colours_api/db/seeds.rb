# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).

puts "Seeding database..."

# Create Element Types
element_types = [
  { id: 1, name: "base" },
  { id: 2, name: "shirt" },
  { id: 3, name: "cap" },
  { id: 4, name: "peak" },
  { id: 5, name: "sleeves" },
  { id: 6, name: "collar" },
  { id: 7, name: "cuffs" },
  { id: 8, name: "tassel" },
  { id: 9, name: "pompom" },
  { id: 10, name: "armbands" },
  { id: 11, name: "seams" },
  { id: 12, name: "sash" },
  { id: 13, name: "epaulettes" },
  { id: 14, name: "crossed sashes" }
]

element_types.each do |et|
  ElementType.find_or_create_by!(id: et[:id]) do |element_type|
    element_type.name = et[:name]
  end
end

puts "Created #{ElementType.count} element types"

# Create basic colors
colors = [
  { name: "red", cmyk: "0,100,100,0", hexadecimal: "#FF0000" },
  { name: "blue", cmyk: "100,100,0,0", hexadecimal: "#0000FF" },
  { name: "green", cmyk: "100,0,100,0", hexadecimal: "#00FF00" },
  { name: "yellow", cmyk: "0,0,100,0", hexadecimal: "#FFFF00" },
  { name: "black", cmyk: "0,0,0,100", hexadecimal: "#000000" },
  { name: "white", cmyk: "0,0,0,0", hexadecimal: "#FFFFFF" },
  { name: "orange", cmyk: "0,50,100,0", hexadecimal: "#FF8000" },
  { name: "purple", cmyk: "50,100,0,0", hexadecimal: "#8000FF" },
  { name: "pink", cmyk: "0,50,0,0", hexadecimal: "#FF80FF" },
  { name: "brown", cmyk: "30,70,100,15", hexadecimal: "#8B4513" },
  { name: "grey", cmyk: "0,0,0,50", hexadecimal: "#808080" },
  { name: "maroon", cmyk: "0,100,100,50", hexadecimal: "#800000" },
  { name: "navy", cmyk: "100,100,0,50", hexadecimal: "#000080" },
  { name: "royal blue", cmyk: "100,75,0,0", hexadecimal: "#4169E1" },
  { name: "emerald green", cmyk: "85,0,100,0", hexadecimal: "#50C878" }
]

colors.each do |color_data|
  Colour.find_or_create_by!(name: color_data[:name]) do |colour|
    colour.cmyk = color_data[:cmyk]
    colour.hexadecimal = color_data[:hexadecimal]
  end
end

puts "Created #{Colour.count} colours"

# Create basic patterns
patterns = [
  { description: "stripes", element_type_id: 2, status: true, path_svg: "stripes.svg", side_path_svg: "stripes_side.svg" },
  { description: "spots", element_type_id: 2, status: true, path_svg: "spots.svg", side_path_svg: "spots_side.svg" },
  { description: "diamonds", element_type_id: 2, status: true, path_svg: "diamonds.svg", side_path_svg: "diamonds_side.svg" },
  { description: "hoops", element_type_id: 2, status: true, path_svg: "hoops.svg", side_path_svg: "hoops_side.svg" },
  { description: "chevrons", element_type_id: 2, status: true, path_svg: "chevrons.svg", side_path_svg: "chevrons_side.svg" },
  { description: "stars", element_type_id: 2, status: true, path_svg: "stars.svg", side_path_svg: "stars_side.svg" }
]

patterns.each do |pattern_data|
  Pattern.find_or_create_by!(description: pattern_data[:description], element_type_id: pattern_data[:element_type_id]) do |pattern|
    pattern.status = pattern_data[:status]
    pattern.path_svg = pattern_data[:path_svg]
    pattern.side_path_svg = pattern_data[:side_path_svg]
  end
end

puts "Created #{Pattern.count} patterns"

# Create admin user
admin_user = User.find_or_create_by!(email: "<EMAIL>") do |user|
  user.first_name = "Admin"
  user.last_name = "User"
  user.password = "password123"
  user.role = "admin"
end

puts "Created admin user: #{admin_user.email}"

puts "Database seeding completed!"
