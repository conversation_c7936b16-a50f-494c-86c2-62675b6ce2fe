<?php

namespace DBLS\Bundle\JerseyColoursBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use DBLS\Bundle\JerseyColoursBundle\Adapters\FileAdapter;

class InstallLevenshteinCommand extends ContainerAwareCommand
{
    protected $container;

    /**
     * 
     */
    protected function configure()
    {
        $this->setName('jersey:install:levenshtein')
             ->setDescription('Create the levenshtein in the database');
    }

    /**
     * 
     * @param  InputInterface  $input
     * @param  OutputInterface $output
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->container = $this->getContainer();

        $contents = $this->getProcedureContents($output);
        $this->createStoredProcedure($contents);
    }

    /**
     * 
     * @param  string $contents
     */
    protected function createStoredProcedure($contents)
    {
        $stmt = $this->container->get('doctrine.orm.entity_manager')->getConnection()->prepare($contents);
        $stmt->execute();
    }

    /**
     * 
     * @return string
     */
    protected function getProcedureContents()
    {
        $rootDir = $this->container->get('kernel')->getRootDir();
        $file = realpath($rootDir . '/../../documentation/functions.sql');

        return file_get_contents($file);
    }
}