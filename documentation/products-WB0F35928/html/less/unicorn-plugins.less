/** Plugins Overrides and fixes **/

/* jQ<PERSON>y <PERSON> */
.jqstooltip{
   width: auto !important;
   height: auto !important;
   padding: 2px 6px !important;
   background-color: rgba(0,0,0,0.7) !important;
   border: 0 !important;
   border-radius: 3px;
}

/* NiceScroll */
.nicescroll-rails {
	background-color: transparent !important;
    border-radius: 5px;
    margin-right: 2px;
    .transition(all 0.2s ease 0s);
    z-index: 1030;
    bottom: 0 !important;
    height: 200% !important;
	> div {
	    background-color: rgba(140, 140, 140, 0.7) !important;
	    border: 0 none !important;
	    .transition(all 0.2s ease 0s);
	}
	@media(min-width: @screen-sm) {
		display: none !important;
	}
}

/* jQuery UI Overrides */
.ui-spinner {
	border: 0;
	.ui-spinner-up, .ui-spinner-down {
		border-right: 1px solid #CCCCCC !important;
		border-bottom: 1px solid #ccc !important;
	}
	.ui-spinner-up {
		border-top: 1px solid #CCCCCC !important;
	}
}
.ui-spinner-input {
	margin: 0;
}

/* FontAwesome Demo */
.the-icons a {
	color: @gray-dark;
	display: block;
	padding: 5px 10px;
	height: 42px;
	line-height: 32px;
	i {
		font-size: 16px;
		margin-right: 10px;
		text-align: right;
		width: 32px;
		display: inline-block;
		vertical-align: middle;
	}
	&:hover {
		background-color: @gray-lighter;
		vertical-align: middle;
		i {
			font-size: 30px;
		}
	}
}
