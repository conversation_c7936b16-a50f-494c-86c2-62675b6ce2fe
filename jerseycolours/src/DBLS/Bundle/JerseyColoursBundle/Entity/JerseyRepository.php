<?php

namespace DBLS\Bundle\JerseyColoursBundle\Entity;

use Doctrine\ORM\EntityRepository;
use DBLS\Bundle\JerseyColoursBundle\Entity\Jersey;

/**
 * JerseyRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class JerseyRepository extends EntityRepository
{

    /**
     * 
     * @param  array  $data
     * @return mixed
     */
    public function findJerseyByRace( array $data )
    {
        $qb = $this->createQueryBuilder("j");
        $qb ->innerJoin('JerseyColoursBundle:Race', 'r', 'WITH', 'r.id = j.race')
            ->innerJoin('JerseyColoursBundle:Meeting', 'm', 'WITH', 'm.id = r.meeting')
            ->where($qb->expr()->andX(
                $qb->expr()->eq($qb->expr()->lower($qb->expr()->trim('j.name')), ":name"),
                $qb->expr()->eq('r.id', ':pRace'),
                $qb->expr()->eq('m.id', ':pMeeting')
            ))
            ->distinct()
            ->setParameter('name', trim(strtolower($data['name'])))
            ->setParameter('pRace',$data['race'])
            ->orderBy('j.id', 'DESC')
            ->setMaxResults(1)
            ->setParameter('pMeeting',$data['meeting']);
        
        $q = $qb->getQuery();     
        
        try {
            return $q->getOneOrNullResult();
        } catch (NoResultException $ex) {
            return null;
        }
        
    }

    /**
     * 
     * @param  array  $data
     * @return mixed
     */
    public function findJerseyByTrainer( array $data )
    {
        $qb = $this->createQueryBuilder("j");
        $qb ->innerJoin('JerseyColoursBundle:Race', 'r', 'WITH', 'r.id = j.race')
            ->where($qb->expr()->andX(
                $qb->expr()->eq($qb->expr()->lower($qb->expr()->trim('j.name')), ":name"),
                $qb->expr()->eq('j.trainer', ':pTrainer'),
                $qb->expr()->eq('r.id', ':pRace')
            ))
            ->distinct()
            ->setParameter('name', trim(strtolower($data['name'])))
            ->setParameter('pTrainer',$data['trainer'])
            ->setParameter('pRace',$data['race'])
            ->orderBy('j.id', 'DESC')
            ->setMaxResults(1);
        
        $q = $qb->getQuery();     
        
        try {
            return $q->getOneOrNullResult();
        } catch (NoResultException $ex) {
            return null;
        }
        
    }

    /**
     * Find the pending jerseys to be parsed
     * @param  integer $batchSize
     * @return Doctrine\ORM\AbstractQuery
     */
    public function findPendingToParse( $batchSize = 200 )
    {
        $now = new \DateTime();
        $fiveMinutesAgo = $now->sub(new \DateInterval("PT5M"));

        $qb = $this->createQueryBuilder('j');
        $qb = $qb->where(
                    $qb->expr()->andX(
                        $qb->expr()->eq('j.status', ':unapproved'),
                        $qb->expr()->eq('j.parsed', ':not_parsed'),
                        $qb->expr()->orX(
                            $qb->expr()->eq('j.parsing', ':not_parsing'),
                            $qb->expr()->lt('j.parsingTime', ':five_minutes')
                        )
                    )
                )
                ->orderBy('j.id', 'ASC')
                ->setParameter('unapproved', 0)
                ->setParameter('not_parsed', 0)
                ->setParameter('not_parsing', 0)
                ->setParameter('five_minutes', $fiveMinutesAgo)
                ->setMaxResults( $batchSize );

        return $qb->getQuery()->getResult();
    }

    /**
     * 
     * @param  Jersey $jersey
     * @return Jersey|mixed
     */
    public function findSimilarJersey(Jersey $jersey)
    {
        $qb = $this->createQueryBuilder('j');

        $qb = $qb->where(
                    $qb->expr()->andX(
                        $qb->expr()->eq('j.status', ':approved'),
                        $qb->expr()->eq('j.trainer', ':trainer'),
                        $qb->expr()->gte('LEVENSHTEIN_RATIO(j.stringValue, :original)', ':ratio')
                    )
                )
                ->addSelect('LEVENSHTEIN_RATIO(j.stringValue, :original) as HIDDEN lev')
                ->orderBy('j.id', 'DESC')
                ->setParameter('approved', 1)
                ->setParameter('ratio', 100)
                ->setParameter('original', $jersey->getStringValue()) 
                ->setMaxResults(1)
                ->setParameter('trainer', $jersey->getTrainer());

        return $qb->getQuery()->getOneOrNullResult();
    }
}
