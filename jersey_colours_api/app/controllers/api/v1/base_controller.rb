# frozen_string_literal: true

module Api
  module V1
    class BaseController < ApplicationController
      include JsonApiSerialization

      before_action :authenticate_user!
      
      rescue_from ActiveRecord::RecordNotFound, with: :not_found
      rescue_from ActiveRecord::RecordInvalid, with: :unprocessable_entity
      rescue_from ActionController::ParameterMissing, with: :bad_request

      private

      def authenticate_user!
        token = request.headers['Authorization']&.split(' ')&.last
        return unauthorized unless token

        begin
          decoded_token = JWT.decode(token, Rails.application.secret_key_base, true, { algorithm: 'HS256' })
          user_id = decoded_token[0]['user_id']
          @current_user = User.find(user_id)
        rescue JWT::DecodeError, ActiveRecord::RecordNotFound
          unauthorized
        end
      end

      def current_user
        @current_user
      end

      def not_found(exception = nil)
        render json: { error: 'Resource not found' }, status: :not_found
      end

      def unprocessable_entity(exception)
        render json: { 
          error: 'Validation failed', 
          details: exception.record.errors.full_messages 
        }, status: :unprocessable_entity
      end

      def bad_request(exception = nil)
        render json: { error: 'Bad request' }, status: :bad_request
      end

      def unauthorized
        render json: { error: 'Unauthorized' }, status: :unauthorized
      end

      def forbidden
        render json: { error: 'Forbidden' }, status: :forbidden
      end

      def paginate(collection)
        collection.page(params[:page]).per(params[:per_page] || 25)
      end

      def render_success(data = nil, message = 'Success', status = :ok)
        response = { success: true, message: message }
        response[:data] = data if data
        render json: response, status: status
      end

      def render_error(message = 'Error', status = :unprocessable_entity, details = nil)
        response = { success: false, error: message }
        response[:details] = details if details
        render json: response, status: status
      end
    end
  end
end
