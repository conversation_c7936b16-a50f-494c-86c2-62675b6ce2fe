import r,{createContext as l,useContext as d}from"react";let n=l(null);n.displayName="OpenClosedContext";var i=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(i||{});function u(){return d(n)}function c({value:o,children:t}){return r.createElement(n.Provider,{value:o},t)}function s({children:o}){return r.createElement(n.Provider,{value:null},o)}export{c as OpenClosedProvider,s as ResetOpenClosedProvider,i as State,u as useOpenClosed};
