# frozen_string_literal: true

class JerseyElementSerializer
  include JSONAPI::Serializer

  attributes :order, :created_at, :updated_at

  belongs_to :element_type, serializer: ElementTypeSerializer
  belongs_to :colour, serializer: ColourSerializer
  has_many :jersey_element_patterns, serializer: JerseyElementPatternSerializer

  attribute :string_representation do |jersey_element|
    jersey_element.to_string
  end
end
