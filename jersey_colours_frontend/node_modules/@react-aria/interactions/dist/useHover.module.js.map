{"mappings": ";;;AAAA;;;;;;;;;;CAUC,GAED,kEAAkE;AAClE,2DAA2D;AAC3D,yDAAyD;AACzD,kHAAkH;;;AAiBlH,oGAAoG;AACpG,iFAAiF;AACjF,sDAAsD;AACtD,IAAI,wDAAkC;AACtC,IAAI,mCAAa;AAEjB,SAAS;IACP,wDAAkC;IAElC,wFAAwF;IACxF,sFAAsF;IACtF,wFAAwF;IACxF,oEAAoE;IACpE,WAAW;QACT,wDAAkC;IACpC,GAAG;AACL;AAEA,SAAS,+CAAyB,CAAC;IACjC,IAAI,EAAE,WAAW,KAAK,SACpB;AAEJ;AAEA,SAAS;IACP,IAAI,OAAO,aAAa,aACtB;IAGF,IAAI,OAAO,iBAAiB,aAC1B,SAAS,gBAAgB,CAAC,aAAa;SAClC,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,QAClC,SAAS,gBAAgB,CAAC,YAAY;IAGxC;IACA,OAAO;QACL;QACA,IAAI,mCAAa,GACf;QAGF,IAAI,OAAO,iBAAiB,aAC1B,SAAS,mBAAmB,CAAC,aAAa;aACrC,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,QAClC,SAAS,mBAAmB,CAAC,YAAY;IAE7C;AACF;AAMO,SAAS,0CAAS,KAAiB;IACxC,IAAI,gBACF,YAAY,iBACZ,aAAa,cACb,UAAU,cACV,UAAU,EACX,GAAG;IAEJ,IAAI,CAAC,WAAW,WAAW,GAAG,CAAA,GAAA,eAAO,EAAE;IACvC,IAAI,QAAQ,CAAA,GAAA,aAAK,EAAE;QACjB,WAAW;QACX,2BAA2B;QAC3B,aAAa;QACb,QAAQ;IACV,GAAG,OAAO;IAEV,CAAA,GAAA,gBAAQ,EAAE,8CAAwB,EAAE;IACpC,IAAI,qBAAC,iBAAiB,4BAAE,wBAAwB,EAAC,GAAG,CAAA,GAAA,yBAAiB;IAErE,IAAI,cAAC,UAAU,mBAAE,eAAe,EAAC,GAAG,CAAA,GAAA,cAAM,EAAE;QAC1C,IAAI,oBAAoB,CAAC,OAAO;YAC9B,MAAM,WAAW,GAAG;YACpB,IAAI,cAAc,gBAAgB,WAAW,MAAM,SAAS,IAAI,CAAC,MAAM,aAAa,CAAC,QAAQ,CAAC,MAAM,MAAM,GACxG;YAGF,MAAM,SAAS,GAAG;YAClB,IAAI,SAAS,MAAM,aAAa;YAChC,MAAM,MAAM,GAAG;YAEf,kGAAkG;YAClG,gGAAgG;YAChG,kFAAkF;YAClF,yGAAyG;YACzG,kBAAkB,CAAA,GAAA,uBAAe,EAAE,MAAM,MAAM,GAAG,eAAe,CAAA;gBAC/D,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,IAAI,CAAC,CAAA,GAAA,mBAAW,EAAE,MAAM,MAAM,EAAE,EAAE,MAAM,GACzE,gBAAgB,GAAG,EAAE,WAAW;YAEpC,GAAG;gBAAC,SAAS;YAAI;YAEjB,IAAI,cACF,aAAa;gBACX,MAAM;wBACN;6BACA;YACF;YAGF,IAAI,eACF,cAAc;YAGhB,WAAW;QACb;QAEA,IAAI,kBAAkB,CAAC,OAAO;YAC5B,IAAI,SAAS,MAAM,MAAM;YACzB,MAAM,WAAW,GAAG;YACpB,MAAM,MAAM,GAAG;YAEf,IAAI,gBAAgB,WAAW,CAAC,MAAM,SAAS,IAAI,CAAC,QAClD;YAGF,MAAM,SAAS,GAAG;YAClB;YAEA,IAAI,YACF,WAAW;gBACT,MAAM;wBACN;6BACA;YACF;YAGF,IAAI,eACF,cAAc;YAGhB,WAAW;QACb;QAEA,IAAI,aAA4B,CAAC;QAEjC,IAAI,OAAO,iBAAiB,aAAa;YACvC,WAAW,cAAc,GAAG,CAAC;gBAC3B,IAAI,yDAAmC,EAAE,WAAW,KAAK,SACvD;gBAGF,kBAAkB,GAAG,EAAE,WAAW;YACpC;YAEA,WAAW,cAAc,GAAG,CAAC;gBAC3B,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GAClD,gBAAgB,GAAG,EAAE,WAAW;YAEpC;QACF,OAAO,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,QAAQ;YAC1C,WAAW,YAAY,GAAG;gBACxB,MAAM,yBAAyB,GAAG;YACpC;YAEA,WAAW,YAAY,GAAG,CAAC;gBACzB,IAAI,CAAC,MAAM,yBAAyB,IAAI,CAAC,uDACvC,kBAAkB,GAAG;gBAGvB,MAAM,yBAAyB,GAAG;YACpC;YAEA,WAAW,YAAY,GAAG,CAAC;gBACzB,IAAI,CAAC,cAAc,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GAClD,gBAAgB,GAAG;YAEvB;QACF;QACA,OAAO;wBAAC;6BAAY;QAAe;IACrC,GAAG;QAAC;QAAc;QAAe;QAAY;QAAY;QAAO;QAAmB;KAAyB;IAE5G,CAAA,GAAA,gBAAQ,EAAE;QACR,iEAAiE;QACjE,qFAAqF;QACrF,IAAI,YACF,gBAAgB;YAAC,eAAe,MAAM,MAAM;QAAA,GAAG,MAAM,WAAW;IAEpE,uDAAuD;IACvD,GAAG;QAAC;KAAW;IAEf,OAAO;oBACL;mBACA;IACF;AACF", "sources": ["packages/@react-aria/interactions/src/useHover.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from react.\n// Original licensing for the following can be found in the\n// NOTICE file in the root directory of this source tree.\n// See https://github.com/facebook/react/tree/cc7c1aece46a6b69b41958d731e0fd27c94bfc6c/packages/react-interactions\n\nimport {DOMAttributes, HoverEvents} from '@react-types/shared';\nimport {getOwnerDocument, nodeContains, useGlobalListeners} from '@react-aria/utils';\nimport {useEffect, useMemo, useRef, useState} from 'react';\n\nexport interface HoverProps extends HoverEvents {\n  /** Whether the hover events should be disabled. */\n  isDisabled?: boolean\n}\n\nexport interface HoverResult {\n  /** Props to spread on the target element. */\n  hoverProps: DOMAttributes,\n  isHovered: boolean\n}\n\n// iOS fires onPointerEnter twice: once with pointerType=\"touch\" and again with pointerType=\"mouse\".\n// We want to ignore these emulated events so they do not trigger hover behavior.\n// See https://bugs.webkit.org/show_bug.cgi?id=214609.\nlet globalIgnoreEmulatedMouseEvents = false;\nlet hoverCount = 0;\n\nfunction setGlobalIgnoreEmulatedMouseEvents() {\n  globalIgnoreEmulatedMouseEvents = true;\n\n  // Clear globalIgnoreEmulatedMouseEvents after a short timeout. iOS fires onPointerEnter\n  // with pointerType=\"mouse\" immediately after onPointerUp and before onFocus. On other\n  // devices that don't have this quirk, we don't want to ignore a mouse hover sometime in\n  // the distant future because a user previously touched the element.\n  setTimeout(() => {\n    globalIgnoreEmulatedMouseEvents = false;\n  }, 50);\n}\n\nfunction handleGlobalPointerEvent(e) {\n  if (e.pointerType === 'touch') {\n    setGlobalIgnoreEmulatedMouseEvents();\n  }\n}\n\nfunction setupGlobalTouchEvents() {\n  if (typeof document === 'undefined') {\n    return;\n  }\n\n  if (typeof PointerEvent !== 'undefined') {\n    document.addEventListener('pointerup', handleGlobalPointerEvent);\n  } else if (process.env.NODE_ENV === 'test') {\n    document.addEventListener('touchend', setGlobalIgnoreEmulatedMouseEvents);\n  }\n\n  hoverCount++;\n  return () => {\n    hoverCount--;\n    if (hoverCount > 0) {\n      return;\n    }\n\n    if (typeof PointerEvent !== 'undefined') {\n      document.removeEventListener('pointerup', handleGlobalPointerEvent);\n    } else if (process.env.NODE_ENV === 'test') {\n      document.removeEventListener('touchend', setGlobalIgnoreEmulatedMouseEvents);\n    }\n  };\n}\n\n/**\n * Handles pointer hover interactions for an element. Normalizes behavior\n * across browsers and platforms, and ignores emulated mouse events on touch devices.\n */\nexport function useHover(props: HoverProps): HoverResult {\n  let {\n    onHoverStart,\n    onHoverChange,\n    onHoverEnd,\n    isDisabled\n  } = props;\n\n  let [isHovered, setHovered] = useState(false);\n  let state = useRef({\n    isHovered: false,\n    ignoreEmulatedMouseEvents: false,\n    pointerType: '',\n    target: null\n  }).current;\n\n  useEffect(setupGlobalTouchEvents, []);\n  let {addGlobalListener, removeAllGlobalListeners} = useGlobalListeners();\n\n  let {hoverProps, triggerHoverEnd} = useMemo(() => {\n    let triggerHoverStart = (event, pointerType) => {\n      state.pointerType = pointerType;\n      if (isDisabled || pointerType === 'touch' || state.isHovered || !event.currentTarget.contains(event.target)) {\n        return;\n      }\n\n      state.isHovered = true;\n      let target = event.currentTarget;\n      state.target = target;\n\n      // When an element that is hovered over is removed, no pointerleave event is fired by the browser,\n      // even though the originally hovered target may have shrunk in size so it is no longer hovered.\n      // However, a pointerover event will be fired on the new target the mouse is over.\n      // In Chrome this happens immediately. In Safari and Firefox, it happens upon moving the mouse one pixel.\n      addGlobalListener(getOwnerDocument(event.target), 'pointerover', e => {\n        if (state.isHovered && state.target && !nodeContains(state.target, e.target as Element)) {\n          triggerHoverEnd(e, e.pointerType);\n        }\n      }, {capture: true});\n\n      if (onHoverStart) {\n        onHoverStart({\n          type: 'hoverstart',\n          target,\n          pointerType\n        });\n      }\n\n      if (onHoverChange) {\n        onHoverChange(true);\n      }\n\n      setHovered(true);\n    };\n\n    let triggerHoverEnd = (event, pointerType) => {\n      let target = state.target;\n      state.pointerType = '';\n      state.target = null;\n\n      if (pointerType === 'touch' || !state.isHovered || !target) {\n        return;\n      }\n\n      state.isHovered = false;\n      removeAllGlobalListeners();\n\n      if (onHoverEnd) {\n        onHoverEnd({\n          type: 'hoverend',\n          target,\n          pointerType\n        });\n      }\n\n      if (onHoverChange) {\n        onHoverChange(false);\n      }\n\n      setHovered(false);\n    };\n\n    let hoverProps: DOMAttributes = {};\n\n    if (typeof PointerEvent !== 'undefined') {\n      hoverProps.onPointerEnter = (e) => {\n        if (globalIgnoreEmulatedMouseEvents && e.pointerType === 'mouse') {\n          return;\n        }\n\n        triggerHoverStart(e, e.pointerType);\n      };\n\n      hoverProps.onPointerLeave = (e) => {\n        if (!isDisabled && e.currentTarget.contains(e.target as Element)) {\n          triggerHoverEnd(e, e.pointerType);\n        }\n      };\n    } else if (process.env.NODE_ENV === 'test') {\n      hoverProps.onTouchStart = () => {\n        state.ignoreEmulatedMouseEvents = true;\n      };\n\n      hoverProps.onMouseEnter = (e) => {\n        if (!state.ignoreEmulatedMouseEvents && !globalIgnoreEmulatedMouseEvents) {\n          triggerHoverStart(e, 'mouse');\n        }\n\n        state.ignoreEmulatedMouseEvents = false;\n      };\n\n      hoverProps.onMouseLeave = (e) => {\n        if (!isDisabled && e.currentTarget.contains(e.target as Element)) {\n          triggerHoverEnd(e, 'mouse');\n        }\n      };\n    }\n    return {hoverProps, triggerHoverEnd};\n  }, [onHoverStart, onHoverChange, onHoverEnd, isDisabled, state, addGlobalListener, removeAllGlobalListeners]);\n\n  useEffect(() => {\n    // Call the triggerHoverEnd as soon as isDisabled changes to true\n    // Safe to call triggerHoverEnd, it will early return if we aren't currently hovering\n    if (isDisabled) {\n      triggerHoverEnd({currentTarget: state.target}, state.pointerType);\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isDisabled]);\n\n  return {\n    hoverProps,\n    isHovered\n  };\n}\n"], "names": [], "version": 3, "file": "useHover.module.js.map"}