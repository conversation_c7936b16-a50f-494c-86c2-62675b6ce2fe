<?php

namespace DBLS\Bundle\JerseyColoursBundle\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolverInterface;
use Doctrine\ORM\EntityRepository;

use DBLS\Bundle\JerseyColoursBundle\Form\PatternColourType;
use DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElement;

class JerseyElementPatternType extends AbstractType
{
    /**
     * @var JerseyElement $jerseyElement
     */
    protected $jerseyElement;

    public function __construct(JerseyElement $jerseyElement)
    {
        $this->jerseyElement = $jerseyElement;
    }

    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $elementType = $this->jerseyElement->getElementType();

        $builder->add(
                    'pattern', 
                    'entity', 
                    array(
                        'class'       => 'DBLS\Bundle\JerseyColoursBundle\Entity\Pattern',
                        'required'    => false,
                        //'placeholder' => '-- Select a pattern --',
                        'empty_data'  => null,
                        'query_builder' => function (EntityRepository $er) use ($elementType) {
                            $qb = $er->createQueryBuilder('p');
                                     
                             $qb->where(
                                 $qb->expr()->andX(
                                        $qb->expr()->eq('p.element_type', ':id'),
                                        $qb->expr()->eq('p.status', ':status')
                                )
                             )
                             ->setParameter('id', $elementType->getId())
                             ->setParameter('status', 1);

                             return $qb;
                        },
                    )
                )
                ->add(
                    'pattern_colours', 
                    'collection', 
                    array( 
                        'type' => new PatternColourType(), 
                        'allow_add'     => true, 
                        'allow_delete'  => true,
                        'prototype_name' => '__colour__' 
                    )
                );

        
    }
    
    /**
     * @param OptionsResolverInterface $resolver
     */
    public function setDefaultOptions(OptionsResolverInterface $resolver)
    {
        $resolver->setDefaults(array(
            'data_class' => 'DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElementPattern'
        ));
    }

    /**
     * @return string
     */
    public function getName()
    {
        return 'dbls_bundle_jerseycoloursbundle_element_type';
    }
}
