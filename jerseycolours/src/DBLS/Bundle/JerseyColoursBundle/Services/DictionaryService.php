<?php

namespace DBLS\Bundle\JerseyColoursBundle\Services;

use Doctrine\ORM\EntityManager;
use Symfony\Component\DependencyInjection\ContainerAware;

use DBLS\Bundle\JerseyColoursBundle\Entity\Colour;
use DBLS\Bundle\JerseyColoursBundle\Entity\Pattern;
use DBLS\Bundle\JerseyColoursBundle\Entity\ElementType;
use DBLS\Bundle\JerseyColoursBundle\Entity\Synonym;
use DBLS\Bundle\JerseyColoursBundle\StringParser\IPspellQueryable;
use DBLS\Bundle\JerseyColoursBundle\StringParser\Tokens;
use DBLS\Bundle\JerseyColoursBundle\StringParser\DictionaryParser;
use Symfony\Component\Filesystem\Filesystem;

class DictionaryService extends ContainerAware implements IPspellQueryable
{

    const FILE_COLOUR = "colour.pws";
    const FILE_PATTERN = "pattern.pws";
    const FILE_ELEMENT = "element.pws";
    const FILE_SYNONYM = "synonym.pws";

    /**
     * Holds an instance of database connection
     * @var EntityManager
     */
    private $em = null;
    
    /*
     * @var typeDictionary
     */
    private $typeDictionary;

    public function __construct(EntityManager $em) 
    {
        $this->em = $em;
    }

    /*
     * 
     * @return path to dictionaries
     */
    protected function _getPath()
    {
        $fs = new Filesystem();
        
        $rootDir = $this->container->get('kernel')->getRootDir();        
        $path = $rootDir . "/dictionary/";
        
        if ( !$fs->exists( $path ) )
            $fs->mkdir( $path );
        
        return $path;
        
    }

    /*
     * @param Dictionary File
     * @return Dictionary
     */
    protected function _getDictionary($dictionary)
    {
        //Create Dictionary        
        return pspell_new_personal ($this->_getPath() . $dictionary,
                                    "en", "", "", "", PSPELL_FAST);
    }

    /*
     * Add word in dictionary
     * @param Word
     * @return boolean
     */
    protected function _addDictionary($term)
    {
        
        switch ($this->typeDictionary){
            case Tokens::T_COLOUR :
                $dictionary = DictionaryService::FILE_COLOUR;
                break;
            case Tokens::T_PATTERN :
                $dictionary = DictionaryService::FILE_PATTERN;
                break;
            case Tokens::T_ELEMENT :
                $dictionary = DictionaryService::FILE_ELEMENT;
                break;
            case 'T_SYNONYM' :
                $dictionary = DictionaryService::FILE_SYNONYM;
                break;
        }
        
        //Get dictionary
        $pspell_link = $this->_getDictionary($dictionary);
        
        pspell_add_to_personal($pspell_link, $term);
        pspell_save_wordlist($pspell_link);
        
        return true;
        
    }

    /*
     * Add work in dictionary Colour
     * @param Entity Colour
     */
    public function saveColour(Colour $entity)
    {
        //set type dictionary
        $this->typeDictionary = Tokens::T_COLOUR;
        
        //Add term in dictionary
        $this->_addDictionary(
                    DictionaryParser::getStringFormated($entity->getName())
                );
        
    }
    
    /*
     * Add work in dictionary Pattern
     * @param Entity Pattern
     */
    public function savePattern(Pattern $entity)
    {
        //set type dictionary
        $this->typeDictionary = Tokens::T_PATTERN;
        
        //Add term in dictionary
        $this->_addDictionary(
                    DictionaryParser::getStringFormated($entity->getDescription())
                );
    }
    
    /*
     * Add work in dictionary ElementType
     * @param Entity ElementType
     */
    public function saveElement(ElementType $entity)
    {
        //set type dictionary
        $this->typeDictionary = Tokens::T_ELEMENT;
        
        //Add term in dictionary
        $this->_addDictionary(
                    DictionaryParser::getStringFormated($entity->getName()) 
                );
    }
    
    /*
     * Add work in dictionary Synonym
     * @param Entity Synonym
     */
    public function saveSynonym(Synonym $entity)
    {
        //set type dictionary
        $this->typeDictionary = 'T_SYNONYM';
        
        //Add term in dictionary
        $this->_addDictionary(
                    DictionaryParser::getStringFormated($entity->getTerm())
                );
    }

    /*
     * Remove file dictionary
     * @param File
     * @return boolean
     */
    protected function _removeFile($file)
    {
        $fs = new Filesystem();
        
        if( $fs->exists($file) ){
            $fs->remove ($file);
            return true;
        }else{
            return false;
        }           
        
    }
    
    /*
     * Usage in Parser
     * @param string Qurey
     */
    public function query($query)
    {
        $type = null;
        $term = null;
        
        //verify in synonym dictionary
        $term = $this->_findDictionarySynonym($query);
        if( !empty($term) )
            return $term;   
        
        /*
        //verify in colour dictionary
        $term = $this->_findAllDictionary($query,DictionaryService::FILE_COLOUR);
        if( !empty($term) )
                return array(
                'token'  => Tokens::T_COLOUR,
                'match'  => $term,
            );
            
        //verify in pattern dictionary
        $term = $this->_findAllDictionary($query,DictionaryService::FILE_PATTERN);
        if( !empty($term) )
                return array(
                'token'  => Tokens::T_PATTERN,
                'match'  => $term,
            );
            
        //verify in element dictionary
        $term = $this->_findAllDictionary($query,DictionaryService::FILE_ELEMENT);
        if( !empty($term) )
                return array(
                'token'  => Tokens::T_ELEMENT,
                'match'  => $term,
            );
        */
        
        return false;
    }
    
    /*
     * Find query in dictionary COLOUR, ELEMENT and PATTERN
     * @param query
     * @param dictionary
     */
    protected function _findAllDictionary($query,$dictionary)
    {
        
        $query = DictionaryParser::getStringFormated($query);
        
        $dictionary_link = $this->_getDictionary($dictionary);
             
        if ( pspell_check($dictionary_link, $query) ){
            return DictionaryParser::returnStringFormated($query);
        }
            
        $arraySuggest = pspell_suggest($dictionary_link, $query);
        
        if( !empty($arraySuggest) )
            return DictionaryParser::returnStringFormated($arraySuggest[0]);
        else
            return null;            
        
    }
    
    /*
     * Find query in especific dictionary Synonym
     * @param query
     */
    protected function _findDictionarySynonym($query)
    {
        $query = DictionaryParser::getStringFormated($query);
        
        $word = null;
        $dictionary_link = $this->_getDictionary(DictionaryService::FILE_SYNONYM);
        
        if ( pspell_check($dictionary_link, $query) ) {
            
            $word = $query;
            
        } else {
            
            $arraySuggest = pspell_suggest($dictionary_link, $query);
        
            if( !empty($arraySuggest) )
                $word = $arraySuggest[0];
        }
        
        if( empty($word) )
            return false;
        
        $word = DictionaryParser::returnStringFormated($word);
        
        //Find Synonym term
        $entitySynonym = $this->em->getRepository('JerseyColoursBundle:Synonym')->findByTerm($word);
        
        if( !$entitySynonym )
            return false;
        
        switch ($entitySynonym->getType()){
            case Synonym::TYPE_COLOUR :
                return array(
                    'token'  => Tokens::T_COLOUR,
                    'match'  => $entitySynonym->getOriginalTerm(),
                );  
                break;
            case Synonym::TYPE_PATTERN :
                return array(
                    'token'  => Tokens::T_PATTERN,
                    'match'  => $entitySynonym->getOriginalTerm(),
                );                  
                break;
            case Synonym::TYPE_ELEMENT :
                return array(
                    'token'  => Tokens::T_ELEMENT,
                    'match'  => $entitySynonym->getOriginalTerm(),
                );                  
                break;
        }
        
        return false;
        
    }
    
    /*
     * Remove file dictionary Colour
     */
    public function clearColours()
    {        
        $file = $this->_getPath() . DictionaryService::FILE_COLOUR;
        $this->_removeFile($file);
    }
    
    /*
     * Remove file dictionary Pattern
     */
    public function clearPatterns()
    {
        $file = $this->_getPath() . DictionaryService::FILE_PATTERN;
        $this->_removeFile($file);
    }
    
    /*
     * Remove file dictionary Element
     */
    public function clearElementTypes()
    {
        $file = $this->_getPath() . DictionaryService::FILE_ELEMENT;
        $this->_removeFile($file);
    }
    
    /*
     * Remove file dictionary Synonym
     */
    public function clearSynonyms()
    {
        $file = $this->_getPath() . DictionaryService::FILE_SYNONYM;
        $this->_removeFile($file);
    }
    
    /*
     * Change word in dictionary colour
     * @param $data array data
     */
    public function updateColour($data)
    {
        $this->_changeWordDictionary($data, DictionaryService::FILE_COLOUR);
        
    }
    
    /*
     * Change word in dictionary Element
     * @param $data array data
     */
    public function updateElement($data)
    {
        $this->_changeWordDictionary($data, DictionaryService::FILE_COLOUR);
        
    }
    
    /*
     * Change word in dictionary Pattern
     * @param $data array data
     */
    public function updatePattern($data)
    {
        $this->_changeWordDictionary($data, DictionaryService::FILE_PATTERN);
        
    }
    
    /*
     * Change word in dictionary Synonym
     * @param $data array data
     */
    public function updateSynonym($data)
    {
        $this->_changeWordDictionary($data, DictionaryService::FILE_SYNONYM);
        
    }
    
    /*
     * Change the word in dictionary especific
     * @param $data array data
     * @param $file
     */
    protected function _changeWordDictionary($data, $file)
    {
        try{
                   
            //Dictionary Word
            $old = DictionaryParser::getStringFormated($data['old']);
            $new = DictionaryParser::getStringFormated($data['new']);

            $filePath = $this->_getPath() . $file;
            
            $fileContent = '';
            if ( is_file($filePath) ) {

                $file = file_get_contents($filePath);
                $fileContent = str_replace($old, $new, $file);
            }
            
            file_put_contents($filePath, $fileContent);
            
            return true;
        
        }catch( \Exception $e ){
            return false;
        }
    }
    
    /*
     * Remove word colour
     * @param $entity Entity
     */
    public function removeColour($entity)
    {
        $this->_deleteWordDictionary($entity->getName(),DictionaryService::FILE_COLOUR);
    }
    
    /*
     * Remove word Pattern
     * @param $entity Entity
     */
    public function removePattern($entity)
    {
        $this->_deleteWordDictionary($entity->getDescription(),DictionaryService::FILE_PATTERN);
    }
    
    /*
     * Remove word element
     * @param $entity Entity
     */
    public function removeElement($entity)
    {
        $this->_deleteWordDictionary($entity->getName(),DictionaryService::FILE_ELEMENT);
    }
    
    /*
     * Remove word synonym
     * @param $entity Entity
     */
    public function removeSynonym($entity)
    {
        $this->_deleteWordDictionary($entity->getTerm(),DictionaryService::FILE_SYNONYM);
    }
    
    /*
     * Delete the word in dictionary especific
     * @param $data array data
     * @param $file
     */
    protected function _deleteWordDictionary($data, $file)
    {
        try{
            $filePath = $this->_getPath() . $file;
            $lines = file($filePath, FILE_IGNORE_NEW_LINES);

            $wordRemove = DictionaryParser::getStringFormated($data);

            foreach( $lines as $key => $line )
                if( 0 === strcmp(strtolower($line), strtolower($wordRemove)) )
                        unset($lines[$key]);

            $data = implode("\n", array_values($lines));
            $file = fopen($filePath,"w");
            fwrite($file, $data);
            fclose($file);
            
            return true;
            
        }catch( \Exception $e ){
            return false;
        }
    }
}
