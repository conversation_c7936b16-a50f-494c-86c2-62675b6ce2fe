<?php

namespace DBLS\Bundle\JerseyColoursBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Response;

use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Template;

use Doctrine\Common\Collections\ArrayCollection;

use DBLS\Bundle\JerseyColoursBundle\Entity\Jersey;
use DBLS\Bundle\JerseyColoursBundle\Form\JerseyType;

/**
 * Jersey controller.
 *
 * @Route("/silk{trailingSlash}", requirements={"trailingSlash" = "[/]{0,1}"}, defaults={"trailingSlash" = "/"})
 */
class JerseyController extends Controller
{
    /**
     * Lists all Colour entities.
     *
     * @Route("{id}", name="jersey", defaults={"id": null}, 
     *     requirements={
     *         "id": "\d+"
     *     },
     *     options={"expose"=true}
     * )
     * @Method("GET")
     * @Template("")
     */
    public function indexAction(Request $request, $id = null)
    {
        
        if( empty($id) )
            return $this->redirectToRoute('jersey_meeting');
        
        $em = $this->getDoctrine()->getManager();

        $entities = array();
        
        $colours = $em->getRepository('JerseyColoursBundle:Colour')->findAll();

        $jersey = null;
        if ( $id ) {
            $em = $this->getDoctrine()->getManager();
            $jerseyRepository = $em->getRepository('JerseyColoursBundle:Jersey');

            $jersey = $jerseyRepository->find($id);
        }

        $dataColours = array();
        foreach ( $colours as $colour ) {
            $dataColours[$colour->getId()] = $colour->getHexadecimal();
        }
        
        return $this->render('JerseyColoursBundle:Jersey:silk.html.twig', array(
            'jersey'        => $jersey,
            'race'         => $jersey->getRace(),
            'colours'       => json_encode($dataColours),
        ));
    }

    /**
     * 
     * @Route("meeting", name="jersey_meeting", options={"expose"=true})
     * @Method("GET")
     * @Template("JerseyColoursBundle:Jersey:meeting.html.twig")
     */
    public function meetingAction()
    {
        $em = $this->getDoctrine()->getManager();
        
        $meetings = $em->getRepository('JerseyColoursBundle:Meeting')->findAllPending();
        
        $dados = array();
        
        foreach( $meetings as $meeting ){
            $dados[date_format($meeting->getDate(), 'Y-m-d')][] = array(
                'id'   => $meeting->getId(),
                'name' => $meeting->getLocation(),
                'races' => $meeting->getRaces(),
            );
        }

        // parameters to template
        return array(
            'dados'         => $dados,
        );
    }
    
    /**
     * Lists all Colour entities.
     *
     * @Route("/approved", name="jersey_approved", options={"expose"=true})
     * @Method("GET")
     * @Template("JerseyColoursBundle:Jersey:approved.html.twig")
     */
    public function approvedAction(Request $request)
    {
        $em = $this->getDoctrine()->getManager();

        $entities = $em->getRepository('JerseyColoursBundle:Race')->findAllApproved();

        // parameters to template
        return array(
            'races'    => $entities
        );
    }
    
        /**
     * Edit the Jersey.
     *
     * @Route("race/{id}", name="jersey_race", options={"expose"=true})
     * @Method("GET")
     * @Template("JerseyColoursBundle:Jersey:silk.html.twig")
     */
    public function raceAction(Request $request, $id)
    {
        
        $em = $this->getDoctrine()->getManager();

        $entities = $em->getRepository('JerseyColoursBundle:Race')->findAllPending($id);
        
        $colours = $em->getRepository('JerseyColoursBundle:Colour')->findAll();
        $dataColours = array();
        foreach ( $colours as $colour ) {
            $dataColours[$colour->getId()] = $colour->getHexadecimal();
        }
        
        /*
        $page = $request->query->getInt('page', 1);
        // Paginator
        $paginator  = $this->get('knp_paginator');
        $pagination = $paginator->paginate(
            $jerseys,
            $page,
            2
        );

        if ( $page )
            $pagination->setParam('id', null);
        */

        // parameters to template
        return array(
            //'pagination'    => $pagination,
            'race'         => $entities,
            'colours'       => json_encode($dataColours),
            'jersey'       => false,
            //'page'          => $page,
        );
    }

    /**
     * Edit the Jersey.
     *
     * @Route("/edit/{id}", name="jersey_edit", options={"expose"=true})
     * @Method("GET")
     * @Template("JerseyColoursBundle:Jersey:edit.html.twig")
     */
    public function editAction(Request $request, $id)
    {
        if ( !$request->isXmlHttpRequest() ) {
            return $this->redirectToRoute('jersey');
        }

        $em = $this->getDoctrine()->getManager();
        $jerseyRepository = $em->getRepository('JerseyColoursBundle:Jersey');

        $entity = $jerseyRepository->find($id);

        //Find Erros not solved
        $errors = $em->getRepository('JerseyColoursBundle:Error')->getErrorsNotSolved($entity);

        if (!$entity) {
            throw $this->createNotFoundException('Unable to find Jersey entity.');
        }

        $editForm = $this->createJerseyForm($entity, $id);

        return array(
            'jersey'    => $entity,
            'errors'    => $errors,
            'form'      => $editForm->createView()
        );
    }

    /**
     * Edit the Jersey.
     *
     * @Route("/refresh/{id}", name="jersey_refresh", options={"expose"=true}, condition="request.isXmlHttpRequest()")
     * @Method("POST")
     */
    public function refreshAction(Request $request, $id)
    {
        if ( !$request->isXmlHttpRequest() ) {
            return $this->redirectToRoute('jersey');
        }

        $em = $this->getDoctrine()->getManager();
        $jersey = $em->getRepository('JerseyColoursBundle:Jersey')->find($id);

        if ( !$jersey ) {
            throw $this->createNotFoundException('Unable to find Jersey entity.');
        }

        $form = $this->createJerseyForm($jersey, $jersey->getId() );
        $form->handleRequest($request);

        $jerseyParser = $this->get('services.jersey_parser');
        $jerseyGenerator = $this->get('services.jersey_generator');

        $em->getRepository('JerseyColoursBundle:Error')->solveErrorsJersey($jersey);

        $jerseyParser->setJersey($jersey);
        $jersey = $jerseyParser->createJerseyFromString($jersey->getDescription());

        $status = $jerseyGenerator->createJersey($jersey, false, false);

        return $this->_responseEntityEdition($jersey, $status);
    }

    /**
     * Creates a new Jersey entity.
     *
     * @Route("/save/{id}", name="jersey_save", defaults={"id": null})
     * @Method("POST")
     */
    public function saveAction(Request $request, $id)
    {
        if ( !$request->isXmlHttpRequest() ) {
            return $this->redirectToRoute('jersey');
        }

    	if ( !$id ) {
    	    $entity = new Jersey();
    	} else {

    	    $em = $this->getDoctrine()->getManager();
    	    $entity = $em->getRepository('JerseyColoursBundle:Jersey')->find($id);

    	    if (!$entity) {
    		  throw $this->createNotFoundException('Unable to find Jersey entity.');
    	    }
    	}

        $form = $this->createJerseyForm($entity, $id);
        $form->handleRequest($request);

        $status = false;
        if ( $form->isValid() ) {

            $jerseyGenerator = $this->get('services.jersey_generator');
            $status = $jerseyGenerator->createJersey($entity, true);
        }

        return $this->_responseEntityEdition($entity, $status);
    }

    /**
     * Edit the Jersey.
     *
     * @Route("/copy/{from}/{to}", name="jersey_copy", options={"expose"=true}, condition="request.isXmlHttpRequest()")
     * @Method("POST")
     */
    public function copyAction(Request $request, $from, $to)
    {
        $em = $this->getDoctrine()->getManager();
        $jerseyRepository = $em->getRepository('JerseyColoursBundle:Jersey');

        $jerseySource = $jerseyRepository->find($from);
        if ( !$jerseySource ) {
            throw $this->createNotFoundException('Unable to find Jersey source entity.');
        }

        $jerseyTarget = $jerseyRepository->find($to);
        if ( !$jerseyTarget ) {
            throw $this->createNotFoundException('Unable to find Jersey target entity.');
        }

        $status = $this->get('services.clone_jersey')->cloneJersey($jerseySource, $jerseyTarget);

        return $this->_responseEntityEdition($jerseyTarget, $status);
    }

    /**
     * Create the Json Response when editing/refreshing jersey
     * @param  Jersey $entity
     * @param  boolean $status
     * @return JsonResponse
     */
    private function _responseEntityEdition(Jersey $entity, $status)
    {
        $editForm = $this->createJerseyForm($entity, $entity->getId());
        $em = $this->getDoctrine()->getManager();
        $errors = $em->getRepository('JerseyColoursBundle:Error')->getErrorsNotSolved($entity);

        $return = array(
            'status'      => $status,
            'livery'      => $entity->getDescription(),
            'id'          => $entity->getId(),
            'front_view'  => $entity->getFrontViewImage(),
            'side_view'   => $entity->getSideViewImage(),
            'can_approve' => !$entity->getRace()->hasError(),
            'id_race'     => $entity->getRace()->getId(),
            'elements'    => $this->renderView('JerseyColoursBundle:Jersey:elements.html.twig', array('form' => $editForm->createView())),
            'has_errors' => !empty($errors)
        );

        if ( $return['has_errors'] )
            $return['errors'] = $this->renderView('JerseyColoursBundle:Jersey:errors.html.twig', array('errors' => $errors));

        return new JsonResponse($return);
    }

    /**
     * Creates a form to save a Jersey entity.
     *
     * @param Jersey $entity The entity
     *
     * @return \Symfony\Component\Form\Form The form
     */
    private function createJerseyForm(Jersey $entity, $id = null)
    {
        $form = $this->createForm(new JerseyType(), $entity, array(
            'action' => $this->generateUrl('jersey_save', array( 'id' => $id )),
            'method' => 'POST',
        ));

        return $form;
    }

    /**
     * Lists all Colour entities.
     *
     * @Route("/svg/{id}/{view}", name="jersey_svg", defaults={"view" = "front"})
     * @Method("GET")
     */
    public function generateSvg($id, $view = 'front')
    {
        $svgGenerator = $this->get("services.svg_generator");
        $em = $this->getDoctrine()->getManager();

        $jersey = $em->getRepository('JerseyColoursBundle:Jersey')->find($id);

        $svgGenerator->output($jersey, $view);
        exit;
    }

    /**
     * Lists all colour entities.
     *
     * @Route("/get-colours", name="getcoloursentities", options={"expose"=true})
     * @Method("GET")
     */
    public function getColoursEntities(Request $request)
    {
        $em = $this->getDoctrine()->getManager();
        $coloursRepository = $em->getRepository('JerseyColoursBundle:Colour');

        $term = $request->get('term');
        $dataColours = array();

        $trim = trim($term);
        if ( !empty($term) )
            $dataColours = $coloursRepository->listOptionsCombo($term);

        return new JsonResponse($dataColours);
    }

}
