# Jersey Colours PHP to Ruby/React Conversion Plan

## Overview
Converting the PHP Symfony Jersey Colours application to a modern Ruby on Rails API backend with React frontend.

## Current PHP Application Analysis

### Core Functionality
- **Jersey Management**: Create, edit, and manage jockey racing silks
- **Natural Language Processing**: Parse text descriptions into structured jersey designs
- **Visual Generation**: Create SVG representations of jerseys
- **Pattern & Color System**: Comprehensive color and pattern management
- **Race Management**: Track races, meetings, trainers
- **Import/Export**: Bulk data operations

### Key Components
1. **String Parser System**
   - Lexer for tokenization
   - Grammar parser for structure
   - Synonym handling
   - Fuzzy matching with Levenshtein distance

2. **Data Models**
   - Jersey (main entity)
   - JerseyElement (parts: base, shirt, cap, sleeves, etc.)
   - ElementType (defines jersey parts)
   - Pattern (visual patterns like stripes)
   - Colour (CMYK/Hex color definitions)
   - JerseyPatternColour (colors in patterns)
   - Race, Meeting, Trainer (context data)

3. **SVG Generation**
   - Base SVG templates
   - Pattern overlays
   - Color application
   - PhantomJS server for rendering

## Ruby/React Architecture Design

### Backend: Ruby on Rails API
```
app/
├── models/
│   ├── jersey.rb
│   ├── jersey_element.rb
│   ├── element_type.rb
│   ├── pattern.rb
│   ├── colour.rb
│   ├── jersey_pattern_colour.rb
│   ├── race.rb
│   ├── meeting.rb
│   ├── trainer.rb
│   └── user.rb
├── services/
│   ├── jersey_parser_service.rb
│   ├── string_lexer_service.rb
│   ├── grammar_parser_service.rb
│   ├── svg_generator_service.rb
│   └── jersey_generator_service.rb
├── controllers/api/v1/
│   ├── jerseys_controller.rb
│   ├── colours_controller.rb
│   ├── patterns_controller.rb
│   ├── races_controller.rb
│   └── auth_controller.rb
└── serializers/
    ├── jersey_serializer.rb
    ├── colour_serializer.rb
    └── pattern_serializer.rb
```

### Frontend: React Application
```
src/
├── components/
│   ├── jerseys/
│   │   ├── JerseyList.jsx
│   │   ├── JerseyForm.jsx
│   │   ├── JerseyViewer.jsx
│   │   └── JerseyGenerator.jsx
│   ├── patterns/
│   │   ├── PatternList.jsx
│   │   └── PatternForm.jsx
│   ├── colours/
│   │   ├── ColourList.jsx
│   │   └── ColourPicker.jsx
│   └── common/
│       ├── Layout.jsx
│       ├── Navigation.jsx
│       └── ErrorBoundary.jsx
├── services/
│   ├── api.js
│   ├── jerseyService.js
│   ├── colourService.js
│   └── authService.js
├── hooks/
│   ├── useJerseys.js
│   ├── useColours.js
│   └── useAuth.js
└── utils/
    ├── svgGenerator.js
    ├── colorUtils.js
    └── validators.js
```

## Technology Stack

### Backend
- **Ruby on Rails 7** (API mode)
- **PostgreSQL** (database)
- **JWT** (authentication)
- **RSpec** (testing)
- **Sidekiq** (background jobs)
- **Redis** (caching/sessions)

### Frontend
- **React 18** with hooks
- **TypeScript** (type safety)
- **React Router** (routing)
- **React Query** (data fetching)
- **Tailwind CSS** (styling)
- **Vite** (build tool)
- **Jest + React Testing Library** (testing)

### Additional Tools
- **Docker** (containerization)
- **GitHub Actions** (CI/CD)
- **SVG.js** (SVG manipulation)
- **Canvas API** (jersey rendering)

## Key Conversion Challenges

1. **String Parsing Logic**: Convert complex PHP lexer/parser to Ruby
2. **SVG Generation**: Replace PhantomJS with modern SVG libraries
3. **Database Schema**: Migrate from MySQL to PostgreSQL
4. **Authentication**: Replace FOSUserBundle with JWT
5. **File Uploads**: Handle pattern SVG files
6. **Error Handling**: Maintain sophisticated error tracking

## Implementation Phases

### Phase 1: Backend Foundation
1. Rails API setup with authentication
2. Database models and migrations
3. Basic CRUD controllers
4. API serialization

### Phase 2: Core Business Logic
1. String parser service conversion
2. Jersey generation logic
3. SVG generation service
4. Error handling system

### Phase 3: Frontend Development
1. React app setup with routing
2. Jersey management components
3. Color and pattern management
4. SVG visualization components

### Phase 4: Advanced Features
1. Import/export functionality
2. Advanced search and filtering
3. User management
4. Performance optimization

### Phase 5: Testing & Deployment
1. Comprehensive test suite
2. Production deployment setup
3. Performance monitoring
4. Documentation

## Migration Strategy

1. **Data Migration**: Export PHP data to JSON/CSV, import to Rails
2. **Parallel Development**: Build Ruby app alongside PHP for comparison
3. **Feature Parity**: Ensure all PHP features work in Ruby version
4. **User Testing**: Validate functionality with existing users
5. **Gradual Rollout**: Phase migration to minimize disruption

## Success Metrics

- All existing jersey parsing functionality preserved
- SVG generation quality matches or exceeds current system
- Performance improvements (faster parsing, rendering)
- Modern, responsive UI
- Comprehensive test coverage (>90%)
- Easy deployment and maintenance
