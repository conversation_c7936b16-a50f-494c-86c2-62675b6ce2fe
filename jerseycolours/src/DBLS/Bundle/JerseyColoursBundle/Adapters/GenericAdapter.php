<?php

namespace DBLS\Bundle\JerseyColoursBundle\Adapters;

/**
 * Description of GenericAdapter
 *
 * <AUTHOR>
 */
class GenericAdapter extends DataAdapter implements DataSourceInterface
{
    /*
     * @var $data
     */
    private $data;

    /*
     *
     */
    public function getData()
    {
        return $this->getDataProcessed( $this->data );
    }
    
    /*
     * 
     */
    public function setData( $data )
    {
        $this->data = $data;
    }
}
