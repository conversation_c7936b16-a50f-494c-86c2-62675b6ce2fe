import React from 'react';

function App() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">Jersey Colours</h1>
          <p className="mt-2 text-gray-600">Application is loading...</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">System Status</h2>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Frontend:</span>
              <span className="text-green-600">✓ Running</span>
            </div>
            <div className="flex justify-between">
              <span>API Connection:</span>
              <span className="text-yellow-600">⚠ Testing...</span>
            </div>
          </div>
        </div>
        
        <div className="text-center text-sm text-gray-500">
          <p>If you see this page, the React app is working!</p>
          <p className="mt-1">Next: Test API connection and authentication</p>
        </div>
      </div>
    </div>
  );
}

export default App;
