#!/bin/bash

echo "🔧 Fixing React app dependencies..."

# Remove existing node_modules and lock file
rm -rf node_modules package-lock.json

# Install compatible versions
npm install react@18.2.0 react-dom@18.2.0
npm install @types/react@18.2.0 @types/react-dom@18.2.0
npm install react-router-dom@6.8.0
npm install @tanstack/react-query@4.29.0
npm install axios@1.4.0
npm install tailwindcss@3.3.0
npm install @headlessui/react@1.7.0
npm install @heroicons/react@2.0.0

# Install dev dependencies
npm install --save-dev @types/node@18.16.0
npm install --save-dev typescript@5.0.0
npm install --save-dev vite@4.5.0

echo "✅ Dependencies fixed! Try running 'npm run dev' now."
