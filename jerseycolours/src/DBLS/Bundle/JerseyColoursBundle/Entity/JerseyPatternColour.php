<?php

namespace DBLS\Bundle\JerseyColoursBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Doctrine\Common\Collections\ArrayCollection;

/**
 * JerseyElement
 *
 * @ORM\Table()
 * @ORM\Entity(repositoryClass="DBLS\Bundle\JerseyColoursBundle\Entity\JerseyPatternColourRepository")
 */
class JerseyPatternColour
{

    /**
     * @var integer
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="JerseyElementPattern", inversedBy="pattern_colours")
     * @ORM\JoinColumn(name="jersey_element_pattern_id", referencedColumnName="id", onDelete="CASCADE")
     */
    private $jersey_element_pattern;

    /**
     * @ORM\ManyToOne(targetEntity="Colour", inversedBy="pattern_colours")
     * @ORM\JoinColumn(name="colour_id", referencedColumnName="id")
     */
    private $colour;
    
    /**
     * @var integer
     *
     * @ORM\Column(name="`order`", type="integer")
     */
    private $order;


    /**
     * Get id
     *
     * @return integer 
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set order
     *
     * @param integer $order
     * @return JerseyPatternColour
     */
    public function setOrder($order)
    {
        $this->order = $order;

        return $this;
    }

    /**
     * Get order
     *
     * @return integer 
     */
    public function getOrder()
    {
        return $this->order;
    }

    /**
     * Set colour
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Colour $colour
     * @return JerseyPatternColour
     */
    public function setColour(\DBLS\Bundle\JerseyColoursBundle\Entity\Colour $colour = null)
    {
        $this->colour = $colour;

        return $this;
    }

    /**
     * Get colour
     *
     * @return \DBLS\Bundle\JerseyColoursBundle\Entity\Colour 
     */
    public function getColour()
    {
        return $this->colour;
    }

    /**
     * Set jersey_element_pattern
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElementPattern $jerseyElementPattern
     * @return JerseyPatternColour
     */
    public function setJerseyElementPattern(\DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElementPattern $jerseyElementPattern = null)
    {
        $this->jersey_element_pattern = $jerseyElementPattern;

        return $this;
    }

    /**
     * Get jersey_element_pattern
     *
     * @return \DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElementPattern 
     */
    public function getJerseyElementPattern()
    {
        return $this->jersey_element_pattern;
    }
}
