class Jersey < ApplicationRecord
  belongs_to :race, optional: true
  belongs_to :trainer, optional: true
  has_many :jersey_elements, -> { order(:order) }, dependent: :destroy
  has_many :errors, dependent: :destroy

  validates :name, presence: true
  validates :string_value, presence: true
  validates :external_jersey_id, presence: true, uniqueness: { scope: :race_id }

  scope :parsed, -> { where(parsed: true) }
  scope :unparsed, -> { where(parsed: false) }
  scope :parsing, -> { where(parsing: true) }

  def base_element
    jersey_elements.joins(:element_type).find_by(element_types: { id: ElementType::BASE })
  end

  def generate_string
    base_element_obj = base_element
    other_elements_str = generate_string_for_elements

    output = ""
    if base_element_obj&.colour
      output = base_element_obj.colour.name
      output = "#{output}, #{other_elements_str}" if other_elements_str.present?
    elsif other_elements_str.present?
      output = other_elements_str
    end

    output.downcase
  end

  def folder_path
    "#{id}/#{name.parameterize}"
  end

  def to_s
    name
  end

  private

  def generate_string_for_elements
    # Implementation for generating string representation of non-base elements
    # This would be similar to the PHP generateStringForElements method
    jersey_elements.joins(:element_type)
                  .where.not(element_types: { id: ElementType::BASE })
                  .map(&:to_string)
                  .join(", ")
  end
end
