class Colour < ApplicationRecord
  has_many :jersey_elements, dependent: :nullify
  has_many :jersey_pattern_colours, dependent: :destroy
  has_many :synonyms, dependent: :destroy

  validates :name, presence: true, uniqueness: { case_sensitive: false }
  validates :cmyk, presence: true
  validates :hexadecimal, presence: true, uniqueness: true, format: { with: /\A#[0-9A-Fa-f]{6}\z/ }

  before_validation :normalize_name, :normalize_hexadecimal

  scope :by_name, ->(name) { where("LOWER(name) = ?", name.downcase) }

  def to_s
    name
  end

  private

  def normalize_name
    self.name = name&.strip&.downcase
  end

  def normalize_hexadecimal
    self.hexadecimal = hexadecimal&.strip&.upcase
    self.hexadecimal = "##{hexadecimal}" unless hexadecimal&.start_with?("#")
  end
end
