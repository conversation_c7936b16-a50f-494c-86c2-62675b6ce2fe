<?php

namespace DBLS\Bundle\JerseyColoursBundle\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolverInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;

use Symfony\Component\Form\Form;
use DBLS\Bundle\JerseyColoursBundle\Entity\ColourRepository;

class JerseyElementType extends AbstractType
{
    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {

        $builder->addEventListener(FormEvents::PRE_SET_DATA, function(FormEvent $event) use ($builder) {
            $jerseyElement = $event->getData();

            if ( !$jerseyElement ) {
                return; // $data is null when form is first constructed
            }

            // add patterns collection
            $form = $event->getForm();
            $form->add('jersey_patterns',
                'collection',
                array(
                    'type' => new JerseyElementPatternType($jerseyElement),
                    'allow_add' => true
                )
            );

            $id = $jerseyElement->getColour() != null ? $jerseyElement->getColour()->getId() : 0;
            $this->_addColourField($form, $id);
        });

        $builder->addEventListener(
            FormEvents::PRE_SUBMIT,
            function (FormEvent $event) 
            {
                $data = $event->getData();
                $form = $event->getForm();

                $this->_addColourField($form, $data['colour']);
            }
        );
    }

    /**
     * 
     * @param Form   $form
     * @param int $colour_id
     */
    protected function _addColourField(Form $form, $colour_id)
    {
        // add colour
        $form->add(
                'colour',
                'entity',
                array(
                    'class'       => 'JerseyColoursBundle:Colour',
                    'attr'        => array('class' => 'color-select'),
                    'required'    => false,
                    'empty_data'  => null,
                    'query_builder' => function(ColourRepository $colourRepository) use ($colour_id) {
                        $qb = $colourRepository->createQueryBuilder('c');
                        $qb->where('c.id = :id')
                            ->setParameter('id', $colour_id);
                        return $qb;
                    }
                )
        );
    }

    /**
     * @param OptionsResolverInterface $resolver
     */
    public function setDefaultOptions(OptionsResolverInterface $resolver)
    {
        $resolver->setDefaults(array(
            'data_class' => 'DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElement'
        ));
    }

    /**
     * @return string
     */
    public function getName()
    {
        return 'dbls_bundle_jerseycoloursbundle_element_type';
    }
}
