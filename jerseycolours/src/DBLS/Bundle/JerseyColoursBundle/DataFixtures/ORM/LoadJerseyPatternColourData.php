<?php

namespace DBLS\Bundle\JerseyColoursBundle\DataFixtures\ORM;

use Doctrine\Common\DataFixtures\AbstractFixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Common\Persistence\ObjectManager;
use DBLS\Bundle\JerseyColoursBundle\Entity\JerseyPatternColour;

class LoadJerseyPatternColourData extends AbstractFixture implements OrderedFixtureInterface
{

    /**
     * {@inheritDoc}
     */
    public function load(ObjectManager $manager)
    {

        $patternColourThubiaan = new JerseyPatternColour();
        $patternColourThubiaan->setJerseyElementPattern($this->getReference('jersey-element-pattern-thubiaan-shirt'));
        $patternColourThubiaan->setColour($this->getReference('colour-red'));
        $patternColourThubiaan->setOrder(1);

        $patternColourWaltzing1 = new JerseyPatternColour();
        $patternColourWaltzing1->setJerseyElementPattern($this->getReference('jersey-element-pattern-waltzing-sleeves'));
        $patternColourWaltzing1->setColour($this->getReference('colour-limegreen'));
        $patternColourWaltzing1->setOrder(1);

        $patternColourWaltzing2 = new JerseyPatternColour();
        $patternColourWaltzing2->setJerseyElementPattern($this->getReference('jersey-element-pattern-waltzing-cap'));
        $patternColourWaltzing2->setColour($this->getReference('colour-limegreen'));
        $patternColourWaltzing2->setOrder(2);

        $patternColourToo1 = new JerseyPatternColour();
        $patternColourToo1->setJerseyElementPattern($this->getReference('jersey-element-pattern-toocool-shirt'));
        $patternColourToo1->setColour($this->getReference('colour-red'));
        $patternColourToo1->setOrder(1);

        $patternColourToo2 = new JerseyPatternColour();
        $patternColourToo2->setJerseyElementPattern($this->getReference('jersey-element-pattern-toocool-sleeves'));
        $patternColourToo2->setColour($this->getReference('colour-red'));
        $patternColourToo2->setOrder(1);

        $patternColourVatican = new JerseyPatternColour();
        $patternColourVatican->setJerseyElementPattern($this->getReference('jersey-element-pattern-vatican-shirt'));
        $patternColourVatican->setColour($this->getReference('colour-orange'));
        $patternColourVatican->setOrder(1);
        
        $manager->persist($patternColourThubiaan);
        $manager->persist($patternColourWaltzing1);
        $manager->persist($patternColourWaltzing2);
        $manager->persist($patternColourToo1);
        $manager->persist($patternColourToo2);
        $manager->persist($patternColourVatican);
        $manager->flush();
    }

    /**
     * {@inheritDoc}
     */
    public function getOrder()
    {
        return 10;
    }
    
}
