<?php

namespace DBLS\Bundle\JerseyColoursBundle\Entity;

use Doctrine\ORM\EntityRepository;

/**
 * MeetingRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class MeetingRepository extends EntityRepository
{
    /**
     * 
     * @return [type] [description]
     */
    public function findAllPending()
    {
        $qb = $this->createQueryBuilder("m");
        
        $qb->innerJoin('JerseyColoursBundle:Race', 'r', 'WITH', 'r.meeting = m.id')
            ->innerJoin('JerseyColoursBundle:Jersey', 'je', 'WITH', 'je.race = r.id')
            ->where(
                $qb->expr()->andX(
                        $qb->expr()->eq('je.status', ':unapproved'),
                        $qb->expr()->eq('je.parsed', ':parsed')
                )
            )
            ->orderBy('m.date','ASC')
            ->distinct()
            ->setParameter('unapproved', 0)
            ->setParameter('parsed', 1);

        return $qb->getQuery()->getResult();
        
    }
}
