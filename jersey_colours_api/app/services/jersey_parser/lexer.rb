# frozen_string_literal: true

require 'levenshtein'

module JerseyParser
  class Lexer
    attr_accessor :tokens, :known_colours, :known_patterns, :known_elements, :synonyms, :levenshtein_ratio

    def initialize
      @tokens = []
      @known_colours = []
      @known_patterns = []
      @known_elements = []
      @synonyms = []
      @levenshtein_ratio = 75
    end

    def tokenize(string)
      @tokens = []
      words = string.split(/\s+/)
      
      words.each do |word|
        cleaned_word = clean_word(word)
        next if cleaned_word.empty?
        
        token = identify_token(cleaned_word)
        @tokens << token
      end
      
      @tokens
    end

    def set_known_colours(colours)
      @known_colours = colours.map(&:downcase)
    end

    def set_known_patterns(patterns)
      @known_patterns = patterns.map(&:downcase)
    end

    def set_known_elements(elements)
      @known_elements = elements.map(&:downcase)
    end

    def set_synonyms(synonyms)
      @synonyms = synonyms
    end

    def get_original_term(term)
      synonym = @synonyms.find { |s| s[:term].downcase == term.downcase }
      synonym ? synonym[:original_term] : term
    end

    private

    def clean_word(word)
      # Remove punctuation except commas and normalize
      cleaned = word.gsub(/[^\w,]/, '').downcase
      return ',' if cleaned == ','
      cleaned.gsub(',', '')
    end

    def identify_token(word)
      return Token.comma_token if word == ','
      return Token.and_token if word.downcase == 'and'

      # Check synonyms first
      original_term = get_original_term(word)
      search_term = original_term.downcase

      # Direct matches
      if @known_colours.include?(search_term)
        return Token.new(type: Token::T_COLOUR, value: search_term, original_term: original_term)
      end

      if @known_patterns.include?(search_term)
        return Token.new(type: Token::T_PATTERN, value: search_term, original_term: original_term)
      end

      if @known_elements.include?(search_term)
        return Token.new(type: Token::T_ELEMENT, value: search_term, original_term: original_term)
      end

      # Fuzzy matching with Levenshtein distance
      fuzzy_match = find_fuzzy_match(search_term)
      return fuzzy_match if fuzzy_match

      # Return unknown token
      Token.unknown_token(word)
    end

    def find_fuzzy_match(term)
      # Try colours first
      match = find_closest_match(term, @known_colours)
      return Token.new(type: Token::T_COLOUR, value: match, match: term, original_term: get_original_term(term)) if match

      # Try patterns
      match = find_closest_match(term, @known_patterns)
      return Token.new(type: Token::T_PATTERN, value: match, match: term, original_term: get_original_term(term)) if match

      # Try elements
      match = find_closest_match(term, @known_elements)
      return Token.new(type: Token::T_ELEMENT, value: match, match: term, original_term: get_original_term(term)) if match

      nil
    end

    def find_closest_match(term, candidates)
      best_match = nil
      best_ratio = 0

      candidates.each do |candidate|
        distance = Levenshtein.distance(term, candidate)
        max_length = [term.length, candidate.length].max
        ratio = ((max_length - distance).to_f / max_length) * 100

        if ratio >= @levenshtein_ratio && ratio > best_ratio
          best_ratio = ratio
          best_match = candidate
        end
      end

      best_match
    end
  end
end
