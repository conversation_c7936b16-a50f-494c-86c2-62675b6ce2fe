<?php

namespace DBLS\Bundle\JerseyColoursBundle\Services;

use Doctrine\ORM\EntityManager;
use Doctrine\ORM\UnitOfWork;

/**
 * Description of DbUtilsService
 *
 * <AUTHOR>
 */
class DbUtilsService {

    /**
     * Holds an instance of database connection
     * @var EntityManager
     */
    private $em = null;

    /**
    * Constructor
    */
    public function __construct(EntityManager $em) {
        $this->em = $em;
    }

    /**
    * Return true if $entity is new (new entity = it is not present in database)
    *
    * @return bool
    */
    public function isNewEntity($entity) {
        return UnitOfWork::STATE_NEW === $this->em->getUnitOfWork()->getEntityState($entity);
    }

}
