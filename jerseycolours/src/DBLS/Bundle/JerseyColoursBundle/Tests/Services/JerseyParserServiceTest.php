<?php

namespace DBLS\Bundle\JerseyColoursBundle\Tests\Services;

use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

use DBLS\Bundle\JerseyColoursBundle\Entity\ElementType;
use DBLS\Bundle\JerseyColoursBundle\Entity\Jersey;
use DBLS\Bundle\JerseyColoursBundle\Entity\Error;
use DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElement;
use DBLS\Bundle\JerseyColoursBundle\Entity\JerseyElementPattern;
use DBLS\Bundle\JerseyColoursBundle\Entity\JerseyPatternColour;

/**
 *
 * <AUTHOR>
 */
class JerseyParserServiceTest extends WebTestCase
{
    /**
     *
     * DI Container
     */
    static $container;

    /**
     *
     * @var DBLS\Bundle\JerseyColoursBundle\Services\JerseyParserService
     */
    static $service;

    /**
     * @var \Doctrine\ORM\EntityManager
     */
    private $em;

    /**
     * {@inheritDoc}
     */
    public function setUp()
    {
        self::bootKernel();
        $this->em = static::$kernel->getContainer()
            ->get('doctrine')
            ->getManager()
        ;
    }

    /**
     * {@inheritDoc}
     */
    protected function tearDown()
    {
        parent::tearDown();
        $this->em->close();
    }

    /**
     * {@inheritDoc}
     */
    public static function setUpBeforeClass()
    {
         //start the symfony kernel
         $kernel = static::createKernel();
         $kernel->boot();

         //get the DI container
         self::$container = $kernel->getContainer();

         self::$service = self::$container->get('services.jersey_parser');
    }

    // -------------------------------------------------------------------------
    //
    // Helper functions
    //
    // -------------------------------------------------------------------------

    /**
     * Check if a jersey has a base colour
     *
     * @param DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $jersey
     * @param bool $baseColour
     */
    private function _hasBaseColour($jersey, $baseColour) {
        $hasBaseColour = false;

        if ( $jersey->getElements()->count() > 0 ) {
            foreach ($jersey->getElements() as $jerseyElement) {
                if ( $jerseyElement->getElementType()->getId() == \DBLS\Bundle\JerseyColoursBundle\Entity\ElementType::BASE ) {
                    if ( $jerseyElement->getColour() && $jerseyElement->getColour()->getName() == $baseColour ) {
                        $hasBaseColour = true;
                    }
                }
            }
        }

        return $hasBaseColour;
    }

    /**
     * Check if jersey has an element with a colour
     *
     * @param DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $jersey
     * @param int $elementTypeId
     * @param string $colourName
     * @return bool
     */
    private function _hasElementWithColour($jersey, $elementTypeId, $colourName) {
        $found = false;
        if ( $jersey->getElements()->count() > 0 ) {
            foreach ($jersey->getElements() as $jerseyElement) {
                if ( $jerseyElement->getElementType()->getId() == $elementTypeId ) {
                    if ( $jerseyElement->getColour() && strtolower(trim($jerseyElement->getColour()->getName())) == strtolower(trim($colourName)) ) {
                        $found = true;
                    }
                }
            }
        }
        return $found;
    }

    /**
     * Check if $jersey has an element with a certain pattern
     *
     * @param DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $jersey
     * @param int $elementTypeId
     * @param string $patternName
     */
    public function _hasElementWithPattern($jersey, $elementTypeId, $patternName, $patternColours = null) {
        $found = false;

        if ( $jersey->getElements()->count() > 0 ) {
            foreach ($jersey->getElements() as $jerseyElement) {
                if ( $jerseyElement->getElementType()->getId() == $elementTypeId ) {
                    if ( $jerseyElement->getJerseyPatterns()->count() > 0 ) {
                        foreach ($jerseyElement->getJerseyPatterns() as $jerseyPattern) {
                            if (trim(strtolower($jerseyPattern->getPattern()->getDescription())) == trim(strtolower($patternName)) ) {
                                if ( $patternColours == null || empty($patternColours) )
                                {
                                    $found = true;
                                }
                                else
                                {
                                    if ( $jerseyPattern->getPatternColours()->count() > 0 ) {
                                        $colours = array();
                                        foreach ($jerseyPattern->getPatternColours() as $patternColour) {
                                            $colours[] = strtolower(trim($patternColour->getColour()->getName()));
                                        }
                                        $arrDiff = array_diff($patternColours, $colours);
                                        if ( !$arrDiff ) {
                                            $found = true;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return $found;
    }


    // -------------------------------------------------------------------------
    //
    // Testing jersey parser
    //
    // -------------------------------------------------------------------------

    public function testCreateJersey_Rebel_King()
    {
        $jersey = self::$service->createJerseyFromString("yellow, red striped sleeves and cap");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'yellow', "Jerseys's base colour isn't pink"));
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::CAP, 'striped', array('red')), "Jersey does not contain an element cap with red striped pattern");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SLEEVES, 'striped', array('red')), "Jersey does not contain an element sleeves with red striped pattern");
    }

    public function testCreateJersey_Miss_Wairoa()
    {
        $jersey = self::$service->createJerseyFromString("pink, pink and black checked sleeves and cap");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'pink', "Jerseys's base colour isn't pink"));
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::CAP, 'checked', array('black')), "Jersey does not contain an element cap with pink and black checked pattern");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::CAP, 'pink'), "Jersey does not contain an element cap with colour pink");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SLEEVES, 'checked', array('black')), "Jersey does not contain an element sleeves with pink and black checked pattern");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SLEEVES, 'pink'), "Jersey does not contain an element sleeves with colour pink");
    }

    public function testCreateJersey_Every_Faith()
    {
        $jersey = self::$service->createJerseyFromString("black, pink spots and hooped sleeves");
        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'black', "Jerseys's base colour isn't black"));
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SHIRT, 'spots', array('pink')), "Jersey does not contain an element shirt with pink spots pattern");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SLEEVES, 'hooped'), "Jersey does not contain an element sleeves with hooped pattern");
    }

    public function testCreateJersey_Duccio()
    {
        $jersey = self::$service->createJerseyFromString("yellow, red armbands, striped sleeves and cap");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'yellow', "Jerseys's base colour isn't yellow"));
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::ARMBANDS, 'red'), "Jersey does not contain an element band with colour red");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SLEEVES, 'striped'), "Jersey does not contain an element sleeves with striped pattern");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::CAP, 'striped'), "Jersey does not contain an element cap with striped pattern");
    }

    public function testCreateJersey_Lisa_Fashionista()
    {
        $jersey = self::$service->createJerseyFromString("maroon,gold maltese cross and cap");

        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SHIRT, 'maltese cross', array('gold')), "Jersey does not contain an element shirt with gold maltese cross pattern");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::CAP, 'gold'), "Jersey does not contain an element cap with colour gold");
    }

    public function testCreateJersey_Thing_Of_Nothing()
    {
        $jersey = self::$service->createJerseyFromString("white, royal blue sides, white, orange and royal blue hooped sleeves, orange cap");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'white'));
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SHIRT, 'sides', array('royal blue')), "Jersey does not contain an element sides with royal blue sides pattern");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SLEEVES, 'hooped', array('orange', 'royal blue')), "Jersey does not contain an element sleeves with orange and royal blue hooped pattern");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SLEEVES, 'white'), "Jersey does not contain an element sleeves with colour white");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::CAP, 'orange'), "Jersey does not contain an element cap with colour orange");
    }

    public function testCreateJersey_Sambuca_Siren()
    {
        $jersey = self::$service->createJerseyFromString("royal blue, white s, royal blue sleeves with white swirl, royal blue cap");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'royal blue'));
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SHIRT, 's', array('white')), "Jersey does not contain an element shirt with s pattern");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SLEEVES, 'swirl', array('white')), "Jersey does not contain an element sleeves with white swirl pattern");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::CAP, 'royal blue'), "Jersey does not contain an element cap with colour royal blue");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SLEEVES, 'royal blue'), "Jersey does not contain an element sleeves with colour royal blue");
    }

    public function testCreateJersey_hioctdane()
    {
        $jersey = self::$service->createJerseyFromString("royal blue, pink fleur de lys, yellow sleeves");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'royal blue'));
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SHIRT, 'fleur de lys', array('pink')), "Jersey does not contain an element shirt with pink fleur de lys pattern");
    }


    /**
     * Test if it is possible to create a jersey with a base colour and a patern for shirt.
     * Ex:
     *   - Molly The Snip: black, pink spots
     */
    public function testCreateJersey_baseColour_with_shirt_pattern() {
        $jersey = self::$service->createJerseyFromString("black, pink spots");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'black'), "Jersey's base colour isn't black");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SHIRT, 'spots', array('pink')), "Jersey does not contain an element shirt with pink spots pattern");
    }

    /**
     * Testing Jersey: Waltizing to Win
     */
    public function testCreateJersey_waltzing_to_win()
    {
        $jersey = self::$service->createJerseyFromString("royal blue, lime hooped sleeves, quartered cap");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'royal blue'), "Jersey's base colour isn't royal blue");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SLEEVES, 'hooped', array('lime')), "Jersey does not contain an element sleeves with lime hooped pattern");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::CAP, 'quartered'), "Jersey does not contain an element sleeves with quartered pattern");
    }

    public function testCreateJersey_gossip_guru() {
        $jersey = self::$service->createJerseyFromString("navy blue, white star, navy blue, red and white striped sleeves, red cap");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'navy blue'), "Jersey's base colour isn't navy blue");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SHIRT, 'star', array('white')), "Jersey does not contain an element shirt with white stars pattern");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SLEEVES, 'striped', array('red', 'white')), "Jersey does not contain an element sleeves with navy blue, red and white striped pattern");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SLEEVES, 'navy blue'), "Jersey does not contain an element sleeves with colour navy blue");
    }

    public function testCreateJersey_Eclair_Go_Go()
    {
        $jersey = self::$service->createJerseyFromString("red, silver diamond, purple collar and sleeves, silver armbands, red cap, silver star, purple peak");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'red'));
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SHIRT, 'diamond', array('silver')), "Jersey does not contain an element shirt with silver diamond pattern");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::COLLAR, 'purple'), "Jersey does not contain an element collar with colour purple");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SLEEVES, 'purple'), "Jersey does not contain an element sleeves with colour purple");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::PEAK, 'purple'), "Jersey does not contain an element sleeves with colour purple");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::ARMBANDS, 'silver'), "Jersey does not contain an element band with colour silver");
    }

    public function testCreateJersey_Lennybe()
    {
        $jersey = self::$service->createJerseyFromString("green, gold diamond, red sleeves and cap");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'green'));
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SHIRT, 'diamond', array('gold')), "Jersey does not contain an element shirt with gold diamond pattern");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SLEEVES, 'red'), "Jersey does not contain an element sleeves with colour red");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::CAP, 'red'), "Jersey does not contain an element cap with colour red");
    }

    public function testCreateJersey_Desdane()
    {
        $jersey = self::$service->createJerseyFromString("green, white shield with black lion, orange collar, white sleeves, red cap");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'green'));
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::COLLAR, 'orange'), "Jersey does not contain an element collar with colour orange");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SLEEVES, 'white'), "Jersey does not contain an element sleeves with colour white");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::CAP, 'red'), "Jersey does not contain an element cap with colour red");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SHIRT, 'shield', array('white')), "Jersey does not contain an element shirt with white shield pattern");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SHIRT, 'lion', array('black')), "Jersey does not contain an element shirt with black lion pattern");
    }

    public function testCreateJersey_Bold_Zamour()
    {
        $jersey = self::$service->createJerseyFromString("gold, green shamrock, green and gold spots cap");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'gold'));
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SHIRT, 'shamrock', array('green')), "Jersey does not contain an element shirt with gold shamrock pattern");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::CAP, 'spots', array('gold')), "Jersey does not contain an element cap with green and gold spots pattern");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::CAP, 'green'), "Jersey does not contain an element cap with colour green");
    }

    public function testCreateJersey_Alberdini()
    {
        $jersey = self::$service->createJerseyFromString("black, green sleeves and hooped cap");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'black'), "Jersery's base colour isn't black");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SLEEVES, 'green'), "Jersey does not contain an element sleeves with colour green");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::CAP, 'hooped'), "Jersey does not contain an element cap with hooped pattern");
    }

    public function testCreateJersey_colourPatternElementAndElement()
    {
        $jersey = self::$service->createJerseyFromString("Black, red star, Armbands And Cap, blue seams");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'black'), "Jersery's base colour isn't black");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SHIRT, 'star', array('red')), "Jersey does not contain an element shirt with red star pattern");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::ARMBANDS, 'red'), "Jersey does not contain an element armbands with colour red");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::CAP, 'red'), "Jersey does not contain an element cap with colour red");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SEAMS, 'blue'), "Jersey does not contain an element seams with colour red");
    }

    public function testCreateJersey_uncolouredPattern()
    {
        $jersey = self::$service->createJerseyFromString("Green, Red Sash And Sleeves, Quartered Cap");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'green'), "Jersery's base colour isn't Green");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::CAP, 'Quartered', array('red')), "Jersey does not contain an element cap with red quartered pattern");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SLEEVES, 'red'), "Jersey does not contain an element sleeves with colour red");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SASH, 'red'), "Jersey does not contain an element sash with colour red");
    }

    public function testCreateJersey_uncolouredPattern2()
    {
        $jersey = self::$service->createJerseyFromString("black, white checked top half, checked sleeves, red cap");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'black'), "Jersery's base colour isn't black");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SLEEVES, 'Checked', array('white')), "Jersey does not contain an element sleeves with white checked pattern");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::CAP, 'red'), "Jersey does not contain an element cap with colour red");
    }

    public function testCreateJersey_unknownPattern1()
    {
        $jersey = self::$service->createJerseyFromString("black, pink horseshoes and sleeves, black cap with horseshoes");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'black'), "Jersery's base colour isn't black");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SLEEVES, 'pink'), "Jersey does not contain an element sleeves with colour pink");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::CAP, 'black'), "Jersey does not contain an element cap with colour black");
    }

    public function testCreateJersey_unknownPattern2()
    {
        $jersey = self::$service->createJerseyFromString("black, pink horseshoes and sleeves, black cap with pink horseshoes");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'black'), "Jersery's base colour isn't black");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SLEEVES, 'pink'), "Jersey does not contain an element sleeves with colour pink");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::CAP, 'black'), "Jersey does not contain an element cap with colour black");
    }

    public function testCreateJersey_unknownToken1()
    {
        $jersey = self::$service->createJerseyFromString("black, pink sleeve, white pom pom");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'black'), "Jersery's base colour isn't black");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SLEEVES, 'pink'), "Jersey does not contain an element sleeves with colour pink");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::POMPOM, 'white'), "Jersey does not contain an element pompom with colour white");
    }

    public function testCreateJersey_unknownToken2()
    {
        $jersey = self::$service->createJerseyFromString("blackf, xpink sleeve, whites pom pom");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'black'), "Jersery's base colour isn't black");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SLEEVES, 'pink'), "Jersey does not contain an element sleeves with colour pink");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::POMPOM, 'white'), "Jersey does not contain an element pompom with colour white");
    }

    public function testCreateJersey_unknownToken3()
    {
        $jersey = self::$service->createJerseyFromString("yelow, red and white core circles, red quarteed cap");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'yellow'), "Jersery's base colour isn't green");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SHIRT, 'Core Circle', array('white', 'white')), "Jersey does not contain an element shirt with red and white core circle pattern");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::CAP, 'Quartered', array('red')), "Jersey does not contain an element cap with red quartered pattern");
    }

    public function testCreateJersey_unknownToken4()
    {
        $jersey = self::$service->createJerseyFromString("Blu, bLu sleeves, luE cap");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'blue'), "Jersery's base colour isn't blue");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SLEEVES, 'blue'), "Jersey does not contain an element sleeves with colour blue");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::CAP, 'blue'), "Jersey does not contain an element cap with colour blue");
    }

    public function testCreateJersey_patternElementWithoutColour()
    {
        $jersey = self::$service->createJerseyFromString("Blue, Red Core Star, Quartered Cap");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'blue'), "Jersery's base colour isn't Blue");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SHIRT, 'Core Star', array('red')), "Jersey does not contain an element shirt with red diamonds pattern");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::CAP, 'Quartered', array('red')), "Jersey does not contain an element cap with red quartered pattern");
    }

    public function testCreateJersey_patternApplyToSleeves1()
    {
        $jersey = self::$service->createJerseyFromString("Blue And White Hoops, Pink And White Checked Cap");
        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'blue'), "Jersery's base colour isn't blue");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SHIRT, 'Hoops', array('white')), "Jersey does not contain an element shirt with blue and white hoops pattern");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SHIRT, 'blue'), "Jersey does not contain an element shirt with colour blue");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SLEEVES, 'Hoops', array('white')), "Jersey does not contain an element sleeves with blue and white hoops pattern");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::CAP, 'Checked', array('white')), "Jersey does not contain an element cap with pink and white checked pattern");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::CAP, 'pink'), "Jersey does not contain an element cap with colour pink");
    }

    public function testCreateJersey_patternApplyToSleeves2()
    {
        $jersey = self::$service->createJerseyFromString("yellow, Blue And White Hoops, Pink And White Checked Cap");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'yellow'), "Jersery's base colour isn't yellow");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SHIRT, 'Hoops', array('white')), "Jersey does not contain an element shirt with blue and white hoops pattern");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SHIRT, 'blue'), "Jersey does not contain an element shirt with colour blue");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::CAP, 'Checked', array('white')), "Jersey does not contain an element cap with pink and white checked pattern");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::CAP, 'pink'), "Jersey does not contain an element cap with colour pink");
    }

    public function testCreateJersey_limegreenIdentfication()
    {
        $jersey = self::$service->createJerseyFromString("yellow, Lime Green Hoops, Pink Cap and Lime Green sleeves");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'yellow'), "Jersery's base colour isn't yellow");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SHIRT, 'Hoops', array('lime green')), "Jersey does not contain an element shirt with lime green hoops pattern");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::CAP, 'pink'), "Jersey does not contain an element cap with colour pink");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SLEEVES, 'lime green'), "Jersey does not contain an element cap with colour lime green");
    }

    public function testCreateJersey_shirInformationAfterSecondChunck()
    {
        $jersey = self::$service->createJerseyFromString("Green, Red And White Checked Cap, Gold Star And Armbands, Red Striped Sleeves");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'green'), "Jersery's base colour isn't green");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::CAP, 'Checked', array('white')), "Jersey does not contain an element cap with red and white checked pattern");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::CAP, 'red'), "Jersey does not contain an element cap with colour red");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SLEEVES, 'Striped', array('red')), "Jersey does not contain an element sleeves with red striped pattern");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SHIRT, 'Star', array('gold')), "Jersey does not contain an element shirt with gold start pattern");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::ARMBANDS, 'gold'), "Jersey does not contain an element armbands with colour gold");
    }

    
    public function testCreateJersey_coloursToElementsAfterUnkownPattern()
    {
        $jersey = self::$service->createJerseyFromString("red, green anything eellssee, sleeves and cap");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'red'), "Jersery's base colour isn't red");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SLEEVES, 'green'), "Jersey does not contain an element sleeves with colour green");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::CAP, 'green'), "Jersey does not contain an element cap with colour green");
    }
    
    public function testCreateJersey_endsWithPatternElement()
    {
        $jersey = self::$service->createJerseyFromString("red, blue anything eellssee and hooped sleeves");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'red'), "Jersery's base colour isn't red");
        $this->assertEquals(true, $this->_hasElementWithPattern($jersey, ElementType::SLEEVES, 'hooped', array('blue')), "Jersey does not contain an element sleeves with blue hooped pattern");
    }
    
    public function testCreateJersey_endsWithElement()
    {
        $jersey = self::$service->createJerseyFromString("red, blue anything eellssee and sleeves");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'red'), "Jersery's base colour isn't red");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SLEEVES, 'blue'), "Jersey does not contain an element sleeves with colour blue");
    }
    
    public function testCreateJersey_elementsWithWITH()
    {
        $jersey = self::$service->createJerseyFromString("White, green sash, Blue Cap With White Pom Pom");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'white'), "Jersery's base colour isn't white");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SASH, 'green'), "Jersey does not contain an element sash with colour green");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::CAP, 'blue'), "Jersey does not contain an element cap with colour blue");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::POMPOM, 'white'), "Jersey does not contain an element pompom with colour white");
    }
    
    public function testCreateJersey_elementsWithWITH2()
    {
        $jersey = self::$service->createJerseyFromString("White, green sash, Blue Cap With Pom Pom");

        $this->assertEquals(true, $this->_hasBaseColour($jersey, 'white'), "Jersery's base colour isn't white");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::SASH, 'green'), "Jersey does not contain an element sash with colour green");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::CAP, 'blue'), "Jersey does not contain an element cap with colour blue");
        $this->assertEquals(true, $this->_hasElementWithColour($jersey, ElementType::POMPOM, 'blue'), "Jersey does not contain an element pompom with colour white");
    }
}
