<?php

namespace DBLS\Bundle\JerseyColoursBundle\DataFixtures\ORM;

use Doctrine\Common\DataFixtures\AbstractFixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Common\Persistence\ObjectManager;
use DBLS\Bundle\JerseyColoursBundle\Entity\ElementType;

class LoadElementTypeData extends AbstractFixture implements OrderedFixtureInterface
{
    /**
     * {@inheritDoc}
     */
    public function load(ObjectManager $manager)
    {
		
        /*
         *  1	Base
            2	Shirt
            3	Cap
            4	Peak
            5	Sleeves
            6	Colar
            7	Cuffs
            8	Tassel
            9	Pom Pom
            10	Band
         */
        
        $ElementType1 = new ElementType();
        $ElementType1->setId(ElementType::BASE);
        $ElementType1->setName("Base");
        $ElementType1->setDescription("Base...");
        $manager->persist($ElementType1);
        $this->addReference('elementType-base', $ElementType1);
		
        $ElementType2 = new ElementType();
        $ElementType2->setName("Shirt");
        $ElementType2->setId(ElementType::SHIRT);
        $ElementType2->setDescription("Shirt...");
        $manager->persist($ElementType2);
        $this->addReference('elementType-shirt', $ElementType2);
		
        $ElementType3 = new ElementType();
        $ElementType3->setName("Cap");
        $ElementType3->setId(ElementType::CAP);
        $ElementType3->setDescription("Cap...");
        $manager->persist($ElementType3);
        $this->addReference('elementType-cap', $ElementType3);
		
        $ElementType4 = new ElementType();
        $ElementType4->setName("Peak");
        $ElementType4->setId(ElementType::PEAK);
        $ElementType4->setDescription("Peak...");
        $manager->persist($ElementType4);
        $this->addReference('elementType-peak', $ElementType4);
		
        $ElementType5 = new ElementType();
        $ElementType5->setName("Sleeves");
        $ElementType5->setId(ElementType::SLEEVES);
        $ElementType5->setDescription("Sleeves...");
        $manager->persist($ElementType5);
        $this->addReference('elementType-sleeves', $ElementType5);
		
        $ElementType6 = new ElementType();
        $ElementType6->setName("Collar");
        $ElementType6->setId(ElementType::COLLAR);
        $ElementType6->setDescription("Collar...");
        $manager->persist($ElementType6);
        $this->addReference('elementType-collar', $ElementType6);
		
        $ElementType7 = new ElementType();
        $ElementType7->setName("Cuffs");
        $ElementType7->setId(ElementType::CUFFS);
        $ElementType7->setDescription("Cuffs...");
        $manager->persist($ElementType7);
        $this->addReference('elementType-cuffs', $ElementType7);
		
        $ElementType8 = new ElementType();
        $ElementType8->setName("Tassel");
        $ElementType8->setId(ElementType::TASSEL);
        $ElementType8->setDescription("Tassel...");
        $manager->persist($ElementType8);
        $this->addReference('elementType-tassel', $ElementType8);
		
        $ElementType9 = new ElementType();
        $ElementType9->setName("PomPom");
        $ElementType9->setId(ElementType::POMPOM);
        $ElementType9->setDescription("Pom Pom...");
        $manager->persist($ElementType9);
        $this->addReference('elementType-pompom', $ElementType9);
		
        $ElementType10 = new ElementType();
        $ElementType10->setName("Armbands");
        $ElementType10->setId(ElementType::ARMBANDS);
        $ElementType10->setDescription("Band...");
        $manager->persist($ElementType10);
        $this->addReference('elementType-band', $ElementType10);

        $ElementType11 = new ElementType();
        $ElementType11->setName("Seams");
        $ElementType11->setId(ElementType::SEAMS);
        $ElementType11->setDescription("Seams...");
        $manager->persist($ElementType11);
        $this->addReference('elementType-seams', $ElementType11);

        $ElementType12 = new ElementType();
        $ElementType12->setName("Sash");
        $ElementType12->setId(ElementType::SASH);
        $ElementType12->setDescription("Sash...");
        $manager->persist($ElementType12);
        $this->addReference('elementType-sash', $ElementType12);

        $ElementType13 = new ElementType();
        $ElementType13->setName("Epaulettes");
        $ElementType13->setId(ElementType::EPAULETTES);
        $ElementType13->setDescription("Epaulettes...");
        $manager->persist($ElementType13);
        $this->addReference('elementType-epaulettes', $ElementType13);
                
        $metadata = $manager->getClassMetaData(get_class(new ElementType()));
        $metadata->setIdGeneratorType(\Doctrine\ORM\Mapping\ClassMetadata::GENERATOR_TYPE_NONE);
        $manager->flush();
    }
        
    /**
     * {@inheritDoc}
     */
    public function getOrder()
    {
        return 6;
    }
}