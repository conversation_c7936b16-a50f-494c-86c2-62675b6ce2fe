<?php

namespace DBLS\Bundle\JerseyColoursBundle\StringParser;


/**
 *
 * <AUTHOR> <adrianorc.com>
 */
class GrammarParser extends Parser {

    /**
     * Keep track of elements identified.
     * 
     * @var array
     */
    private $elementsIdentified;
    
    /**
     * Tokens after being stripped
     * @var array 
     */
    private $strippedTokens;
    
    /**
     *
     * @var IStringParserHelper
     */
    private $helper = null;
    
    //-------------------------------------------------
    //
    //  Public methods
    //
    //-------------------------------------------------

    /**
     * Constructor
     * 
     * @param Lexer $pLexer
     */
    public function __construct(Lexer $pLexer, IStringParserHelper $pHelper) {
        parent::__construct($pLexer);
        $this->strippedTokens = array();
        $this->helper = $pHelper;
    }


    /**
     *
     * @param array $tokens
     */
    public function parse($tokens) {
        $this->strippedTokens = $this->_intelliStrip($tokens);
        foreach ($this->strippedTokens as $idx => $item) {
            $this->_parseItem($idx, $item);
        }
        $this->_postChecks();
    }

    public function parseBase($items) {

        if ( $this->_correspond(array(Tokens::T_COLOUR), $items) )
        {
            $this->_include(array('match' => 'base', 'token' => Tokens::T_ELEMENT), $items[0]);
            return true;
        }

        //if ( $this->_correspond(array(Tokens::T_PATTERN), $items) )
        //{
        //    $this->_include('base', null, array($items[0]['match'] => array()));
        //    return;
        //}

        //if ( $this->_correspond( array(Tokens::T_COLOUR, Tokens::T_PATTERN), $items ) ) {
        //    $this->_include('base', null, array($items[1]['match'] => array($items[0]['match'])));
        //    return;
        //}

        foreach ($items as $token) {
            if ( $token['token'] == Tokens::T_UNKOWN ) {
                $this->_addError(
                        array(
                            'type' => Parser::E_TOKEN_UNKNOWN,
                            'message' => sprintf("The word <b>%s (originally: %s)</b> is unknown. It is not a colour, pattern or an element", $token['match'], $this->getOriginalTerm($token['match'])),
                            'expression' => $token['match']
                        )
                );
            }
        }

        //$this->_addError(
        //    array(
        //        'type' => Parser::E_INVALID_STRING_START,
        //        'message' => sprintf(
        //            "The beginning of the string is invalid. It should start with a colour, a pattern or a colour-pattern. " .
        //            "Instead, it starts like: <b>%s</b>, ...", Tokens::backToString($items) ),
        //        'expression' => null
        //    )
        //);

        return false;
    }

    //-------------------------------------------------
    //
    //  Private methods
    //
    //-------------------------------------------------

    private function _hasItem($items, $item) {
        $has = false;
        if ( count($items) > 0 ) {
            foreach($items as $it) {
                $has = $has || strtolower(trim($it['match'])) == strtolower(trim($item['match']));
            }
        }
        return $has;
    }

    private function _getLastIdentifiedColour($index) {
        
        // take the last colours from the lexer stream
        while ($index >= 0 ) {
            $chunk = $this->strippedTokens[$index];
            $count = count($chunk) -1;
            while ($count >= 0) {
                $token = $chunk[$count];
                
                if ( !empty($token['token']) && $token['token'] == Tokens::T_COLOUR) {
                    return $token;
                }
                
                $count--;
            }
            
            $index--;
        }
        
        return "";
    }
    
    private function _getFirstIdentifiedColour() {
        foreach ($this->strippedTokens as $idx => $arrTokens) {
            foreach($arrTokens as $token) {
                if ($token['token'] == Tokens::T_COLOUR) {
                    return $token;
                }
            }
        }
        return null;
    }
    
    private function _findColoursToUncolouredPattern($index) {
        $return = array();

        /*$count = count($this->elementsFound);
        
        // search colours in the elemenets found array
        if ($count > 0) {
            for($i=$count-1; $i>=0; $i--) {
                $element = $this->elementsFound[$i];
                if ( $element[self::S_ELEMENT_NAME] != 'base' ) {
                    if (isset($element[self::S_COLOUR]) && !empty($element[self::S_COLOUR]) ) {
                        //-- include that colour
                        if ( !$this->_hasItem($return, $element[self::S_COLOUR]) ) {
                            $return[] = $element[self::S_COLOUR];
                        }
                    }
                    if (isset($element[self::S_PATTERNS]) && !empty($element[self::S_PATTERNS]) ) {
                        foreach($element[self::S_PATTERNS] as $pattern) {
                            if (isset($pattern[self::S_COLOURS]) && !empty($pattern[self::S_COLOURS]) ) {
                                foreach($pattern[self::S_COLOURS] as $colour) {
                                    if ( !$this->_hasItem($return, $colour) ) {
                                        $return[] = $colour;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }*/

        // take the last colours from the lexer stream
        $lastColour = $this->_getLastIdentifiedColour($index);
        
        if ( !$this->_hasItem($return, $lastColour) ) {
            $return[] = $lastColour;
        }
        
        return $return;
    }

    private function _parseItem($index, $tokens) {
        
        // Match things like:
        //   - white sleeves
        //   - red cap
        if ( $this->_correspond( array(Tokens::T_COLOUR, Tokens::T_ELEMENT), $tokens ) ) {
            $this->_include($tokens[1], $tokens[0]);
            return;
        }

        // Match things like:
        //   - striped sleeves
        //   - quartered cap
        if ( $this->_correspond( array(Tokens::T_PATTERN, Tokens::T_ELEMENT), $tokens ) ) {
        $this->_include($tokens[1], null, array(array('pattern' => $tokens[0], 'colours' => $this->_findColoursToUncolouredPattern($index))));
            return;
        }

        // Match things like:
        //   - white cap and red pompom
        if ( $this->_correspond( array(Tokens::T_COLOUR, Tokens::T_ELEMENT, Tokens::T_AND, Tokens::T_COLOUR, Tokens::T_ELEMENT), $tokens ) ) {
            $this->_include($tokens[1], $tokens[0]);
            $this->_include($tokens[4], $tokens[3]);
            return;
        }

        // Match things like:
        //   - white cap WITH red pompom
        if ( $this->_correspond( array(Tokens::T_COLOUR, Tokens::T_ELEMENT, Tokens::T_WITH, Tokens::T_COLOUR, Tokens::T_ELEMENT), $tokens ) ) {
            $this->_include($tokens[1], $tokens[0]);
            $this->_include($tokens[4], $tokens[3]);
            return;
        }
        
              // Match things like:
        //   - white cap WITH pompom
        if ( $this->_correspond( array(Tokens::T_COLOUR, Tokens::T_ELEMENT, Tokens::T_WITH, Tokens::T_ELEMENT), $tokens ) ) {
            $this->_include($tokens[1], $tokens[0]);
            $this->_include($tokens[3], $tokens[0]);
            return;
        }

        // Match things like:
        //   - ..., striped sleeves and cap, ...
        if ( $this->_correspond( array(Tokens::T_PATTERN, Tokens::T_ELEMENT, Tokens::T_AND, Tokens::T_ELEMENT), $tokens ) ) {
            $this->_include($tokens[1], null, array(array('pattern' => $tokens[0], 'colours' => $this->_findColoursToUncolouredPattern($index))));
            $this->_include($tokens[3], null, array(array('pattern' => $tokens[0], 'colours' => $this->_findColoursToUncolouredPattern($index))));
            return;
        }

        // Match things like:
        //   - gold hooped sleeves
        //   - red quartered cap
        if ( $this->_correspond( array(Tokens::T_COLOUR, Tokens::T_PATTERN, Tokens::T_ELEMENT), $tokens ) ) {
            $this->_include($tokens[2], null, array(array('pattern' => $tokens[1], 'colours' => array($tokens[0]))));
            return;
        }

        // Match things like:
        //   - red striped sleeves and cap
        if ( $this->_correspond( array(Tokens::T_COLOUR, Tokens::T_PATTERN, Tokens::T_ELEMENT, Tokens::T_AND, Tokens::T_ELEMENT), $tokens ) ) {
            $this->_include($tokens[2], null, array(array('pattern' => $tokens[1], 'colours' => array($tokens[0]))));
            $this->_include($tokens[4], null, array(array('pattern' => $tokens[1], 'colours' => array($tokens[0]))));
            return;
        }

        // Match things like:
        //   - white quartered cap and red pompom
        if ( $this->_correspond( array(Tokens::T_COLOUR, Tokens::T_PATTERN, Tokens::T_ELEMENT, Tokens::T_AND, Tokens::T_COLOUR, Tokens::T_ELEMENT), $tokens ) ) {
            $this->_include($tokens[2], null, array(array('pattern' => $tokens[1], 'colours' => array($tokens[0]))));
            $this->_include($tokens[5], $tokens[4]);
            return;
        }

        // Match things like:
        //   - white quartered cap and red checked pompom
        if ( $this->_correspond( array(Tokens::T_COLOUR, Tokens::T_PATTERN, Tokens::T_ELEMENT, Tokens::T_AND, Tokens::T_COLOUR, Tokens::T_PATTERN, Tokens::T_ELEMENT), $tokens ) ) {
            $this->_include($tokens[2], null, array(array('pattern' => $tokens[1], 'colours' => array($tokens[0]))));
            $this->_include($tokens[6], null, array(array('pattern' => $tokens[5], 'colours' => array($tokens[4]))));
            return;
        }

        // Match things like:
        //   - red and white striped sleeves
        //   - green and gold quartered cap
        if ( $this->_correspond( array(Tokens::T_COLOUR, Tokens::T_AND, Tokens::T_COLOUR, Tokens::T_PATTERN, Tokens::T_ELEMENT), $tokens ) ) {
            $this->_include($tokens[4], null, array(array('pattern' => $tokens[3], 'colours' => array($tokens[0], $tokens[2]))));
            return;
        }

        // Match things like:
        //   - pink and black checked sleeves and cap
        if ( $this->_correspond( array(Tokens::T_COLOUR, Tokens::T_AND, Tokens::T_COLOUR, Tokens::T_PATTERN, Tokens::T_ELEMENT, Tokens::T_AND, Tokens::T_ELEMENT), $tokens ) ) {
            $this->_include($tokens[4], null, array(array('pattern' => $tokens[3], 'colours' => array($tokens[0], $tokens[2]))));
            $this->_include($tokens[6], null, array(array('pattern' => $tokens[3], 'colours' => array($tokens[0], $tokens[2]))));
            return;
        }

        // Match things like:
        //   - navy blue, red and white striped sleeves
        if ( $this->_correspond( array(Tokens::T_COLOUR, Tokens::T_COLOUR, Tokens::T_AND, Tokens::T_COLOUR, Tokens::T_PATTERN, Tokens::T_ELEMENT), $tokens ) ) {
            $this->_include($tokens[5], null, array(array('pattern' => $tokens[4], 'colours' => array($tokens[0], $tokens[1], $tokens[3]))));
            return;
        }

        // Match things like:
        //   - purple collar and sleeves
        //   - red cap and pom pom
        if ( $this->_correspond(array(Tokens::T_COLOUR, Tokens::T_ELEMENT, Tokens::T_AND, Tokens::T_ELEMENT), $tokens) ) {
            $this->_include($tokens[1], $tokens[0]);
            $this->_include($tokens[3], $tokens[0]);
            return;
        }

        // Match things like:
        //   - green sleeves and hooped cap
        //   - green sleeves and checked cap
        if ( $this->_correspond(array(Tokens::T_COLOUR, Tokens::T_ELEMENT, Tokens::T_AND, Tokens::T_PATTERN, Tokens::T_ELEMENT), $tokens) ) {
            $this->_include($tokens[1], $tokens[0]);
            $this->_include($tokens[4], null, array(array('pattern' => $tokens[3], 'colours' => array($tokens[0]))));
            return;
        }

        // Match things like:
        //   - royal blue  sleeves  with  swirl
        if ( $this->_correspond(array(Tokens::T_COLOUR, Tokens::T_ELEMENT, Tokens::T_WITH, Tokens::T_COLOUR, Tokens::T_PATTERN), $tokens) ) {
            $this->_include($tokens[1], $tokens[0], array(array('pattern' => $tokens[4], 'colours' => array($tokens[3]))));
            return;
        }

        // Match things like:
        //   - blue sleeves with red 3 stars and red pompom
        if ( $this->_correspond(array(Tokens::T_COLOUR, Tokens::T_ELEMENT, Tokens::T_WITH, Tokens::T_COLOUR, Tokens::T_PATTERN, Tokens::T_AND, Tokens::T_COLOUR, Tokens::T_ELEMENT), $tokens) ) {
            $this->_include($tokens[1], $tokens[0], array(array('pattern' => $tokens[4], 'colours' => array($tokens[3]))));
            $this->_include($tokens[7], $tokens[6]);
            return;
        }

        // Match things like:
        //   - green shirt with red and yellow hoops
        if ( $this->_correspond(array(Tokens::T_COLOUR, Tokens::T_ELEMENT, Tokens::T_WITH, Tokens::T_COLOUR, Tokens::T_AND, Tokens::T_COLOUR, Tokens::T_PATTERN), $tokens) ) {
            $this->_include($tokens[1], $tokens[0], array(array('pattern' => $tokens[6], 'colours' => array($tokens[3], $tokens[5]))));
            return;
        }
        
        // Match things like:
        //   - blue sleeves with red 3 stars and red checked pompom
        if ( $this->_correspond(array(Tokens::T_COLOUR, Tokens::T_ELEMENT, Tokens::T_WITH, Tokens::T_COLOUR, Tokens::T_PATTERN, Tokens::T_AND, Tokens::T_COLOUR, Tokens::T_PATTERN, Tokens::T_ELEMENT), $tokens) ) {
            $this->_include($tokens[1], $tokens[0], array(array('pattern' => $tokens[4], 'colours' => array($tokens[3]))));
            $this->_include($tokens[8], null, array(array('pattern' => $tokens[7], 'colours' => array($tokens[6]))));
            return;
        }

        // Match things like:
        //   - blue sleeves with red 3 stars and red pompom with white checked
        if ( $this->_correspond(array(Tokens::T_COLOUR, Tokens::T_ELEMENT, Tokens::T_WITH, Tokens::T_COLOUR, Tokens::T_PATTERN, Tokens::T_AND,Tokens::T_COLOUR, Tokens::T_ELEMENT, Tokens::T_WITH, Tokens::T_COLOUR, Tokens::T_PATTERN), $tokens) ) {
            $this->_include($tokens[1], $tokens[0], array(array('pattern' => $tokens[4], 'colours' => array($tokens[3]))));
            $this->_include($tokens[7], $tokens[6], array(array('pattern' => $tokens[10], 'colours' => array($tokens[9]))));
            return;
        }


        // Next tests is directed to the first chunk of the string.
        // This chunk is special, once it is related to shirt element
        // So, the name of the element (shirt) is not present
        // 
        // THESE CONDITION IS COMMENTED, AS ASKED BY STRING:
        //      - Green, Red And White Checked Cap, Gold Star And Armbands, Red Striped Sleeves
        // -> in which, shirt information is after second chunck
        // 
        //if ( $index == 0 ) {

            // Match things like:
            //   - ..., fluer de lys, ...
            if ( $this->_correspond(array(Tokens::T_PATTERN), $tokens) ) {
                $this->_include(array('match' => Parser::ELEMENT_SHIRT, 'token' => Tokens::T_ELEMENT), null, array(array('pattern' => $tokens[0], 'colours' => array())));
                return;
            }

            // Match things like:
            //   - ..., pink fluer de lys, ...
            if ( $this->_correspond(array(Tokens::T_COLOUR, Tokens::T_PATTERN), $tokens) ) {
                $this->_include(
                        array('match' => Parser::ELEMENT_SHIRT, 'token' => Tokens::T_ELEMENT),
                        null,
                        array(array('pattern' => $tokens[1], 'colours' => array($tokens[0]))),
                        Tokens::backToString($tokens)
                        );
                return;
            }

            // Match things like:
            //   - ..., purple and white hoops, ...
            if ( $this->_correspond(array(Tokens::T_COLOUR, Tokens::T_AND, Tokens::T_COLOUR, Tokens::T_PATTERN), $tokens) ) {
                $this->_include(array('match' => Parser::ELEMENT_SHIRT, 'token' => Tokens::T_ELEMENT), null, array(array('pattern' => $tokens[3], 'colours' => array($tokens[0], $tokens[2]))));
                return;
            }

            // Match things like:
            //   - ..., white shield with black lion, ...
            if ( $this->_correspond(array(Tokens::T_COLOUR, Tokens::T_PATTERN, Tokens::T_WITH, Tokens::T_COLOUR, Tokens::T_PATTERN), $tokens) ) {
                $this->_include(array('match' => Parser::ELEMENT_SHIRT, 'token' => Tokens::T_ELEMENT), null, array(
                    array('pattern' => $tokens[1], 'colours' => array($tokens[0])),
                    array('pattern' => $tokens[4], 'colours' => array($tokens[3]))
                ));
                return;
            }

            // Match things like:
            //   - ..., gold matese cross and cap, ...
            if ( $this->_correspond(array(Tokens::T_COLOUR, Tokens::T_PATTERN, Tokens::T_AND, Tokens::T_ELEMENT), $tokens) ) {
                $this->_include(array('match' => Parser::ELEMENT_SHIRT, 'token' => Tokens::T_ELEMENT), null, array(array('pattern' => $tokens[1], 'colours' => array($tokens[0]))));
                $this->_include($tokens[3], $tokens[0]);
                return;
            }

            // Match things like:
            //   - ..., pink spots and hooped sleeves, ...
            if ( $this->_correspond(array(Tokens::T_COLOUR, Tokens::T_PATTERN, Tokens::T_AND, Tokens::T_PATTERN, Tokens::T_ELEMENT), $tokens) ) {
                $this->_include(array('match' => Parser::ELEMENT_SHIRT, 'token' => Tokens::T_ELEMENT), null, array(array('pattern' => $tokens[1], 'colours' => array($tokens[0]))));
                $this->_include($tokens[4], null, array(array('pattern' => $tokens[3], 'colours' => array($tokens[0]))));
                return;
            }
        //}

        //---------------------------------------------------------------------
        //
        // if we came here, so none of the above grammar were found.
        // From here and on, you MUST NO include a return when find out another combination.
        //
        //---------------------------------------------------------------------

        //
        //-- BEGIN - Exceptions
        //

        // like: pink horseshoes and sleeves (horseshoes is unknown)
        if ( $this->_correspond(array(Tokens::T_COLOUR, Tokens::T_UNKOWN, Tokens::T_AND, Tokens::T_ELEMENT), $tokens) ) {
            $this->_include($tokens[3], $tokens[0]);
            // note that here we dont have the return statement.
        }

        // like: black cap with pink horseshoes (horseshoes is unknown)
        if ( $this->_correspond(array(Tokens::T_COLOUR, Tokens::T_ELEMENT, Tokens::T_WITH, Tokens::T_COLOUR, Tokens::T_UNKOWN), $tokens) ) {
            $this->_include($tokens[1], $tokens[0]);
            // note that here we dont have the return statement.
        }

        // like: black cap with horseshoes (horseshoes is unknown)
        if ( $this->_correspond(array(Tokens::T_COLOUR, Tokens::T_ELEMENT, Tokens::T_WITH, Tokens::T_UNKOWN), $tokens) ) {
            $this->_include($tokens[1], $tokens[0]);
            // note that here we dont have the return statement.
        }

        // like: quartered cap with pink pom pom
        if ( $this->_correspond(array(Tokens::T_PATTERN, Tokens::T_ELEMENT, Tokens::T_WITH, Tokens::T_COLOUR, Tokens::T_ELEMENT), $tokens) ) {
            
            $lastColour = $this->_getLastIdentifiedColour($index);
            $this->_include(
                $tokens[1], 
                null, 
                array(
                    array('pattern' => $tokens[0], 'colours' => array($lastColour))
                )
            );
            $this->_include($tokens[4], $tokens[3]);
            // note that here we dont have the return statement.
        }

        // like: red, green celtic cross, sleeves and cap
        if ( $this->_correspond(array(Tokens::T_ELEMENT, Tokens::T_AND, Tokens::T_ELEMENT), $tokens) ) {
            $lastColour = $this->_getLastIdentifiedColour($index);
            if (!empty($lastColour)) {
                $this->_include($tokens[0], $lastColour);
                $this->_include($tokens[2], $lastColour);
                // note that here we dont have the return statement.
            } 
        }
        
        // like: orange, royal blue triangular quarters and hooped sleeves
        if ($this->_endsWith(array(Tokens::T_AND, Tokens::T_PATTERN, Tokens::T_ELEMENT), $tokens)) {
            $idx = count($tokens) -1;
            $this->_include($tokens[$idx], null, array(array('pattern' => $tokens[$idx-1], 'colours' => $this->_findColoursToUncolouredPattern($index))));
            // note that here we dont have the return statement.
        }
        
        // like: navy, red ring, lightning bolt and sleeves, quartered cap
        if ($this->_endsWith(array(Tokens::T_AND, Tokens::T_ELEMENT), $tokens)) {
            $idx = count($tokens) -1;
            $lastColour = $this->_getLastIdentifiedColour($index);
            if (!empty($lastColour)) {
                $this->_include($tokens[$idx], $lastColour, null);
                // note that here we dont have the return statement.
            } 
        }
        
        // like: white and silver band and armbands (band is a shirt pattern and armbans is and element).
        if ($this->_beginsWith(array(Tokens::T_COLOUR, Tokens::T_AND, Tokens::T_COLOUR, Tokens::T_PATTERN, Tokens::T_AND), $tokens)) {
            $this->_include(
                    array('match' => Parser::ELEMENT_SHIRT, 'token' => Tokens::T_ELEMENT),
                    null,
                    array(
                        array('pattern' => $tokens[3], 'colours' => array($tokens[0], $tokens[2]))
                    )
            );
            // note that here we dont have the return statement.
        }
        
        // like: maroon diamond sash and seams cap
        if ( $this->_beginsWith(array(Tokens::T_COLOUR,  Tokens::T_PATTERN, Tokens::T_ELEMENT), $tokens) ) {
            $this->_include(
                    $tokens[2],
                    null,
                    array(
                        array('pattern' => $tokens[1], 'colours' => array($tokens[0]))
                    )
            );
            // note that here we dont have the return statement.
        }
            
        
        //
        //-- END - Exceptions
        //




        foreach ($tokens as $token) {
            if ( $token['token'] == Tokens::T_UNKOWN ) {
                $this->_addError(
                        array(
                            'type' => Parser::E_TOKEN_UNKNOWN,
                            'message' => sprintf("The word <b>%s (originally: %s)</b> is unknown. It is not a colour, pattern or an element", $token['match'], $this->getOriginalTerm($token['match'])),
                            'expression' => $token['match']
                        )
                );
            }
        }

        $this->_addError(
                array(
                    'type' => Parser::E_NO_SEMANTICAL_MEANING,
                    'message' => sprintf("The sequence <b>%s</b> has no semantical meaning", Tokens::backToString($tokens)),
                    'expression' => null
                )
        );
    }

    private function _include($pElementName, $pElementColour, $patterns = null, $original_chunk = "") {
        $elementColour = null;
        $elementName = $pElementName['match'];
        
        if ( !empty($pElementColour['match']) ) {
            $elementColour = $pElementColour['match'];
        }
                
        if (!empty($this->elementsIdentified[$elementName])) {
            if ( !empty($original_chunk) ) {
                $this->_addError(
                        array(
                            'type' => Parser::E_NO_SEMANTICAL_MEANING,
                            'message' => sprintf(
                                    "Cannot apply <b>%s</b> to '%s' because '%s' has gotten something earlier.",
                                    $original_chunk,
                                    $elementName,
                                    $elementName
                            ),
                            'expression' => $original_chunk
                        )
                );
            }
            return false;
        }
        $this->elementsIdentified[$elementName] = true;
        
        $element = array();
        $element[self::S_ELEMENT_NAME] = $elementName;

        if ( !empty($patterns) ){

            $includePatterns = array();
            foreach ($patterns as $arrPattern) {
                $itPattern = $arrPattern['pattern'];
                $itColours = $arrPattern['colours'];
                
                // Rule 1: we have to check if this pattern is related to this element
                $checkPattern = $this->helper->checkPatternBelongToElement($itPattern['match'], $element[self::S_ELEMENT_NAME]);
                if (empty($checkPattern)) {
                    // here we have a problem: an identified pattern is not related to the element
                    $newPattern = $this->lexer->findPatternToElement($element[self::S_ELEMENT_NAME], !empty($itPattern['original_term']) ? $itPattern['original_term'] : $itPattern['match']);
                    
                    if ( !empty($newPattern) ) {
                        $itPattern = $newPattern;
                    } else {
                        $this->_addError(
                                array(
                                    'type' => Parser::E_PATTERN_NOT_RELATED_TO_ELEMENT,
                                    'message' => sprintf(
                                            "The pattern %s (originally: <b>%s</b>) cannot be applied on <b>%s</b>. ".
                                            "Avaliable pattern(s) for this element is(are): ['%s'].",
                                            $itPattern['match'],
                                            $itPattern['original_term'],
                                            $element[self::S_ELEMENT_NAME],
                                            implode("', '", $this->lexer->getAvaliablePatternsForElement($element[self::S_ELEMENT_NAME]))
                                    ),
                                    'expression' => $itPattern['original_term']
                                )
                        );
                        continue;
                    }
                }
                
                // Rule 2: when there are more than one colour for a given pattern, the first colour should
                // be applied to the element and the remaining colours are applied to the pattern
                $arrOfColours = Tokens::getArrayOf($itColours, 'match');
                if (count($arrOfColours) > 1) {
                    if ( empty($elementColour) ) {
                        $firstColour = array_shift($arrOfColours);
                        $elementColour = $firstColour;
                    }
                }
                
                $pattern = array();
                $pattern[self::S_DESCRIPTION] = $itPattern['match'];
                $pattern[self::S_COLOURS] = $arrOfColours;
                $includePatterns[] = $pattern;
                
            }
            $element[self::S_PATTERNS] = $includePatterns;
        }
        
        if ( !empty($elementColour) ) {
            $element[self::S_COLOUR] = $elementColour;
        }

        $this->_addElement($element);
        
        return true;
    }


    /**
     *
     * @param array $identifiers
     * @param array $tokens
     */
    private function _correspond($identifiers, $tokens) {

        $result = count($identifiers) == count($tokens);

        if ( $result ) {
            $idx = 0;
            foreach ($identifiers as $ident) {
                $result = $result && ($ident == $tokens[$idx]['token']);
                $idx++;
            }
        }

        return $result;
    }
    
    private function _beginsWith($identifiers, $tokens) {
        $countIdent = count($identifiers);
        $count = count($tokens);
        
        if ($count == 0 || $countIdent > $count) {
            return false;
        }

        $beginsWith = true;
        $idx = 0;
        for($jdx=0; $jdx<$countIdent; $jdx++) {
            $beginsWith = $beginsWith && ($tokens[$idx++]['token'] == $identifiers[$jdx]);
        }
        return $beginsWith;
    }
    
    /**
     * 
     * @param type $identifiers
     * @param type $tokens
     */
    private function _endsWith($identifiers, $tokens) {
        $count = count($tokens);
        $countIdent = count($identifiers);
        
        if ($count == 0 || $countIdent > $count) {
            return false;
        }

        $endsWith = true;
        $idx = $count-1;
        for($jdx=$countIdent-1; $jdx>=0; $jdx--) {
            $endsWith = $endsWith && ($tokens[$idx--]['token'] == $identifiers[$jdx]);
        }
        return $endsWith;
    }

    private function _intelliStrip($tokens) {
        $result = array();

        $count = count($tokens);
        $head = 0;
        $currGrp = 0;

        $last = null;
        $next = null;

        for( ; $head<$count ; ) {
            $incriseToHead = 0;
            $incriseToGrp = 0;

            $item = $tokens[$head];

            if ( $item['token'] == Tokens::T_COMMA) {

                if (
                        ( array_key_exists($head-1, $tokens) && $tokens[$head-1]['token'] == Tokens::T_COLOUR ) &&
                        ( array_key_exists($head+1, $tokens) && $tokens[$head+1]['token'] == Tokens::T_COLOUR )
                        && (
                           (!array_key_exists($head-2, $tokens)) ||
                           (array_key_exists($head-2, $tokens) && $tokens[$head-2]['token'] == Tokens::T_COMMA )
                        )
                ) {
                    //        |
                    //       \/
                    //   RED , GREEN and white quartered cap
                    //
                    $incriseToGrp = -1;
                }

                if (
                     ( array_key_exists($head-2, $tokens) && ($tokens[$head-2]['token']  == Tokens::T_COLOUR) ) &&
                     ( array_key_exists($head+1, $tokens) && ($tokens[$head+1]['token']  == Tokens::T_ELEMENT) ) &&
                     ( array_key_exists($head+2, $tokens) && ($tokens[$head+2]['token']  == Tokens::T_AND) ) &&
                     ( array_key_exists($head+3, $tokens) && ($tokens[$head+3]['token']  == Tokens::T_ELEMENT) )
                ) {
                    //                      |
                    //                     \/
                    //   Black, RED STRIPE , ARMBANDS AND CAP
                    //
                    //                           |
                    //                          \/
                    //   Orange, PURPLE CHEVRONS, SLEEVES AND CAP
                    //

                    $result[$currGrp+1][] = $tokens[$head-2];
                    $result[$currGrp+1][] = $tokens[$head+1];

                    $result[$currGrp+2][] = $tokens[$head-2];
                    $result[$currGrp+2][] = $tokens[$head+3];

                    $incriseToGrp = 3;
                    $incriseToHead = 3;
                }

                $head = ++$head + $incriseToHead;
                $currGrp = ++$currGrp + $incriseToGrp;
            }
            else {
                $result[$currGrp][] = $item;
                $head++;
            }

            // this item becomes the last
            $last = $item;
        }

        return $result;
    }
    
    public function _postChecks() {
        $baseElement = $this->findElement('base');
        
        // When there's no base colour specified ...
        if ( null == $baseElement ) {
            
            // 1: the pattern specified for the shirt is also applied to the sleeves
            $shirtElement = $this->findElement('shirt');
            if ( !empty($shirtElement[self::S_PATTERNS]) ) {

                $sleevesElement = $this->findElement('sleeves');
                if ( null == $sleevesElement ) {
                    $newElement[self::S_ELEMENT_NAME] = 'sleeves';
                    $newElement[self::S_PATTERNS] = $shirtElement[self::S_PATTERNS];
                    $this->elementsFound[] = $newElement;
                }
                
            }
            
            
            // 2: the first colour found in the string is applied to the base
            $firstColour = $this->_getFirstIdentifiedColour();
            if ( !empty($firstColour) ) {
                $this->_include(array('match' => 'base', 'token' => Tokens::T_ELEMENT), $firstColour);
            }
        }
        
//        echo "<pre>";
//        print_r($this->elementsFound);
//        print_r($this->errors);
//        die();
    }
    
}
