<?php

namespace DBLS\Bundle\JerseyColoursBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Doctrine\Common\Collections\ArrayCollection;

/**
 * Trainer
 *
 * @ORM\Table()
 * @ORM\Entity(repositoryClass="DBLS\Bundle\JerseyColoursBundle\Entity\TrainerRepository")
 */
class Trainer
{
    /**
     * @var integer
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=255)
     */
    private $name;

    /**
     * @ORM\OneToMany(targetEntity="Jersey", mappedBy="trainer")
     */
    private $jerseys;

    /*
     * 
     */
    public function __construct()
    {
       $this->jerseys = new ArrayCollection();
    }


    /**
     * Get id
     *
     * @return integer 
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set name
     *
     * @param string $name
     * @return Trainer
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get name
     *
     * @return string 
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Add jerseys
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $jerseys
     * @return Race
     */
    public function addJersey( \DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $jerseys )
    {
        $this->jerseys[] = $jerseys;

        return $this;
    }

    /**
     * Remove jerseys
     *
     * @param \DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $jerseys
     */
    public function removeJersey( \DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $jerseys )
    {
       $this->jerseys->removeElement( $jerseys );
    }

    /**
     * Get jerseys
     *
     * @return \Doctrine\Common\Collections\Collection 
     */
    public function getJerseys()
    {
       return $this->jerseys;
    }
}
