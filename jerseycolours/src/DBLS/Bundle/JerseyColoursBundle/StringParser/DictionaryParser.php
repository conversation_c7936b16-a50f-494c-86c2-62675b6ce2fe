<?php

namespace DBLS\Bundle\JerseyColoursBundle\StringParser;

/**
 * Description of DictionaryParser
 *
 * <AUTHOR>
 */
class DictionaryParser {
    
    const T_SPACEFLAG = "ssppaaccee";
    
    /*
     * @var arrayExtensive
     */
    static protected $arrayExtensive = 
            array(
                'zzeerroo','oonnee','ttwwoo','tthhrreeee','ffoouurr','ffiivvee',
                'ssiixx','sseevveenn','eeiigghhtt','nniinnee',
            );
    
    /*
     * @var arrayNumber
     */
    static protected  $arrayNumber = 
            array(
                0,1,2,3,4,5,6,7,8,9,
            );
    
    
    /*
     * Convert number to extensive
     * @param string
     */
    public static function convertNumberToExtensive($string)
    {        
        return str_replace(self::$arrayNumber, self::$arrayExtensive, $string);
    }
    
    /*
     * Convert extensive to number
     * @param string
     */
    public static function convertExtensiveToNumber($string)
    {
        $string = str_replace(self::T_SPACEFLAG, " ", $string);
        return str_replace(self::$arrayExtensive, self::$arrayNumber, $string);
    }
    
    /*
     * Replacement string to remove caracters special
     * @param string
     */
    public static function replacementString($string)
    {
        $patterns = array(
            " ",
        );
        
        $replacements = array(
            self::T_SPACEFLAG,
        );
        
        //remove caracteres special
        $string = preg_replace("/&([a-z])[a-z]+;,/i", "",trim($string));
        
        return str_replace($patterns, $replacements, $string);
    }
    
    /*
     * Get string formated
     * @param string
     */
    public static function getStringFormated($string)
    {
        $string = strtolower($string);
        
        //remove white spaces
        $words = self::replacementString($string);
        
        //check string is not varchar
        if( !ctype_alpha($words) ){
            
            $words = self::convertNumberToExtensive($words);
            
        }
        
        return strtolower($words);
        
    }
    
    /*
     * Return string formated for parse
     * @param string
     */
    public static function returnStringFormated($string)
    {
        return self::convertExtensiveToNumber($string);
    }
    
}