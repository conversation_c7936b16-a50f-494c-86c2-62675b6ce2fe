# frozen_string_literal: true

module JsonApiSerialization
  extend ActiveSupport::Concern

  private

  def serialize_resource(resource, serializer_class, options = {})
    serializer_class.new(resource, options).serializable_hash
  end

  def serialize_collection(collection, serializer_class, options = {})
    serializer_class.new(collection, options).serializable_hash
  end

  def render_json_api(resource, serializer_class, status: :ok, **options)
    if resource.respond_to?(:each)
      serialized = serialize_collection(resource, serializer_class, options)
    else
      serialized = serialize_resource(resource, serializer_class, options)
    end
    
    render json: serialized, status: status
  end
end
