"use client";import{useFocusRing as Ie}from"@react-aria/focus";import{useHover as He}from"@react-aria/interactions";import T,{createContext as Fe,useCallback as oe,useContext as Be,useEffect as pe,useMemo as K,useRef as ie,useState as ue}from"react";import{useActivePress as Ue}from'../../hooks/use-active-press.js';import{useElementSize as Ne}from'../../hooks/use-element-size.js';import{useEvent as y}from'../../hooks/use-event.js';import{useEventListener as we}from'../../hooks/use-event-listener.js';import{useId as re}from'../../hooks/use-id.js';import{useIsoMorphicEffect as Ke}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as _e}from'../../hooks/use-latest-value.js';import{useOnDisappear as We}from'../../hooks/use-on-disappear.js';import{useOutsideClick as Ve}from'../../hooks/use-outside-click.js';import{useOwnerDocument as ce}from'../../hooks/use-owner.js';import{useResolveButtonType as je}from'../../hooks/use-resolve-button-type.js';import{MainTreeProvider as Ae,useMainTreeNode as $e,useRootContainers as Je}from'../../hooks/use-root-containers.js';import{useScrollLock as Xe}from'../../hooks/use-scroll-lock.js';import{optionalRef as qe,useSyncRefs as Y}from'../../hooks/use-sync-refs.js';import{Direction as I,useTabDirection as Ce}from'../../hooks/use-tab-direction.js';import{transitionDataAttributes as Oe,useTransition as xe}from'../../hooks/use-transition.js';import{CloseProvider as De}from'../../internal/close-provider.js';import{FloatingProvider as ze,useFloatingPanel as Ye,useFloatingPanelProps as Qe,useFloatingReference as Ze,useResolvedAnchor as et}from'../../internal/floating.js';import{Hidden as me,HiddenFeatures as Te}from'../../internal/hidden.js';import{OpenClosedProvider as tt,ResetOpenClosedProvider as ot,State as Q,useOpenClosed as Le}from'../../internal/open-closed.js';import{useSlice as Z}from'../../react-glue.js';import{isDisabledReactIssue7711 as he}from'../../utils/bugs.js';import*as Me from'../../utils/dom.js';import{Focus as H,FocusResult as Ee,FocusableMode as rt,focusIn as W,getFocusableElements as Se,isFocusableElement as nt}from'../../utils/focus-management.js';import{match as ne}from'../../utils/match.js';import'../../utils/micro-task.js';import{getOwnerDocument as lt}from'../../utils/owner.js';import{RenderFeatures as de,forwardRefWithAs as ee,mergeProps as be,useRender as le}from'../../utils/render.js';import{Keys as V}from'../keyboard.js';import{Portal as at,useNestedPortals as st}from'../portal/portal.js';import{PopoverStates as c}from'./popover-machine.js';import{PopoverContext as pt,usePopoverMachine as it,usePopoverMachineContext as ye}from'./popover-machine-glue.js';let ge=Fe(null);ge.displayName="PopoverGroupContext";function Ge(){return Be(ge)}let fe=Fe(null);fe.displayName="PopoverPanelContext";function ut(){return Be(fe)}let ct="div";function dt(b,M){var k;let F=re(),{__demoMode:B=!1,...d}=b,r=it({id:F,__demoMode:B}),g=ie(null),t=Y(M,qe(n=>{g.current=n})),[_,f,o,O,E]=Z(r,oe(n=>[n.popoverState,n.button,n.panel,n.buttonId,n.panelId],[])),P=ce((k=g.current)!=null?k:f),A=_e(O),a=_e(E),i=K(()=>({buttonId:A,panelId:a,close:r.actions.close}),[A,a,r]),u=Ge(),l=u==null?void 0:u.registerPopover,v=y(()=>{var n;return(n=u==null?void 0:u.isFocusWithinPopoverGroup())!=null?n:(P==null?void 0:P.activeElement)&&((f==null?void 0:f.contains(P.activeElement))||(o==null?void 0:o.contains(P.activeElement)))});pe(()=>l==null?void 0:l(i),[l,i]);let[m,j]=st(),$=$e(f),J=Je({mainTreeNode:$,portals:m,defaultContainers:[{get current(){return r.state.button}},{get current(){return r.state.panel}}]});we(P==null?void 0:P.defaultView,"focus",n=>{var D,z,G,U,L,N;n.target!==window&&Me.isHTMLorSVGElement(n.target)&&r.state.popoverState===c.Open&&(v()||r.state.button&&r.state.panel&&(J.contains(n.target)||(z=(D=r.state.beforePanelSentinel.current)==null?void 0:D.contains)!=null&&z.call(D,n.target)||(U=(G=r.state.afterPanelSentinel.current)==null?void 0:G.contains)!=null&&U.call(G,n.target)||(N=(L=r.state.afterButtonSentinel.current)==null?void 0:L.contains)!=null&&N.call(L,n.target)||r.actions.close()))},!0);let x=_===c.Open;Ve(x,J.resolveContainers,(n,D)=>{r.actions.close(),nt(D,rt.Loose)||(n.preventDefault(),f==null||f.focus())});let X=K(()=>({open:_===c.Open,close:r.actions.refocusableClose}),[_,r]),te=Z(r,oe(n=>ne(n.popoverState,{[c.Open]:Q.Open,[c.Closed]:Q.Closed}),[])),q={ref:t},C=le();return T.createElement(Ae,{node:$},T.createElement(ze,null,T.createElement(fe.Provider,{value:null},T.createElement(pt.Provider,{value:r},T.createElement(De,{value:r.actions.refocusableClose},T.createElement(tt,{value:te},T.createElement(j,null,C({ourProps:q,theirProps:d,slot:X,defaultTag:ct,name:"Popover"}))))))))}let ft="button";function Pt(b,M){let F=re(),{id:B=`headlessui-popover-button-${F}`,disabled:d=!1,autoFocus:r=!1,...g}=b,t=ye("Popover.Button"),[_,f,o,O,E,P,A]=Z(t,oe(e=>[e.popoverState,t.selectors.isPortalled(e),e.button,e.buttonId,e.panel,e.panelId,e.afterButtonSentinel],[])),a=ie(null),i=`headlessui-focus-sentinel-${re()}`,u=Ge(),l=u==null?void 0:u.closeOthers,m=ut()!==null;pe(()=>{if(!m)return t.actions.setButtonId(B),()=>t.actions.setButtonId(null)},[m,B,t]);let[j]=ue(()=>Symbol()),$=Y(a,M,Ze(),y(e=>{if(!m){if(e)t.state.buttons.current.push(j);else{let p=t.state.buttons.current.indexOf(j);p!==-1&&t.state.buttons.current.splice(p,1)}t.state.buttons.current.length>1&&console.warn("You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported."),e&&t.actions.setButton(e)}})),J=Y(a,M),x=ce(a),X=y(e=>{var p,h,S;if(m){if(t.state.popoverState===c.Closed)return;switch(e.key){case V.Space:case V.Enter:e.preventDefault(),(h=(p=e.target).click)==null||h.call(p),t.actions.close(),(S=t.state.button)==null||S.focus();break}}else switch(e.key){case V.Space:case V.Enter:e.preventDefault(),e.stopPropagation(),t.state.popoverState===c.Closed?(l==null||l(t.state.buttonId),t.actions.open()):t.actions.close();break;case V.Escape:if(t.state.popoverState!==c.Open)return l==null?void 0:l(t.state.buttonId);if(!a.current||x!=null&&x.activeElement&&!a.current.contains(x.activeElement))return;e.preventDefault(),e.stopPropagation(),t.actions.close();break}}),te=y(e=>{m||e.key===V.Space&&e.preventDefault()}),q=y(e=>{var p,h;he(e.currentTarget)||d||(m?(t.actions.close(),(p=t.state.button)==null||p.focus()):(e.preventDefault(),e.stopPropagation(),t.state.popoverState===c.Closed?(l==null||l(t.state.buttonId),t.actions.open()):t.actions.close(),(h=t.state.button)==null||h.focus()))}),C=y(e=>{e.preventDefault(),e.stopPropagation()}),{isFocusVisible:k,focusProps:n}=Ie({autoFocus:r}),{isHovered:D,hoverProps:z}=He({isDisabled:d}),{pressed:G,pressProps:U}=Ue({disabled:d}),L=_===c.Open,N=K(()=>({open:L,active:G||L,disabled:d,hover:D,focus:k,autofocus:r}),[L,D,k,G,d,r]),ae=je(b,o),Pe=m?be({ref:J,type:ae,onKeyDown:X,onClick:q,disabled:d||void 0,autoFocus:r},n,z,U):be({ref:$,id:O,type:ae,"aria-expanded":_===c.Open,"aria-controls":E?P:void 0,disabled:d||void 0,autoFocus:r,onKeyDown:X,onKeyUp:te,onClick:q,onMouseDown:C},n,z,U),se=Ce(),s=y(()=>{if(!Me.isHTMLElement(t.state.panel))return;let e=t.state.panel;function p(){ne(se.current,{[I.Forwards]:()=>W(e,H.First),[I.Backwards]:()=>W(e,H.Last)})===Ee.Error&&W(Se().filter(S=>S.dataset.headlessuiFocusGuard!=="true"),ne(se.current,{[I.Forwards]:H.Next,[I.Backwards]:H.Previous}),{relativeTo:t.state.button})}p()}),R=le();return T.createElement(T.Fragment,null,R({ourProps:Pe,theirProps:g,slot:N,defaultTag:ft,name:"Popover.Button"}),L&&!m&&f&&T.createElement(me,{id:i,ref:A,features:Te.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:s}))}let vt="div",mt=de.RenderStrategy|de.Static;function ke(b,M){let F=re(),{id:B=`headlessui-popover-backdrop-${F}`,transition:d=!1,...r}=b,g=ye("Popover.Backdrop"),t=Z(g,oe(l=>l.popoverState,[])),[_,f]=ue(null),o=Y(M,f),O=Le(),[E,P]=xe(d,_,O!==null?(O&Q.Open)===Q.Open:t===c.Open),A=y(l=>{if(he(l.currentTarget))return l.preventDefault();g.actions.close()}),a=K(()=>({open:t===c.Open}),[t]),i={ref:o,id:B,"aria-hidden":!0,onClick:A,...Oe(P)};return le()({ourProps:i,theirProps:r,slot:a,defaultTag:vt,features:mt,visible:E,name:"Popover.Backdrop"})}let Tt="div",Et=de.RenderStrategy|de.Static;function bt(b,M){let F=re(),{id:B=`headlessui-popover-panel-${F}`,focus:d=!1,anchor:r,portal:g=!1,modal:t=!1,transition:_=!1,...f}=b,o=ye("Popover.Panel"),O=Z(o,o.selectors.isPortalled),[E,P,A,a,i]=Z(o,oe(s=>[s.popoverState,s.button,s.__demoMode,s.beforePanelSentinel,s.afterPanelSentinel],[])),u=`headlessui-focus-sentinel-before-${F}`,l=`headlessui-focus-sentinel-after-${F}`,v=ie(null),m=et(r),[j,$]=Ye(m),J=Qe();m&&(g=!0);let[x,X]=ue(null),te=Y(v,M,m?j:null,o.actions.setPanel,X),q=ce(P),C=ce(v);Ke(()=>(o.actions.setPanelId(B),()=>o.actions.setPanelId(null)),[B,o]);let k=Le(),[n,D]=xe(_,x,k!==null?(k&Q.Open)===Q.Open:E===c.Open);We(n,P,o.actions.close),Xe(A?!1:t&&n,C);let G=y(s=>{var R;switch(s.key){case V.Escape:if(o.state.popoverState!==c.Open||!v.current||C!=null&&C.activeElement&&!v.current.contains(C.activeElement))return;s.preventDefault(),s.stopPropagation(),o.actions.close(),(R=o.state.button)==null||R.focus();break}});pe(()=>{var s;b.static||E===c.Closed&&((s=b.unmount)==null||s)&&o.actions.setPanel(null)},[E,b.unmount,b.static,o]),pe(()=>{if(A||!d||E!==c.Open||!v.current)return;let s=C==null?void 0:C.activeElement;v.current.contains(s)||W(v.current,H.First)},[A,d,v.current,E]);let U=K(()=>({open:E===c.Open,close:o.actions.refocusableClose}),[E,o]),L=be(m?J():{},{ref:te,id:B,onKeyDown:G,onBlur:d&&E===c.Open?s=>{var e,p,h,S,w;let R=s.relatedTarget;R&&v.current&&((e=v.current)!=null&&e.contains(R)||(o.actions.close(),((h=(p=a.current)==null?void 0:p.contains)!=null&&h.call(p,R)||(w=(S=i.current)==null?void 0:S.contains)!=null&&w.call(S,R))&&R.focus({preventScroll:!0})))}:void 0,tabIndex:-1,style:{...f.style,...$,"--button-width":Ne(P,!0).width},...Oe(D)}),N=Ce(),ae=y(()=>{let s=v.current;if(!s)return;function R(){ne(N.current,{[I.Forwards]:()=>{var p;W(s,H.First)===Ee.Error&&((p=o.state.afterPanelSentinel.current)==null||p.focus())},[I.Backwards]:()=>{var e;(e=o.state.button)==null||e.focus({preventScroll:!0})}})}R()}),Pe=y(()=>{let s=v.current;if(!s)return;function R(){ne(N.current,{[I.Forwards]:()=>{if(!o.state.button)return;let e=Se(),p=e.indexOf(o.state.button),h=e.slice(0,p+1),w=[...e.slice(p+1),...h];for(let ve of w.slice())if(ve.dataset.headlessuiFocusGuard==="true"||x!=null&&x.contains(ve)){let Re=w.indexOf(ve);Re!==-1&&w.splice(Re,1)}W(w,H.First,{sorted:!1})},[I.Backwards]:()=>{var p;W(s,H.Previous)===Ee.Error&&((p=o.state.button)==null||p.focus())}})}R()}),se=le();return T.createElement(ot,null,T.createElement(fe.Provider,{value:B},T.createElement(De,{value:o.actions.refocusableClose},T.createElement(at,{enabled:g?b.static||n:!1,ownerDocument:q},n&&O&&T.createElement(me,{id:u,ref:a,features:Te.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:ae}),se({ourProps:L,theirProps:f,slot:U,defaultTag:Tt,features:Et,visible:n,name:"Popover.Panel"}),n&&O&&T.createElement(me,{id:l,ref:i,features:Te.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:Pe})))))}let yt="div";function gt(b,M){let F=ie(null),B=Y(F,M),[d,r]=ue([]),g=y(a=>{r(i=>{let u=i.indexOf(a);if(u!==-1){let l=i.slice();return l.splice(u,1),l}return i})}),t=y(a=>(r(i=>[...i,a]),()=>g(a))),_=y(()=>{var u;let a=lt(F);if(!a)return!1;let i=a.activeElement;return(u=F.current)!=null&&u.contains(i)?!0:d.some(l=>{var v,m;return((v=a.getElementById(l.buttonId.current))==null?void 0:v.contains(i))||((m=a.getElementById(l.panelId.current))==null?void 0:m.contains(i))})}),f=y(a=>{for(let i of d)i.buttonId.current!==a&&i.close()}),o=K(()=>({registerPopover:t,unregisterPopover:g,isFocusWithinPopoverGroup:_,closeOthers:f}),[t,g,_,f]),O=K(()=>({}),[]),E=b,P={ref:B},A=le();return T.createElement(Ae,null,T.createElement(ge.Provider,{value:o},A({ourProps:P,theirProps:E,slot:O,defaultTag:yt,name:"Popover.Group"})))}let Rt=ee(dt),Ft=ee(Pt),Bt=ee(ke),_t=ee(ke),At=ee(bt),Ct=ee(gt),io=Object.assign(Rt,{Button:Ft,Backdrop:_t,Overlay:Bt,Panel:At,Group:Ct});export{io as Popover,_t as PopoverBackdrop,Ft as PopoverButton,Ct as PopoverGroup,Bt as PopoverOverlay,At as PopoverPanel};
