import nltk, re, pprint, json

#-- this is a dictionary of races. Each race has a collection of jockeys
races = [
    ("race1", "jockey2", "Walzing To Win: royal blue, lime hooped sleeves, quartered cap"),
    ("race2", "jockey4", "Brimavi Prince: royal blue, pink striped sleeves"),
    ("race1", "jockey7", "Rocmarr: orange, royal blue sleeves and orange cap"),
]


#
#-- Main function
#

def main():

    # default tagger and grammar
    tagger = get_tagger()
    grammar = r"""
       NP: {<NNP>*<:><JJ>*}     # jockey's name
       NN: {<JJ>*<(NN|NNS)>}  # sleeves color or pattern
    """
    
    for race, jockey, description in races:
        dic = evaluate(tagger, grammar, description)  
        print ("String to be evaluated...: " + description)
        print ("Results:")
        print (json.dumps(dic, indent=2))
        print ("-------------------------------------")



#
# -- this function returns a trained pos tagger
#
def get_tagger():
    default_tagger = nltk.data.load(nltk.tag._POS_TAGGER)
    model = {
            'orange':'JJ',
            'blue': 'JJ',
            'hooped': 'JJ',
            'quartered' : 'JJ',
            'Walzing': 'NNP',
            'To': 'NNP',
            'striped': 'JJ',
            'pink': 'JJ'
        }
    return nltk.tag.UnigramTagger(model=model, backoff=default_tagger)


#
#-- this function evaluates a string
#
def evaluate(tagger, grammar, string):
    p = nltk.RegexpParser(grammar)
    sentence = tagger.tag(nltk.word_tokenize(string))
    t = p.parse(sentence)
    dic = {}

    # chunk jockey's name
    for s in t.subtrees(lambda t: t.label() == "NP"):
        jj = ' '.join([a for (a,b) in s if b == 'JJ' ])
        nnp = ' '.join([a for (a,b) in s if b == 'NNP' ])

        dic['jockey'] = {
            'name': nnp,
            'paint': jj
        }
    
    for s in t.subtrees(lambda t: t.label() == "NN"):
        # extract noun phrase
        np = [a for (a,b) in s.leaves()]
        # extract cap adjectives
        if 'cap' in np:
            dic['cap'] = ' '.join([w for w in np if w != 'cap'])
        # extract sleeves adjectives
        if 'sleeves' in np:
            dic['sleeves'] = ' '.join([w for w in np if w != 'sleeves'])

    return dic


#
# -- script ntry point
#
if __name__ == "__main__":
    main()
