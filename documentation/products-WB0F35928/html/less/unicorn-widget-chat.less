/* Chat styles */
.chat-content {
	height: 500px;
	padding: 15px;

	.chat-messages {
	    background: none repeat scroll 0 0 #FBFBFB;
	    border: 1px solid #DDDDDD;
	    border-radius: 4px 4px 0 0;
	    height: 425px;
	    overflow: auto;
	    position: relative;
	    z-index: 10;

	    .chat-messages-inner {
			overflow-x: hidden;
			z-index: 0;

			p {
			    padding: 10px;
			    margin: 0;
				.translate(0,-50%);
				.opacity(0);
				.transition(all 0.3s ease-in-out);
				z-index: 0;

				&.al {
					.translate(-50%,0);
					.transition-duration(1s);	
				}

				&.show {
					.translate(0,0);
					.opacity(1);
				}

				&.al.show {
					.translate(0,0);
				}

				img {
				    display: inline-block;
				    float: left;
				    vertical-align: middle;
				    width: 28px;
				    height: 28px;
				    margin-top: 6px;
				    border-radius: 50%;
				    background-clip: content-box;
				    -moz-background-clip: content-box;
				    -webkit-backgrond-clip: content-box;
				    border: 1px solid #fff;
				    .box-shadow(0 1px 3px rgba(0,0,0,0.5));
				}

				&.offline span, .msg-block {
					background: none repeat scroll 0 0 #FFFFFF;
				    border: 1px solid #cccccc;
				    .box-shadow(1px 1px 0 1px rgba(0, 0, 0, 0.05));
				    display: block;
				    margin-left: 40px;
				    padding: 10px;
				    position: relative;
				}

				&.offline span {
					background: none repeat scroll  0 0 #FFF5F5;
				}
			}

			.time {
				color: #999999;
			    font-size: 11px;
			    font-style: italic;
			}

			.msg {
				display: block;
				margin-top: 10px;

				&:before {
					border-right: 7px solid rgba(0,0,0,0.1);
					border-top: 7px solid transparent;
					border-bottom: 7px solid transparent;
					content: "";
					display: inline-block;
					left: -7px;
					position: absolute;
					top: 11px;
				}

				&:after {
					border-right: 6px solid #ffffff;
					border-top: 6px solid transparent;
					border-bottom: 6px solid transparent;
					content: "";
					display: inline-block;
					left: -6px;
					position: absolute;
					top: 12px;
				}
			}
		}
	}

	.chat-message {
		border-radius: 0 0 4px 4px;
		padding: 10px;
		margin: -1px 0 0;
		box-shadow: none;

		.input-group {
			padding: 0;
		}

		input[type=text] {
			margin-bottom: 0 !important;
			width: 100%;
		}

		button {
			float: right;
			margin: 0;
		}
	}
}


.chat-users {
	padding: 0 0 30px;

	.contact-list {
	    line-height: 21px;
	    list-style: none outside none;
	    margin: 0;
	    padding: 0;
	    font-size: 10px;

		li {
		    border-color: #dddddd;
		    border-style: none none solid;
		    border-width: 0 0 1px;
		    padding: 1px;
		    position: relative;

			&:hover {
				background-color: #efefef;
			}

			a {
			    color: #666666;
			    display: block;
			    padding: 8px 5px;

			    img {
				    display: inline-block;
				    margin-right: 10px;
				    vertical-align: middle;
				    width: 28px;
				    height: 28px;
				    border-radius: 3px;
				}
			}

			&.online a {
				font-weight: bold;
			}

			&.new {
				background-color: #eaeaea;
			}

			&.offline {
				background-color: #EDE0E0;
			}
		}
	}

	.msg-count, .away {
		padding: 3px 5px;
	    position: absolute;
	    right: 10px;
	    top: 12px;
	}
	
	.away {
		font-size: 10px;
		top: 15px;
	}
}

.widget-chat.widget-full-screen {
	& .widget-content,	& .chat-content {
		height: 100% !important;
	}
	& .chat-content {
		padding: 0;
		position: relative;
	}
	& .chat-messages {
		height: 100%;
		top: 0;
		bottom: 0;
		border-width: 0 0 1px 0;
	}
	#chat-messages-inner {
		padding-bottom: 100px;
	}
	.chat-message {
		position: absolute;
		bottom: 35px;
		width: 100%;
		z-index: 2000;
	}
	
}
.white-backdrop {
	position:fixed;
	top: 0;
	left: 0;
	background-color: #ffffff;
	.square(100%);
	display: none;
	z-index: 9999;
}
