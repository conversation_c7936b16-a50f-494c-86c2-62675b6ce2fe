<?php

namespace DBLS\Bundle\JerseyColoursBundle\EventListener;

use Doctrine\Common\EventSubscriber;
use Doctrine\ORM\Event\LifecycleEventArgs;
use Doctrine\ORM\Event\OnFlushEventArgs;
use Doctrine\ORM\Event\PreFlushEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;

use DBLS\Bundle\JerseyColoursBundle\Entity\Colour;
use DBLS\Bundle\JerseyColoursBundle\Entity\Pattern;
use DBLS\Bundle\JerseyColoursBundle\Entity\ElementType;
use DBLS\Bundle\JerseyColoursBundle\Entity\Synonym;
use Symfony\Component\DependencyInjection\ContainerAware;

/**
* 
*/
class DictionarySubscriber extends ContainerAware implements EventSubscriber
{
    
    protected  $flag = false;
    
    /*
     * 
     */
    public function getSubscribedEvents() {
        
        return array(
            'preUpdate',
            'postPersist',
            'postRemove',
            'postUpdate',
        );
        
    }
    
    /*
     * return array with Entities using
     */
    protected function _getArrayEntity()
    {
        return $arrayEntities = array(
                'DBLS\Bundle\JerseyColoursBundle\Entity\Colour',
                'DBLS\Bundle\JerseyColoursBundle\Entity\Pattern',
                'DBLS\Bundle\JerseyColoursBundle\Entity\ElementType',
                'DBLS\Bundle\JerseyColoursBundle\Entity\Synonym'
            );
    }

    /*
     * Post Persist
     * @param $args
     */
    public function postPersist(LifecycleEventArgs $args)
    {
        
        $entity = $args->getEntity();
        
        if(in_array(get_class($entity),  $this->_getArrayEntity()) ){
        	$this->_saveDictionary($entity);
                $this->_regenerateSilks($entity);
        }
        
        $this->flag = true;
        
    }	
    
    /*
     * Pre Update
     * @param $args PreUpdateEventArgs
     */
    public function preUpdate(PreUpdateEventArgs $args)
    {
        if( $this->flag )
            return false;
        
        $em  = $args->getEntityManager();
        $uow = $em->getUnitOfWork();
        
        //Updates
        foreach ( $uow->getScheduledEntityUpdates() as $entity ){
            if(in_array(get_class($entity),  $this->_getArrayEntity()) )
                $this->_updateDictionary($entity,$args);            
        }
    }
    
    /*
     * Post Update
     * @param $args
     */
    public function postUpdate(LifecycleEventArgs $args)
    {
        
        if( $this->flag )
            return false;
        
        $entity = $args->getEntity();
        
        if(in_array(get_class($entity),  $this->_getArrayEntity()) ){
                $this->_regenerateSilks($entity);
        }
        
    }
    
    /*
     * Post Remove
     * @param $args LifecycleEventArgs
     */
    public function postRemove(LifecycleEventArgs $args)
    {
        
        $entity = $args->getEntity();
        
        if(in_array(get_class($entity),  $this->_getArrayEntity()) ){
                $this->_removeDictionary($entity);
                $this->_regenerateSilks($entity);
        }
    }

    /*
     * Save Dictionary
     * @param $entity Entity
     */
    protected function _saveDictionary($entity)
    {
        //Service
        $serviceDictionary = $this->container->get('services.dictionary'); 
        
        switch (true){
            
            case $entity instanceof Colour:
                $serviceDictionary->saveColour($entity);
                break;
            case $entity instanceof Pattern:
                $serviceDictionary->savePattern($entity);
                break;
            case $entity instanceof ElementType:
                $serviceDictionary->saveElement($entity);
                break;
            case $entity instanceof Synonym:
                $serviceDictionary->saveSynonym($entity);
                break;
            
        }
        
    }
    
    /*
     * @param $entity Entity
     * @param $args PreUpdateEventArgs
     */
    protected function _updateDictionary($entity,$args)
    {
        //Service
        $serviceDictionary = $this->container->get('services.dictionary'); 
        
        switch (true){
            
            case $entity instanceof Colour:

                 if ( !$args->hasChangedField('name') )
                    return true;
                
                $old =  $args->getOldValue('name');
                $new =  $args->getNewValue('name');
                
                $words = array(
                        'old' => $old,
                        'new' => $new, 
                        'id' => $entity->getId() 
                    );
                
                if( $old != $new)               
                    $serviceDictionary->updateColour($words);
                
                break;
            case $entity instanceof Pattern:

                if ( !$args->hasChangedField('description') )
                    return true;

                $old =  $args->getOldValue('description');
                $new =  $args->getNewValue('description');
                
                $words = array(
                    'old' => $old,
                    'new' => $new, 
                    'id' => $entity->getId() 
                );
                
                if( $old != $new )               
                    $serviceDictionary->updatePattern($words);
                
                break;
            case $entity instanceof ElementType:

                if ( !$args->hasChangedField('name') )
                    return true;
                
                $old =  $args->getOldValue('name');
                $new =  $args->getNewValue('name');
                
                $words = array(
                        'old' => $old,
                        'new' => $new, 
                        'id' => $entity->getId() 
                    );
                
                if( $old != $new)               
                    $serviceDictionary->updateElement($words);
                
                break;
            case $entity instanceof Synonym:

                if ( !$args->hasChangedField('term') )
                    return true;
                
                $old =  $args->getOldValue('term');
                $new =  $args->getNewValue('term');
                
                $words = array(
                        'old' => $old,
                        'new' => $new, 
                        'id' => $entity->getId() 
                    );
                
                if( $old != $new)               
                    $serviceDictionary->updateSynonym($words);
                
                break;
            
        }
    }
    
    /*
     * Remove Dictionary
     * @param $entity Entity
     */
    protected function _removeDictionary($entity)
    {
        //Service
        $serviceDictionary = $this->container->get('services.dictionary'); 
        
        switch (true){
            
            case $entity instanceof Colour:
                $serviceDictionary->removeColour($entity);
                break;
            case $entity instanceof Pattern:
                $serviceDictionary->removePattern($entity);
                break;
            case $entity instanceof ElementType:
                $serviceDictionary->removeElement($entity);
                break;
            case $entity instanceof Synonym:
                $serviceDictionary->removeSynonym($entity);
                break;
            
        }
        
    }
	
    /*
     * Regenerate Silks in Insert, Update and Remove
     * @param $entity
     */
    protected function _regenerateSilks($entity)
    {
        
        if( $entity instanceof Colour || $entity instanceof Pattern){
        
            $em = $this->container->get('doctrine')->getEntityManager();		

            //Find Silks not approvad
            /*$jerseys = $em->getRepository('JerseyColoursBundle:Jersey')->findBy(
                            array('status' => 0)
                        );
            
            foreach( $jerseys as $jersey ){
                
                $jersey->setParsed(0);                
                $em->persist($jersey);
                
            }
            
            
                $em->flush();*/
        
        }

    }
    
}