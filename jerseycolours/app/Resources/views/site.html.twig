{% extends 'base.html.twig' %}

{% block body %}
    <div id="wrapper">
	{% block header %}
	    <div id="header">
		<h1><a href="{{ path('homepage') }}">Unicorn Admin</a></h1>	
		<a id="menu-trigger" href="#"><i class="fa fa-bars"></i></a>	
	    </div>
	{% endblock header %}

	{% block usernav %}
	    <div id="user-nav">
		<ul class="btn-group">
		    <!--<li class="btn" ><a title="" href="{{ path('fos_user_profile_edit') }}"><i class="fa fa-user"></i> <span class="text">Profile</span></a></li>-->
                    <li class="btn" ><a title="" href="{{ path('fos_user_change_password') }}"><i class="fa fa-user"></i> <span class="text">Password</span></a></li>
		    <li class="btn"><a title="Logout" href="{{ path('fos_user_security_logout') }}"><i class="fa fa-share"></i> <span class="text">Logout</span></a></li>
		</ul>
	    </div>
	{% endblock usernav %}

	{% block sidebar %}
	    <div id="sidebar">
		{% block menu %}
		    {{ knp_menu_render('JerseyColoursBundle:Builder:mainMenu', {'template':'JerseyColoursBundle:Menu:menu.html.twig', 'currentClass': 'active open', 'ancestorClass': 'active open', 'branch_class': 'submenu'}) }}
		{% endblock %}
	    </div>
	{% endblock sidebar %}

	<div id="content">
	    	    
	    {% block page_header %}{% endblock page_header %}
	    
	    {% block breadcrumb %}{% endblock breadcrumb %}
	    
	    {% for flashMessage in app.session.flashbag.get('messages') %}
		<div class="row">
		    <div class="col-xs-12">
			<div class="alert bold alert-{{ flashMessage.level }}">
			    <button type="button" class="close" data-dismiss="alert"></button>
			    {{ flashMessage.msg }}
			</div>
		    </div>
		</div>
	    {% endfor %}
	    
	    {% block content %}{% endblock content %}
	</div>


	{% block footer %}
	    <div class="row">
		<div id="footer" class="col-xs-12">
		    2015 &copy; Jockey Colours
		</div>
	    </div>
	{% endblock footer %}
    </div>
{% endblock %}