/* Chart styles */
.chart, .pie, .bars {
	height: 300px;
	max-width: 100%;
}

// jQuery Flot Tooltips
#tooltip {
	position: absolute;
	display:none;
	border: none;
	padding: 3px 8px;
	border-radius: 3px;
	font-size: 10px;
	background-color: #222222;
	color: #ffffff;
	z-index: 25;
}
/***/
.site-stats {
	margin: 0;
	padding: 0;
	list-style: none;
}
.site-stats li {
    background-color: #F5F5F5;
    border: 1px solid #DDDDDD;
    cursor: pointer;
    margin: 0 0 10px;
    padding: 10px 20px 10px;
    position: relative;
}
.site-stats li:hover {
    background-color: #EDEDED;
}
.site-stats li .cc {
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-ms-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.site-stats li:hover .cc {
	-webkit-transform: translateX(10px);
	-moz-transform: translateX(10px);
	-ms-transform: translateX(10px);
	-o-transform: translateX(10px);
	transform: translateX(10px);
}
.site-stats li.divider {
	padding: 0;
	border-width: 1px 0 0;
	border-color: #DDDDDD;
	border-style: dashed;
}
.site-stats li i {
	vertical-align: baseline;
}
.site-stats li strong {
	font-weight: bold;
	font-size: 20px;
	margin-left: 5px;
}
.site-stats li small {
	margin-left: 5px;
	font-size: 12px;
	color: #888888;
	font-style: italic;
}