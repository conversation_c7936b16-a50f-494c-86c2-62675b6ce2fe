services:
    message.broker:
        class: <PERSON><PERSON>\Bundle\JerseyColoursBundle\Message\Broker
        calls:
            - [ setContainer,  [@service_container] ]

    services.jersey_generator:
        class: DBLS\Bundle\JerseyColoursBundle\Services\JerseyGeneratorService
        arguments: [@doctrine.orm.entity_manager]
        calls:
          - [ setContainer,[ @service_container ] ]

    services.jersey_parser:
        class: DBLS\Bundle\JerseyColoursBundle\Services\JerseyParserService
        arguments: [@doctrine.orm.entity_manager]
        calls:
          - [ setContainer,[ @service_container ] ]

    services.pattern.file_namer:
        class: DBLS\Bundle\JerseyColoursBundle\Services\PatternFileNamerService

    services.pattern.directory_namer:
        class: DBLS\Bundle\JerseyColoursBundle\Services\PatternDirectoryNamerService
        arguments: [@doctrine.orm.entity_manager]

    services.pattern.sanitization:
        class: DBLS\Bundle\JerseyColoursBundle\Services\PatternSanitizationService
        tags:
            - { name: doctrine.event_listener, event: prePersist }
            - { name: doctrine.event_listener, event: preUpdate }

    services.importer:
        class: DBLS\Bundle\JerseyColoursBundle\Services\ImporterService
        arguments: [@doctrine.orm.entity_manager]
        calls:
          - [ setJerseyParserService,[ @services.jersey_parser ] ]
          - [ setJerseyGeneratorService,[ @services.jersey_generator ] ]
          - [ setMeetingGeneratorService,[ @services.meeting_generator ] ]
          - [ setContainer,[ @service_container ] ]

    services.meeting_generator:
        class: DBLS\Bundle\JerseyColoursBundle\Services\MeetingGeneratorService
        arguments: [@doctrine.orm.entity_manager]
        calls:
          - [ setContainer,[ @service_container ] ]

    services.svg_generator:
        class: DBLS\Bundle\JerseyColoursBundle\Services\SvgGenerator
        arguments: [@doctrine.orm.entity_manager]
        calls:
          - [ setContainer,[ @service_container ] ]

    services.race.approval:
        class: DBLS\Bundle\JerseyColoursBundle\Services\RaceApprovalService
        arguments: [@doctrine.orm.entity_manager]
        calls:
          - [ setContainer,[ @service_container ] ]

    services.utils.dbUtils:
        class: DBLS\Bundle\JerseyColoursBundle\Services\DbUtilsService
        arguments: [@doctrine.orm.entity_manager]

    services.clone_jersey:
        class: DBLS\Bundle\JerseyColoursBundle\Services\JerseyCloneService
        arguments: [@doctrine.orm.entity_manager]
        calls:
          - [ setContainer,[ @service_container ] ]

    services.utils.remove_folders:
        class: DBLS\Bundle\JerseyColoursBundle\Services\RemoveEmptyFolderService

    services.synonym_options:
        class: DBLS\Bundle\JerseyColoursBundle\Services\SynonymOptions
        arguments: [@doctrine.orm.entity_manager]
        calls:
          - [ setContainer,[ @service_container ] ]

    services.dictionary:
        class: DBLS\Bundle\JerseyColoursBundle\Services\DictionaryService
        arguments: [@doctrine.orm.entity_manager]
        calls:
          - [ setContainer,[ @service_container ] ]

    services.jpeg.generator:
        class: DBLS\Bundle\JerseyColoursBundle\Services\JpegGeneratorService
        arguments: [@doctrine.orm.entity_manager]
        calls:
          - [ setContainer,[ @service_container ] ]
          
    dictionary.subscriber:
        class: DBLS\Bundle\JerseyColoursBundle\EventListener\DictionarySubscriber
        calls:
          - [ setContainer,[ @service_container ] ]
        tags:
            - { name: doctrine.event_subscriber }

    kernel.listener.command_dispatch:
        class: DBLS\Bundle\JerseyColoursBundle\EventListener\ConsoleExceptionListener
        arguments:
            logger: "@logger"
        tags:
            - { name: kernel.event_listener, event: console.exception }
            - { name: kernel.event_listener, event: console.terminate }

    svg.utils:
        class: DBLS\Bundle\JerseyColoursBundle\Services\SvgUtilsService

    slug.update:
        class: DBLS\Bundle\JerseyColoursBundle\EventListener\SluggableUpdateListener
        arguments: [@sluggable.slugger]
        tags:
            - { name: doctrine.event_listener, event: preUpdate }