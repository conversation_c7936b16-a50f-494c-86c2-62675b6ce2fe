<?php

namespace DBLS\Bundle\JerseyColoursBundle\Validator\Constraints;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\File\File;

/**
 * Validate file extension
 *
 * <AUTHOR>
 */
class FileExtensionValidator extends ConstraintValidator {

    public function validate($value, Constraint $constraint) {
        $ext = null;

        if ($value instanceof File) {
            $ext = strtolower($value->getExtension());
        }
        
        //NOTE: here the order is important because UploadedFile is also a File, so it must be the last test.
        if ($value instanceof UploadedFile) {
            $ext = strtolower($value->getClientOriginalExtension());
        }

        if ($ext === null && $value !== null) {
            throw new \Exception("'FileExtension' validation applied to an invalid field type.");
        }

        if ($ext !== null && !in_array($ext, $constraint->allowExtensions)) {
            $this->context->buildViolation($constraint->errorMessage)
                    ->setParameter('{{user_extension}}', $ext)
                    ->setParameter('{{allowed_extensions}}', implode(",", $constraint->allowExtensions))
                    ->addViolation();
        }
    }

}
