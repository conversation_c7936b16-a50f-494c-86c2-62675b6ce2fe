<?php

namespace DBLS\Bundle\JerseyColoursBundle\Entity;

use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\Query\Expr;
use Doctrine\ORM\NoResultException;

/**
 * PatternRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class PatternRepository extends EntityRepository
{
    /**
     * Find pattern by description insensitive
     * 
     * @param string $patternDescription
     * @param string $elementName
     * @return ElementType
     */
    public function findByDescriptionAndElement($patternDescription, $elementName) {
        $qb = $this->createQueryBuilder('t');
        $qb
            ->innerJoin('JerseyColoursBundle:ElementType', 'u', Expr\Join::WITH, "t.element_type = u.id")
            ->where($qb->expr()->eq($qb->expr()->lower($qb->expr()->trim('t.description')), ":patternDescription"))
            ->andWhere($qb->expr()->eq($qb->expr()->lower($qb->expr()->trim('u.name')), ":elementName"))
            ->setParameter('patternDescription', trim(strtolower($patternDescription)))
            ->setParameter('elementName', trim(strtolower($elementName)))
        ;

        $q = $qb->getQuery();
        
        try {
            return $q->getSingleResult();
        } catch (NoResultException $ex) {
            return null;
        }
        
    }

    /**
     * 
     * @param  string $name
     * @return array
     */
    public function listOptionsCombo($name)
    {
        $qb = $this->createQueryBuilder('p');

        $patterns = $qb->where($qb->expr()->like(
                        $qb->expr()->lower($qb->expr()->trim('p.description')), ":description"
                    ))
                    ->setParameter('description', '%' . strtolower(trim($name)) . '%')
                    ->orderBy('p.description','ASC')
                    ->getQuery()
                    ->getResult();

        $dataPatterns = array();
        foreach ( $patterns as $pattern ) {
            $dataPatterns[] = array(
                'id' => $pattern->getId(),
                'text' => (string)$pattern
            );
        }

        return $dataPatterns;
    }
    
}
