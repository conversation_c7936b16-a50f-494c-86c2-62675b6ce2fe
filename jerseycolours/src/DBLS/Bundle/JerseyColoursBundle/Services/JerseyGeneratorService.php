<?php

namespace DBLS\Bundle\JerseyColoursBundle\Services;

use Doctrine\ORM\EntityManager;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Util\Debug;
use DBLS\Bundle\JerseyColoursBundle\Entity\Jersey;
use DBLS\Bundle\JerseyColoursBundle\Entity\Error;
use Symfony\Component\DependencyInjection\ContainerAware;
use Symfony\Component\Filesystem\Filesystem;

/**
 * The purpose of this class is to generate Jerseys.
 *
 * <AUTHOR>
 */
class JerseyGeneratorService extends ContainerAware
{

    /**
     * Holds an instance of database connection
     * @var EntityManager
     */
    private $em = null;

    public function __construct(EntityManager $em)
    {
        $this->em = $em;
    }

    /**
     * This method creates a new Jersey.
     */
    public function createJersey(Jersey $jersey, $solveErrors = false, $generateDescription = true)
    {
        // 1. Persist all errors associated with this jersey
        foreach ($jersey->getErrors() as $error) {

            if ( $solveErrors )
                $error->setSolved(1);

            $error->setJersey($jersey);
            $this->em->persist($error);
        }

        // 2. Persist all jersey's elements
        foreach ($jersey->getElements() as $element) {
            $element->setJersey($jersey);
            $this->em->persist($element);

            // 2.1 Persist all patterns related to this element
            foreach ($element->getJerseyPatterns() as $jerseyElementPattern) {

                if ( null == $jerseyElementPattern->getPattern() ) {
                    // delete related colours
                    foreach ($jerseyElementPattern->getPatternColours() as $patternColour) {
                        $jerseyElementPattern->getPatternColours()->removeElement($patternColour);
                        $this->em->remove($patternColour);
                    }

                    $element->getJerseyPatterns()->removeElement($jerseyElementPattern);
                    $this->em->remove($jerseyElementPattern);
                    continue;
                }

                $jerseyElementPattern->setJerseyElement($element);

                $this->em->persist($jerseyElementPattern);

                // 2.1.1 Persist all colours related to this element pattern
                foreach ($jerseyElementPattern->getPatternColours() as $patternColour)
                {
                    if ( null == $patternColour->getColour() ) {
                        $jerseyElementPattern->getPatternColours()->removeElement($patternColour);
                        $this->em->remove($patternColour);
                        continue;
                    }

                    $patternColour->setJerseyElementPattern($jerseyElementPattern);
                    $this->em->persist($patternColour);
                }
            }
        }

        $dbUtils = $this->container->get("services.utils.dbUtils");

        if ( !$dbUtils->isNewEntity($jersey) && $generateDescription ) {
            $jersey->setDescription($jersey->generateString());
        }

        $this->em->persist($jersey);
        $this->em->flush();

        return $this->container->get("services.svg_generator")->generate($jersey);
        //return $this->generateJerse($jersey);
    }

    /**
    * @param DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $jersey
    * @return string
    */
    public function jerseyToJson(Jersey $jersey)
    {
        $jerseySerialized = array(
            'name'      => $jersey->getName(),
            'elements'  => array()
        );

        foreach ( $jersey->getElements() as $jerseyElement ) {

            $elementEntity = $jerseyElement->getElementType();
            $name = trim(strtolower(preg_replace('/ */i', '', $elementEntity->getName() )));

            $element = array(
                'name'      => $name,
                'colour'    => $jerseyElement->getColour() ? $jerseyElement->getColour()->getHexadecimal() : '',
                'patterns'  => array()
            );

            foreach ($jerseyElement->getJerseyPatterns() as $jerseyElementPattern) {

                $patternEntity = $jerseyElementPattern->getPattern();

                $pattern = array(
                    'slug'      => $patternEntity->getSlug(),
                    'name'      => $patternEntity->getDescription(),
                    'colours'   => array()
                );

                foreach( $jerseyElementPattern->getPatternColours() as $colour ) {
                    $pattern['colours'][] = $colour->getColour()->getHexadecimal();
                }

                $element['patterns'][] = $pattern;
            }

            $jerseySerialized['elements'][] = $element;
        }

        return $jerseySerialized;
    }

    /**
     * @return string
     */
    public function getJerseysDir()
    {
        $fs = new Filesystem();

        $rootDir = $this->container->get('kernel')->getRootDir();
        $webPath = realpath($rootDir . '/../web/');
        $jerseysDir =  $webPath . '/images/silks';

        if ( !$fs->exists( $jerseysDir ) )
            $fs->mkdir( $jerseysDir );

        return $jerseysDir;
    }

    /**
    * @param DBLS\Bundle\JerseyColoursBundle\Entity\Jersey $jersey
    * @return string
    */
    public function generateJerse(Jersey $jersey)
    {
        $jerseyJson = $this->jerseyToJson($jersey);

        $rootDir = $this->container->get('kernel')->getRootDir();
        $webPath = realpath($rootDir . '/../web/');

        $fs = new Filesystem();
        $jerseysDir = $this->getJerseysDir();

        $phantom = realpath($rootDir . '/../bin/phantomjs');

        $jerseyFolder = sprintf( '%s/%s', $jerseysDir, $jersey->getFolderPath() );

        $jerseyFrontFile = sprintf( '%s/%s', $jerseyFolder, 'front.svg' );
        $jerseySideFile = sprintf( '%s/%s', $jerseyFolder, 'side.svg' );

        if ( !$fs->exists( $jerseyFolder ) )
            $fs->mkdir( $jerseyFolder );

        $url = $this->container->getParameter('svg_server');

        try {

            $client = new \Guzzle\Service\Client();
            $request = $client->post($url, array('Content-type' => 'application/json'), array());

            $request->setBody(json_encode($jerseyJson));
            $response = $request->send();
            file_put_contents($jerseyFrontFile, $response->getBody());

            $jerseyJson['view'] = 'side';
            $request->setBody(json_encode($jerseyJson));
            $response = $request->send();
            file_put_contents($jerseySideFile, $response->getBody());

            return true;

        } catch ( \Exception $e ) {

            $error = new Error();
            $error->setType(Error::GENERATE_SVG)
                  ->setMessage('Error communcating with the server when generating the SVG.')
                  ->setJersey($jersey);

            $this->em->persist($error);
            $this->em->flush();

            return false;
        }

        /*
        $rasterize = $webPath . '/generator/rasterize.js';

        $router = $this->container->get('router');

        $urlFront = $router->generate('jersey_generator', array( 'id' => $jersey->getId() ), true );
        $urlSide = $router->generate('jersey_generator', array( 'id' => $jersey->getId(), 'view' => 'side' ), true );

        $commandFront = sprintf("%s --web-security=false %s %s > %s &", $phantom, $rasterize, $urlFront, $jerseyFrontFile );
        $commandSide = sprintf("%s --web-security=false %s %s > %s &", $phantom, $rasterize, $urlSide, $jerseySideFile );

        Proc_Close (Proc_Open ($commandFront, Array (), $foo));
        Proc_Close (Proc_Open ($commandSide, Array (), $foo));
        */
    }
}
