<?php

namespace DBLS\Bundle\JerseyColoursBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;

use Sensio\Bundle\FrameworkExtraBundle\Configuration\Method;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Template;

use DBLS\Bundle\JerseyColoursBundle\Entity\Pattern;
use DBLS\Bundle\JerseyColoursBundle\Form\PatternType;

/**
 * Pattern controller.
 *
 * @Route("/pattern")
 */
class PatternController extends Controller
{

    /**
     * Lists all Pattern entities.
     *
     * @Route("/", name="pattern", options={"expose"=true})
     * @Method("GET")
     * @Template("JerseyColoursBundle:Pattern:index.html.twig")
     */
    public function indexAction()
    {
        $em = $this->getDoctrine()->getManager();

        $entities = $em->getRepository('JerseyColoursBundle:Pattern')->findAll();

        return array(
            'entities' => $entities,
        );
    }

    /**
     * Displays a form to create a new Pattern entity.
     *
     * @Route("/new", name="pattern_new")
     * @Method("GET")
     * @Template("JerseyColoursBundle:Pattern:form.html.twig")
     */
    public function newAction()
    {
        $entity = new Pattern();
        $form   = $this->createPatternForm($entity);

        return array(
            'entity' => $entity,
            'form'   => $form->createView(),
        );
    }

    /**
     * Displays a form to edit an existing Pattern entity.
     *
     * @Route("/edit/{id}", name="pattern_edit")
     * @Method("GET")
     * @Template("JerseyColoursBundle:Pattern:form.html.twig")
     */
    public function editAction($id)
    {
        $em = $this->getDoctrine()->getManager();

        $entity = $em->getRepository('JerseyColoursBundle:Pattern')->find($id);

        if (!$entity) {
            throw $this->createNotFoundException('Unable to find Pattern entity.');
        }

        $editForm = $this->createPatternForm($entity, $id, true);

        return array(
            'entity'    => $entity,
            'form'	=> $editForm->createView()
        );
    }

    /**
     * Creates a new Pattern entity.
     *
     * @Route("/save/{id}", name="pattern_save", defaults={"id": null})
     * @Method("POST")
     * @Template("JerseyColoursBundle:Pattern:form.html.twig")
     */
    public function saveAction(Request $request, $id)
    {
        $isUpdate = false;
    	if ( !$id ) {
    	    $entity = new Pattern();
    	} else {
    	    $isUpdate = true;

    	    $em = $this->getDoctrine()->getManager();
    	    $entity = $em->getRepository('JerseyColoursBundle:Pattern')->find($id);

    	    if (!$entity) {
    		  throw $this->createNotFoundException('Unable to find Pattern entity.');
    	    }
    	}

        $form = $this->createPatternForm($entity, $id, $isUpdate);
        $form->handleRequest($request);

    	$messageBroker = $this->get('message.broker');
    	$messageDefaults = $this->container->getParameter('messages');

        if ( $form->isValid() ) {

            $em = $this->getDoctrine()->getManager();
            $em->persist($entity);
            $em->flush();

	       $form = $this->createPatternForm($entity, $entity->getId(), true);

	       $messageBroker->addSuccess($messageDefaults['success']);

        } else {

	       $messageBroker->addError($messageDefaults['error']);
    	}

    	return array( 'form' => $form->createView() );
    }

    /**
     * Creates a form to save a Pattern entity.
     *
     * @param Pattern $entity The entity
     *
     * @return \Symfony\Component\Form\Form The form
     */
    private function createPatternForm(Pattern $entity, $id = null, $isUpdate = false)
    {
        $form = $this->createForm(new PatternType($isUpdate), $entity, array(
            'action' => $this->generateUrl('pattern_save', array( 'id' => $id )),
            'method' => 'POST',
        ));

        return $form;
    }

    /**
     * Deletes a Pattern entity.
     *
     * @Route("/delete/{id}", name="pattern_delete", options={"expose"=true})
     * @Method("POST")
     */
    public function deleteAction(Request $request, $id)
    {
	$em = $this->getDoctrine()->getManager();
	$entity = $em->getRepository('JerseyColoursBundle:Pattern')->find($id);

	if ( !$entity ) {
	    throw $this->createNotFoundException('Unable to find Pattern entity.');
	}

	$em->remove($entity);
	$em->flush();

        $serializedEntity = $this->container->get('jms_serializer')->serialize($entity, 'json');
	return new JsonResponse($serializedEntity);
    }

    /**
     * Debug parser
     *
     * @Route("/parse", name="jersey_parse", options={"expose"=true})
     * @Method("GET")
     */
    public function parseIndex() {

        error_log('logando erro...');
        $parserService = $this->get("services.jersey_parser");
        //$jersey = $parserService->createJerseyFromString("Royal Blue, White Crossed Sashes");
        //$jersey = $parserService->createJerseyFromString("yellow, red stripe sleeves");
        $jersey = $parserService->createJerseyFromString("aqua, white bell, red piping, red and white hooped sleeves, aqua cap with pom pom");

        $jerseyGenerator = $this->get('services.jersey_generator');
        echo "-- END OF REQUEST --";die();
        
    }

}
