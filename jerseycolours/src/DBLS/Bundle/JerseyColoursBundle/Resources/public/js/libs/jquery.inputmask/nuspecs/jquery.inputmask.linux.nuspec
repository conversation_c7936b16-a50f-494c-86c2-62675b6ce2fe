<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2010/07/nuspec.xsd">
    <metadata>
        <id>jQuery.InputMask</id>
        <version>0.0.0</version>
        <title>jQuery Input Mask</title>
        <authors><PERSON></authors>
        <owners><PERSON></owners>
        <licenseUrl>http://opensource.org/licenses/mit-license.php</licenseUrl>
        <projectUrl>https://github.com/RobinHerbots/jquery.inputmask</projectUrl>
        <requireLicenseAcceptance>true</requireLicenseAcceptance>
		<summary>jquery.inputmask is a jquery plugin which create an input mask.</summary>
        <description>jquery.inputmask is a jquery plugin which create an input mask.

An inputmask helps the user with the input by ensuring a predefined format. This can be useful for dates, numerics, phone numbers, ...

Highlights:
- easy to use
- optional parts anywere in the mask
- possibility to define aliases which hide complexity
- date / datetime masks
- numeric masks
- lots of callbacks
- non-greedy masks
- many features can be enabled/disabled/configured by options
- supports readonly/disabled/dir="rtl" attributes
- support data-inputmask attribute(s)  
- alternator-mask support
- regex-mask support
- dynamic-mask support
- preprocessing-mask support
- value formatting / validating without input element
</description>
        <tags>jQuery, plugins, input, form, inputmask, mask</tags>
    </metadata>
    <files>
        <file src="dist/inputmask/jquery.inputmask.date.extensions.js" target="content/Scripts/jquery.inputmask/jquery.inputmask.date.extensions.js" />
        <file src="dist/inputmask/jquery.inputmask.extensions.js" target="content/Scripts/jquery.inputmask/jquery.inputmask.extensions.js" />
        <file src="dist/inputmask/jquery.inputmask.numeric.extensions.js" target="content/Scripts/jquery.inputmask/jquery.inputmask.numeric.extensions.js" />
		    <file src="dist/inputmask/jquery.inputmask.regex.extensions.js" target="content/Scripts/jquery.inputmask/jquery.inputmask.regex.extensions.js" />
		    <file src="dist/inputmask/jquery.inputmask.phone.extensions.js" target="content/Scripts/jquery.inputmask/jquery.inputmask.phone.extensions.js" />
        <file src="dist/inputmask/jquery.inputmask.js" target="content/Scripts/jquery.inputmask/jquery.inputmask.js" />
    </files>
</package>
